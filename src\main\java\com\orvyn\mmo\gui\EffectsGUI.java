package com.orvyn.mmo.gui;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import com.orvyn.mmo.models.ActiveEffect;
import com.orvyn.mmo.models.Effect;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class EffectsGUI implements Listener {

    private final OrvynMMOPlugin plugin;

    public EffectsGUI(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    public void openEffectsGUI(Player player) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        if (data == null) {
            player.sendMessage(ChatColor.RED + "Unable to load your data!");
            return;
        }

        Inventory gui = Bukkit.createInventory(null, 54, ChatColor.DARK_PURPLE + "Active Effects");

        // Fill with glass panes
        ItemStack glassPane = new ItemStack(Material.BLACK_STAINED_GLASS_PANE);
        ItemMeta glassMeta = glassPane.getItemMeta();
        glassMeta.setDisplayName(" ");
        glassPane.setItemMeta(glassMeta);

        for (int i = 0; i < 54; i++) {
            gui.setItem(i, glassPane);
        }

        // Add active effects
        Map<String, ActiveEffect> activeEffects = data.getActiveEffects();
        int slot = 10; // Start from slot 10 for better layout

        for (Map.Entry<String, ActiveEffect> entry : activeEffects.entrySet()) {
            if (slot >= 44) break; // Don't overflow the GUI

            String effectId = entry.getKey();
            ActiveEffect activeEffect = entry.getValue();
            Effect effect = plugin.getEffectManager().getEffect(effectId);

            if (effect != null) {
                ItemStack effectItem = createEffectItem(effect, activeEffect);
                gui.setItem(slot, effectItem);
                
                slot++;
                if (slot % 9 == 8) { // Skip the right border
                    slot += 2;
                }
            }
        }

        // Add clear effects button
        ItemStack clearButton = new ItemStack(Material.BARRIER);
        ItemMeta clearMeta = clearButton.getItemMeta();
        clearMeta.setDisplayName(ChatColor.RED + "Clear All Effects");
        List<String> clearLore = new ArrayList<>();
        clearLore.add(ChatColor.GRAY + "Click to remove all active effects");
        clearLore.add(ChatColor.RED + "Warning: This cannot be undone!");
        clearMeta.setLore(clearLore);
        clearButton.setItemMeta(clearMeta);
        gui.setItem(49, clearButton);

        player.openInventory(gui);
    }

    private ItemStack createEffectItem(Effect effect, ActiveEffect activeEffect) {
        Material material = getEffectMaterial(effect);
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();

        // Set display name with color
        meta.setDisplayName(effect.getColor() + effect.getName());

        // Create lore
        List<String> lore = new ArrayList<>();
        lore.add(ChatColor.GRAY + effect.getDescription());
        lore.add("");
        
        // Category
        String categoryColor = getCategoryColor(effect.getCategory());
        lore.add(categoryColor + "Category: " + effect.getCategory().name());
        
        // Duration
        int remainingSeconds = activeEffect.getRemainingTicks() / 20;
        int minutes = remainingSeconds / 60;
        int seconds = remainingSeconds % 60;
        String timeText = minutes > 0 ? minutes + "m " + seconds + "s" : seconds + "s";
        lore.add(ChatColor.YELLOW + "Time Remaining: " + timeText);
        
        // Stacks
        if (effect.isStackable() && activeEffect.getStacks() > 1) {
            lore.add(ChatColor.AQUA + "Stacks: " + activeEffect.getStacks() + "/" + effect.getMaxStacks());
        }
        
        // Effect details
        lore.add("");
        switch (effect.getType()) {
            case DAMAGE_OVER_TIME:
                lore.add(ChatColor.RED + "Damage: " + (effect.getDamagePerTick() * activeEffect.getStacks()) + " per " + (effect.getTickInterval() / 20) + "s");
                break;
            case HEAL_OVER_TIME:
                lore.add(ChatColor.GREEN + "Healing: " + (effect.getHealPerTick() * activeEffect.getStacks()) + " per " + (effect.getTickInterval() / 20) + "s");
                break;
            case MANA_DRAIN:
                lore.add(ChatColor.DARK_PURPLE + "Mana Drain: " + (effect.getDamagePerTick() * activeEffect.getStacks()) + " per " + (effect.getTickInterval() / 20) + "s");
                break;
            case MANA_REGENERATION:
                lore.add(ChatColor.BLUE + "Mana Regen: " + (effect.getHealPerTick() * activeEffect.getStacks()) + " per " + (effect.getTickInterval() / 20) + "s");
                break;
            case ATTRIBUTE_MODIFIER:
                if (effect.getAttribute() != null) {
                    String modType = effect.getModifierType() == Effect.ModifierType.MULTIPLY ? "%" : "";
                    double value = effect.getModifierValue();
                    if (effect.getModifierType() == Effect.ModifierType.MULTIPLY) {
                        value *= 100;
                    }
                    String sign = value > 0 ? "+" : "";
                    lore.add(ChatColor.GOLD + effect.getAttribute().getDisplayName() + ": " + sign + value + modType);
                }
                break;
        }

        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }

    private Material getEffectMaterial(Effect effect) {
        switch (effect.getCategory()) {
            case BUFF:
                return Material.LIME_DYE;
            case DEBUFF:
                return Material.RED_DYE;
            case NEUTRAL:
            default:
                return Material.GRAY_DYE;
        }
    }

    private String getCategoryColor(Effect.EffectCategory category) {
        switch (category) {
            case BUFF:
                return ChatColor.GREEN.toString();
            case DEBUFF:
                return ChatColor.RED.toString();
            case NEUTRAL:
            default:
                return ChatColor.GRAY.toString();
        }
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!event.getView().getTitle().equals(ChatColor.DARK_PURPLE + "Active Effects")) {
            return;
        }

        event.setCancelled(true);

        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();
        ItemStack clickedItem = event.getCurrentItem();

        if (clickedItem == null || clickedItem.getType() == Material.AIR) {
            return;
        }

        // Handle clear all effects button
        if (clickedItem.getType() == Material.BARRIER) {
            plugin.getEffectManager().clearAllEffects(player);
            player.closeInventory();
            player.sendMessage(ChatColor.GREEN + "All effects cleared!");
        }
    }
}
