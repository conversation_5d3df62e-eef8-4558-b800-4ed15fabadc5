package com.orvyn.mmo.data;

import com.orvyn.mmo.enums.Attribute;
import com.orvyn.mmo.models.ActiveEffect;

import java.io.Serializable;
import java.util.*;

public class PlayerData implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private final UUID playerId;
    private String playerClass;
    private int classLevel;
    private long classExp;
    
    // Attributes (base + equipment + buffs)
    private final Map<Attribute, Integer> baseAttributes = new HashMap<>();
    private final Map<Attribute, Integer> equipmentAttributes = new HashMap<>();
    private final Map<Attribute, Integer> buffAttributes = new HashMap<>();

    // Resources
    private double currentHP;
    private double currentMana;
    private double maxHP;
    private double maxMana;

    // Professions
    private final Map<String, Integer> professionLevels = new HashMap<>();
    private final Map<String, Long> professionExp = new HashMap<>();

    // Skills
    private final Map<String, Long> skillCooldowns = new HashMap<>();
    private final Set<String> unlockedSkills = new HashSet<>();
    private final Map<Integer, String> skillBar = new HashMap<>(); // Slot (1-5) -> Skill ID
    
    // Waypoints
    private final Set<String> discoveredWaypoints = new HashSet<>();
    
    // Quests
    private final Set<String> completedQuests = new HashSet<>();
    private final Map<String, QuestProgress> activeQuests = new HashMap<>();

    // Effects
    private final Map<String, ActiveEffect> activeEffects = new HashMap<>();

    // Party
    private UUID partyId;
    
    // Misc
    private boolean dirty = false;
    private long lastSave = System.currentTimeMillis();
    
    public PlayerData(UUID playerId) {
        this.playerId = playerId;
        this.playerClass = "warrior"; // Default class
        this.classLevel = 1;
        this.classExp = 0;
        
        // Initialize base attributes
        initializeBaseAttributes();
        
        // Initialize resources
        recalculateResources();

        // Give starter skills based on class
        giveStarterSkills();
    }
    
    private void initializeBaseAttributes() {
        // Default warrior stats
        baseAttributes.put(Attribute.HP, 20);
        baseAttributes.put(Attribute.MANA, 50);
        baseAttributes.put(Attribute.STR, 5);
        baseAttributes.put(Attribute.AGI, 5);
        baseAttributes.put(Attribute.INT, 5);
        baseAttributes.put(Attribute.CRIT, 0);
        baseAttributes.put(Attribute.HASTE, 0);
    }
    
    public void recalculateResources() {
        // Calculate max HP and Mana based on total attributes
        int totalHP = getTotalAttribute(Attribute.HP);
        int totalMana = getTotalAttribute(Attribute.MANA);
        
        double oldMaxHP = maxHP;
        double oldMaxMana = maxMana;
        
        maxHP = totalHP;
        maxMana = totalMana;
        
        // Adjust current values proportionally if max changed
        if (oldMaxHP > 0) {
            currentHP = Math.min(currentHP * (maxHP / oldMaxHP), maxHP);
        } else {
            currentHP = maxHP;
        }
        
        if (oldMaxMana > 0) {
            currentMana = Math.min(currentMana * (maxMana / oldMaxMana), maxMana);
        } else {
            currentMana = maxMana;
        }
        
        markDirty();
    }
    
    public int getTotalAttribute(Attribute attribute) {
        return baseAttributes.getOrDefault(attribute, 0) +
               equipmentAttributes.getOrDefault(attribute, 0) +
               buffAttributes.getOrDefault(attribute, 0);
    }
    
    public void addExp(long amount) {
        classExp += amount;
        markDirty();
        // Note: Level-up checking is now handled by ExperienceManager
    }
    
    public void addProfessionExp(String profession, long amount) {
        long currentExp = professionExp.getOrDefault(profession, 0L);
        professionExp.put(profession, currentExp + amount);

        // Check profession level up
        checkProfessionLevelUp(profession);
        markDirty();
    }

    private void checkProfessionLevelUp(String profession) {
        int currentLevel = professionLevels.getOrDefault(profession, 0);
        long currentExp = professionExp.getOrDefault(profession, 0L);

        // Keep checking for level ups until we can't level up anymore
        while (true) {
            long requiredExp = getProfessionRequiredExp(currentLevel + 1);
            if (currentExp >= requiredExp) {
                currentLevel++;
                professionLevels.put(profession, currentLevel);
            } else {
                break;
            }
        }
    }
    
    private long getProfessionRequiredExp(int level) {
        // Simple progression: level * 50
        return level * 50L;
    }

    public int getProfessionLevel(String profession) {
        return professionLevels.getOrDefault(profession, 0);
    }

    public void setProfessionLevel(String profession, int level) {
        professionLevels.put(profession, level);
        markDirty();
    }

    public long getProfessionExp(String profession) {
        return professionExp.getOrDefault(profession, 0L);
    }
    
    public boolean isSkillOnCooldown(String skillId) {
        long cooldownEnd = skillCooldowns.getOrDefault(skillId, 0L);
        return System.currentTimeMillis() < cooldownEnd;
    }

    public void setSkillCooldown(String skillId, long durationMs) {
        skillCooldowns.put(skillId, System.currentTimeMillis() + durationMs);
        markDirty();
    }

    public long getSkillCooldownRemaining(String skillId) {
        long cooldownEnd = skillCooldowns.getOrDefault(skillId, 0L);
        return Math.max(0, cooldownEnd - System.currentTimeMillis());
    }
    
    public boolean canAffordManaCost(int cost) {
        return currentMana >= cost;
    }
    
    public void consumeMana(int amount) {
        currentMana = Math.max(0, currentMana - amount);
        markDirty();
    }
    
    public void heal(double amount) {
        currentHP = Math.min(maxHP, currentHP + amount);
        markDirty();
    }
    
    public void restoreMana(double amount) {
        currentMana = Math.min(maxMana, currentMana + amount);
        markDirty();
    }
    
    public void takeDamage(double amount) {
        currentHP = Math.max(0, currentHP - amount);
        markDirty();
    }
    
    public boolean isDead() {
        return currentHP <= 0;
    }
    
    public void markDirty() {
        this.dirty = true;
    }
    
    public boolean isDirty() {
        return dirty;
    }
    
    public void markClean() {
        this.dirty = false;
        this.lastSave = System.currentTimeMillis();
    }
    
    // Getters and setters
    public UUID getPlayerId() { return playerId; }
    public String getPlayerClass() { return playerClass; }
    public void setPlayerClass(String playerClass) { this.playerClass = playerClass; markDirty(); }
    public int getClassLevel() { return classLevel; }
    public void setClassLevel(int classLevel) { this.classLevel = classLevel; markDirty(); }
    public long getClassExp() { return classExp; }
    public void setClassExp(long classExp) { this.classExp = classExp; markDirty(); }

    public void addClassExp(long amount) {
        this.classExp += amount;
        markDirty();
        // Note: Level-up checking is now handled by ExperienceManager
    }
    
    public double getCurrentHP() { return currentHP; }
    public void setCurrentHP(double currentHP) { this.currentHP = currentHP; markDirty(); }
    public double getCurrentMana() { return currentMana; }
    public void setCurrentMana(double currentMana) { this.currentMana = currentMana; markDirty(); }
    public double getMaxHP() { return maxHP; }
    public double getMaxMana() { return maxMana; }
    
    public Map<Attribute, Integer> getBaseAttributes() { return baseAttributes; }
    public Map<Attribute, Integer> getEquipmentAttributes() { return equipmentAttributes; }
    public Map<Attribute, Integer> getBuffAttributes() { return buffAttributes; }

    public Map<String, Integer> getProfessionLevels() { return professionLevels; }
    public Map<String, Long> getProfessionExp() { return professionExp; }
    
    public Set<String> getUnlockedSkills() { return unlockedSkills; }
    public Set<String> getDiscoveredWaypoints() { return discoveredWaypoints; }
    public Set<String> getCompletedQuests() { return completedQuests; }
    public Map<String, QuestProgress> getActiveQuests() { return activeQuests; }
    public Map<String, ActiveEffect> getActiveEffects() { return activeEffects; }

    public UUID getPartyId() { return partyId; }
    public void setPartyId(UUID partyId) { this.partyId = partyId; markDirty(); }

    public long getLastSave() { return lastSave; }

    private void giveStarterSkills() {
        // Give level 1 skills based on class (matching classes.yml)
        switch (playerClass.toLowerCase()) {
            case "warrior":
                unlockedSkills.add("arc_slash");
                break;
            case "mage":
                unlockedSkills.add("fire_bolt");
                break;
            case "archer":
                unlockedSkills.add("power_shot");
                break;
            case "rogue":
                unlockedSkills.add("stealth");
                break;
            case "paladin":
                unlockedSkills.add("holy_strike");
                break;
            case "druid":
                unlockedSkills.add("nature_bolt");
                break;
            default:
                // Default skills for unknown classes
                unlockedSkills.add("arc_slash");
                break;
        }
    }

    // Skill Bar Methods
    public String getSkillBarSlot(int slot) {
        return skillBar.get(slot);
    }

    public void setSkillBarSlot(int slot, String skillId) {
        if (slot >= 1 && slot <= 5) {
            if (skillId == null || skillId.isEmpty()) {
                skillBar.remove(slot);
            } else {
                skillBar.put(slot, skillId);
            }
            markDirty();
        }
    }

    public Map<Integer, String> getSkillBar() {
        return new HashMap<>(skillBar);
    }

    public void clearSkillBar() {
        skillBar.clear();
        markDirty();
    }
}
