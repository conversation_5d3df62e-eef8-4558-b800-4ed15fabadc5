package com.orvyn.mmo.managers;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.models.Spawner;
import com.orvyn.mmo.models.SpawnerMob;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.attribute.Attribute;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.*;

public class SpawnerManager implements Listener {

    private final OrvynMMOPlugin plugin;
    private final Map<String, Spawner> spawners = new HashMap<>();
    private final Map<Entity, String> spawnedEntities = new HashMap<>();
    private final Map<String, Long> lastSpawnTimes = new HashMap<>();
    private BukkitRunnable spawnerTask;

    // Global settings
    private int levelHealthMultiplier = 20;
    private double levelDamageMultiplier = 1.5;
    private int despawnDistance = 64;

    public SpawnerManager(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
        reload();
        startSpawnerTask();
    }

    public void reload() {
        spawners.clear();
        spawnedEntities.clear();
        lastSpawnTimes.clear();
        loadSpawners();
        plugin.getLogger().info("Loaded " + spawners.size() + " spawners");
    }

    private void loadSpawners() {
        FileConfiguration config = plugin.getConfigHub().getConfig("spawners");

        if (config == null) {
            plugin.getLogger().warning("Spawners configuration not found! Skipping spawner loading.");
            return;
        }

        // Load global settings
        ConfigurationSection globalSection = config.getConfigurationSection("global_settings");
        if (globalSection != null) {
            levelHealthMultiplier = globalSection.getInt("level_health_multiplier", 20);
            levelDamageMultiplier = globalSection.getDouble("level_damage_multiplier", 1.5);
            despawnDistance = globalSection.getInt("despawn_distance", 64);
        }

        // Load spawners
        for (String spawnerId : config.getKeys(false)) {
            if (spawnerId.equals("global_settings")) continue;

            ConfigurationSection section = config.getConfigurationSection(spawnerId);
            if (section != null) {
                Spawner spawner = loadSpawner(spawnerId, section);
                if (spawner != null) {
                    spawners.put(spawnerId, spawner);
                }
            }
        }
    }

    private Spawner loadSpawner(String id, ConfigurationSection section) {
        try {
            // Load region
            ConfigurationSection regionSection = section.getConfigurationSection("region");
            if (regionSection == null) {
                plugin.getLogger().warning("No region defined for spawner: " + id);
                return null;
            }

            String worldName = regionSection.getString("world");
            World world = plugin.getServer().getWorld(worldName);
            if (world == null) {
                plugin.getLogger().warning("World not found for spawner " + id + ": " + worldName);
                return null;
            }

            ConfigurationSection pos1Section = regionSection.getConfigurationSection("pos1");
            ConfigurationSection pos2Section = regionSection.getConfigurationSection("pos2");

            if (pos1Section == null || pos2Section == null) {
                plugin.getLogger().warning("Invalid region positions for spawner: " + id);
                return null;
            }

            Location pos1 = new Location(world,
                pos1Section.getDouble("x"),
                pos1Section.getDouble("y"),
                pos1Section.getDouble("z"));
            Location pos2 = new Location(world,
                pos2Section.getDouble("x"),
                pos2Section.getDouble("y"),
                pos2Section.getDouble("z"));

            Spawner.Region region = new Spawner.Region(world, pos1, pos2);

            // Load mobs
            List<SpawnerMob> mobs = new ArrayList<>();
            List<Map<?, ?>> mobsList = section.getMapList("mobs");

            for (Map<?, ?> mobMap : mobsList) {
                SpawnerMob mob = loadSpawnerMob(mobMap);
                if (mob != null) {
                    mobs.add(mob);
                }
            }

            if (mobs.isEmpty()) {
                plugin.getLogger().warning("No valid mobs defined for spawner: " + id);
                return null;
            }

            int spawnInterval = section.getInt("spawn_interval", 200);
            boolean enabled = section.getBoolean("enabled", true);

            return new Spawner(id, region, mobs, spawnInterval, enabled);

        } catch (Exception e) {
            plugin.getLogger().warning("Failed to load spawner " + id + ": " + e.getMessage());
            return null;
        }
    }

    @SuppressWarnings("unchecked")
    private SpawnerMob loadSpawnerMob(Map<?, ?> mobMap) {
        try {
            String typeStr = (String) mobMap.get("type");
            EntityType type = EntityType.valueOf(typeStr.toUpperCase());

            String name = (String) mobMap.get("name");
            int level = mobMap.containsKey("level") ? (Integer) mobMap.get("level") : 1;
            double spawnChance = mobMap.containsKey("spawn_chance") ? ((Number) mobMap.get("spawn_chance")).doubleValue() : 1.0;
            int maxCount = mobMap.containsKey("max_count") ? (Integer) mobMap.get("max_count") : 5;

            // Load custom drops
            List<SpawnerMob.CustomDrop> customDrops = new ArrayList<>();
            List<Map<?, ?>> dropsList = (List<Map<?, ?>>) mobMap.get("custom_drops");

            if (dropsList != null) {
                for (Map<?, ?> dropMap : dropsList) {
                    String item = (String) dropMap.get("item");
                    double chance = dropMap.containsKey("chance") ? ((Number) dropMap.get("chance")).doubleValue() : 1.0;
                    int amount = dropMap.containsKey("amount") ? (Integer) dropMap.get("amount") : 1;
                    String dropName = (String) dropMap.get("name");

                    customDrops.add(new SpawnerMob.CustomDrop(item, chance, amount, dropName));
                }
            }

            return new SpawnerMob(type, name, level, spawnChance, maxCount, customDrops);

        } catch (Exception e) {
            plugin.getLogger().warning("Failed to load spawner mob: " + e.getMessage());
            return null;
        }
    }

    private void startSpawnerTask() {
        spawnerTask = new BukkitRunnable() {
            @Override
            public void run() {
                processSpawners();
            }
        };
        spawnerTask.runTaskTimer(plugin, 0L, 20L); // Run every second
    }

    private void processSpawners() {
        long currentTime = System.currentTimeMillis();

        for (Spawner spawner : spawners.values()) {
            if (!spawner.isEnabled()) continue;

            String spawnerId = spawner.getId();
            long lastSpawn = lastSpawnTimes.getOrDefault(spawnerId, 0L);
            long spawnIntervalMs = spawner.getSpawnInterval() * 50L; // Convert ticks to ms

            if (currentTime - lastSpawn >= spawnIntervalMs) {
                processSpawner(spawner);
                lastSpawnTimes.put(spawnerId, currentTime);
            }
        }

        // Clean up dead entities
        cleanupDeadEntities();
    }

    private void processSpawner(Spawner spawner) {
        // Check if any players are nearby
        boolean playersNearby = false;
        for (Player player : plugin.getServer().getOnlinePlayers()) {
            if (spawner.isInRegion(player.getLocation())) {
                playersNearby = true;
                break;
            }
        }

        if (!playersNearby) return;

        // Process each mob type
        for (SpawnerMob mobConfig : spawner.getMobs()) {
            if (!mobConfig.shouldSpawn()) continue;

            // Count existing mobs of this type in the spawner
            int existingCount = countMobsInSpawner(spawner, mobConfig);
            if (existingCount >= mobConfig.getMaxCount()) continue;

            // Spawn the mob
            Location spawnLoc = spawner.getRandomLocationInRegion();
            spawnCustomMob(spawner, mobConfig, spawnLoc);
        }
    }

    private int countMobsInSpawner(Spawner spawner, SpawnerMob mobConfig) {
        int count = 0;
        for (Entity entity : spawnedEntities.keySet()) {
            if (entity.getType() == mobConfig.getType() &&
                spawner.isInRegion(entity.getLocation()) &&
                !entity.isDead()) {
                count++;
            }
        }
        return count;
    }

    private void spawnCustomMob(Spawner spawner, SpawnerMob mobConfig, Location location) {
        Entity entity = location.getWorld().spawnEntity(location, mobConfig.getType());

        if (entity instanceof LivingEntity) {
            LivingEntity livingEntity = (LivingEntity) entity;

            // Set custom name
            livingEntity.setCustomName(ChatColor.translateAlternateColorCodes('§', mobConfig.getName()));
            livingEntity.setCustomNameVisible(true);

            // Set level-based health and damage
            if (livingEntity.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH) != null) {
                double baseHealth = livingEntity.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).getBaseValue();
                double newHealth = baseHealth + (mobConfig.getLevel() * levelHealthMultiplier);
                livingEntity.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).setBaseValue(newHealth);
                livingEntity.setHealth(newHealth);
            }

            // Set damage if applicable
            if (livingEntity.getAttribute(org.bukkit.attribute.Attribute.ATTACK_DAMAGE) != null) {
                double baseDamage = livingEntity.getAttribute(org.bukkit.attribute.Attribute.ATTACK_DAMAGE).getBaseValue();
                double newDamage = baseDamage + (mobConfig.getLevel() * levelDamageMultiplier);
                livingEntity.getAttribute(org.bukkit.attribute.Attribute.ATTACK_DAMAGE).setBaseValue(newDamage);
            }
        }

        // Track the spawned entity
        spawnedEntities.put(entity, spawner.getId());
    }

    private void cleanupDeadEntities() {
        Iterator<Map.Entry<Entity, String>> iterator = spawnedEntities.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Entity, String> entry = iterator.next();
            Entity entity = entry.getKey();

            if (entity.isDead() || !entity.isValid()) {
                iterator.remove();
                continue;
            }

            // Check despawn distance
            boolean playerNearby = false;
            for (Player player : plugin.getServer().getOnlinePlayers()) {
                if (player.getLocation().distance(entity.getLocation()) <= despawnDistance) {
                    playerNearby = true;
                    break;
                }
            }

            if (!playerNearby) {
                entity.remove();
                iterator.remove();
            }
        }
    }

    @EventHandler
    public void onEntityDeath(EntityDeathEvent event) {
        Entity entity = event.getEntity();
        String spawnerId = spawnedEntities.get(entity);

        if (spawnerId != null) {
            // Remove from tracking
            spawnedEntities.remove(entity);

            // Find the spawner mob config for custom drops
            Spawner spawner = spawners.get(spawnerId);
            if (spawner != null) {
                for (SpawnerMob mobConfig : spawner.getMobs()) {
                    if (mobConfig.getType() == entity.getType()) {
                        handleCustomDrops(event, mobConfig);
                        break;
                    }
                }
            }
        }
    }

    private void handleCustomDrops(EntityDeathEvent event, SpawnerMob mobConfig) {
        // Clear default drops
        event.getDrops().clear();

        // Add custom drops
        for (SpawnerMob.CustomDrop drop : mobConfig.getCustomDrops()) {
            if (drop.shouldDrop()) {
                try {
                    Material material = Material.valueOf(drop.getItem().toUpperCase());
                    ItemStack item = new ItemStack(material, drop.getAmount());

                    if (drop.getName() != null && !drop.getName().isEmpty()) {
                        ItemMeta meta = item.getItemMeta();
                        if (meta != null) {
                            meta.setDisplayName(ChatColor.translateAlternateColorCodes('§', drop.getName()));
                            item.setItemMeta(meta);
                        }
                    }

                    event.getDrops().add(item);
                } catch (IllegalArgumentException e) {
                    plugin.getLogger().warning("Invalid material for custom drop: " + drop.getItem());
                }
            }
        }
    }

    public void shutdown() {
        if (spawnerTask != null) {
            spawnerTask.cancel();
        }

        // Remove all spawned entities
        for (Entity entity : spawnedEntities.keySet()) {
            if (!entity.isDead()) {
                entity.remove();
            }
        }
        spawnedEntities.clear();
    }

    // Getters for commands
    public Map<String, Spawner> getSpawners() { return spawners; }
    public Map<Entity, String> getSpawnedEntities() { return spawnedEntities; }
}
