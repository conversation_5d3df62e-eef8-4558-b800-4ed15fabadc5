package com.orvyn.mmo.gui;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import com.orvyn.mmo.models.Party;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class PartyManagementGUI implements Listener {

    private final OrvynMMOPlugin plugin;

    public PartyManagementGUI(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    public void openPartyGUI(Player player) {
        Inventory gui = Bukkit.createInventory(null, 54, ChatColor.DARK_PURPLE + "Party Management");

        // Get player's party
        Party party = plugin.getPartyManager().getPlayerParty(player);
        
        if (party == null) {
            // No party - show create party option
            setupNoPartyGUI(gui, player);
        } else {
            // Has party - show party management
            setupPartyManagementGUI(gui, player, party);
        }

        player.openInventory(gui);
    }

    private void setupNoPartyGUI(Inventory gui, Player player) {
        // Create Party button
        ItemStack createParty = new ItemStack(Material.EMERALD);
        ItemMeta createMeta = createParty.getItemMeta();
        createMeta.setDisplayName(ChatColor.GREEN + "Create Party");
        List<String> createLore = new ArrayList<>();
        createLore.add(ChatColor.GRAY + "Start your own party and");
        createLore.add(ChatColor.GRAY + "invite other players!");
        createLore.add("");
        createLore.add(ChatColor.YELLOW + "Click to create party");
        createMeta.setLore(createLore);
        createParty.setItemMeta(createMeta);
        gui.setItem(22, createParty);

        // Info item
        ItemStack info = new ItemStack(Material.BOOK);
        ItemMeta infoMeta = info.getItemMeta();
        infoMeta.setDisplayName(ChatColor.AQUA + "Party Benefits");
        List<String> infoLore = new ArrayList<>();
        infoLore.add(ChatColor.GRAY + "Parties provide:");
        infoLore.add(ChatColor.GREEN + "• Shared experience");
        infoLore.add(ChatColor.GREEN + "• Health/mana visibility");
        infoLore.add(ChatColor.GREEN + "• Coordinated gameplay");
        infoLore.add(ChatColor.GREEN + "• Group chat");
        infoMeta.setLore(infoLore);
        info.setItemMeta(infoMeta);
        gui.setItem(13, info);
    }

    private void setupPartyManagementGUI(Inventory gui, Player player, Party party) {
        // Party info
        ItemStack partyInfo = new ItemStack(Material.WHITE_BANNER);
        ItemMeta infoMeta = partyInfo.getItemMeta();
        infoMeta.setDisplayName(ChatColor.GOLD + "Party Information");
        List<String> infoLore = new ArrayList<>();
        infoLore.add(ChatColor.GRAY + "Members: " + ChatColor.WHITE + party.getMembers().size() + "/6");
        infoLore.add(ChatColor.GRAY + "Leader: " + ChatColor.WHITE + getPlayerName(party.getLeader()));
        infoMeta.setLore(infoLore);
        partyInfo.setItemMeta(infoMeta);
        gui.setItem(4, partyInfo);

        // Party members (starting from slot 10)
        int slot = 10;
        for (UUID memberId : party.getMembers()) {
            Player member = Bukkit.getPlayer(memberId);
            if (member != null) {
                ItemStack memberHead = new ItemStack(Material.PLAYER_HEAD);
                SkullMeta skullMeta = (SkullMeta) memberHead.getItemMeta();
                skullMeta.setOwningPlayer(member);
                skullMeta.setDisplayName(ChatColor.YELLOW + member.getName());
                
                List<String> memberLore = new ArrayList<>();
                PlayerData memberData = plugin.getPlayerDataManager().getPlayerData(member);
                if (memberData != null) {
                    memberLore.add(ChatColor.GRAY + "Level: " + ChatColor.WHITE + memberData.getClassLevel());
                    memberLore.add(ChatColor.GRAY + "Class: " + ChatColor.WHITE + memberData.getPlayerClass());
                    memberLore.add(ChatColor.GRAY + "Health: " + ChatColor.RED + (int)member.getHealth() + "/" + (int)member.getMaxHealth());
                    memberLore.add(ChatColor.GRAY + "Mana: " + ChatColor.BLUE + memberData.getCurrentMana() + "/" + memberData.getMaxMana());
                }
                
                if (memberId.equals(party.getLeader())) {
                    memberLore.add("");
                    memberLore.add(ChatColor.GOLD + "★ Party Leader ★");
                }
                
                if (player.getUniqueId().equals(party.getLeader()) && !memberId.equals(player.getUniqueId())) {
                    memberLore.add("");
                    memberLore.add(ChatColor.RED + "Right-click to kick");
                }
                
                skullMeta.setLore(memberLore);
                memberHead.setItemMeta(skullMeta);
                gui.setItem(slot, memberHead);
                slot++;
                
                if (slot == 17) slot = 19; // Skip to next row
                if (slot == 26) break; // Max 6 members
            }
        }

        // Action buttons
        if (player.getUniqueId().equals(party.getLeader())) {
            // Invite player button
            ItemStack invite = new ItemStack(Material.EMERALD);
            ItemMeta inviteMeta = invite.getItemMeta();
            inviteMeta.setDisplayName(ChatColor.GREEN + "Invite Player");
            List<String> inviteLore = new ArrayList<>();
            inviteLore.add(ChatColor.GRAY + "Invite a player to your party");
            inviteLore.add("");
            inviteLore.add(ChatColor.YELLOW + "Click to invite");
            inviteMeta.setLore(inviteLore);
            invite.setItemMeta(inviteMeta);
            gui.setItem(45, invite);

            // Disband party button
            ItemStack disband = new ItemStack(Material.BARRIER);
            ItemMeta disbandMeta = disband.getItemMeta();
            disbandMeta.setDisplayName(ChatColor.RED + "Disband Party");
            List<String> disbandLore = new ArrayList<>();
            disbandLore.add(ChatColor.GRAY + "Permanently disband the party");
            disbandLore.add("");
            disbandLore.add(ChatColor.RED + "Click to disband");
            disbandMeta.setLore(disbandLore);
            disband.setItemMeta(disbandMeta);
            gui.setItem(53, disband);
        }

        // Leave party button (for non-leaders)
        if (!player.getUniqueId().equals(party.getLeader())) {
            ItemStack leave = new ItemStack(Material.IRON_DOOR);
            ItemMeta leaveMeta = leave.getItemMeta();
            leaveMeta.setDisplayName(ChatColor.YELLOW + "Leave Party");
            List<String> leaveLore = new ArrayList<>();
            leaveLore.add(ChatColor.GRAY + "Leave the current party");
            leaveLore.add("");
            leaveLore.add(ChatColor.YELLOW + "Click to leave");
            leaveMeta.setLore(leaveLore);
            leave.setItemMeta(leaveMeta);
            gui.setItem(49, leave);
        }
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!event.getView().getTitle().equals(ChatColor.DARK_PURPLE + "Party Management")) {
            return;
        }

        event.setCancelled(true);
        
        if (!(event.getWhoClicked() instanceof Player)) return;
        Player player = (Player) event.getWhoClicked();
        
        ItemStack clicked = event.getCurrentItem();
        if (clicked == null || clicked.getType() == Material.AIR) return;

        switch (clicked.getType()) {
            case EMERALD:
                if (clicked.getItemMeta().getDisplayName().contains("Create Party")) {
                    player.closeInventory();
                    plugin.getPartyManager().createParty(player);
                } else if (clicked.getItemMeta().getDisplayName().contains("Invite Player")) {
                    player.closeInventory();
                    player.sendMessage(ChatColor.YELLOW + "Use " + ChatColor.WHITE + "/ommo party invite <player>" + ChatColor.YELLOW + " to invite a player!");
                }
                break;
            case BARRIER:
                if (clicked.getItemMeta().getDisplayName().contains("Disband Party")) {
                    player.closeInventory();
                    plugin.getPartyManager().disbandParty(player);
                }
                break;
            case IRON_DOOR:
                if (clicked.getItemMeta().getDisplayName().contains("Leave Party")) {
                    player.closeInventory();
                    plugin.getPartyManager().leaveParty(player);
                }
                break;
            case PLAYER_HEAD:
                // Handle kicking members (right-click)
                if (event.isRightClick()) {
                    String memberName = ChatColor.stripColor(clicked.getItemMeta().getDisplayName());
                    Player target = Bukkit.getPlayer(memberName);
                    if (target != null && !target.equals(player)) {
                        Party party = plugin.getPartyManager().getPlayerParty(player);
                        if (party != null && player.getUniqueId().equals(party.getLeader())) {
                            player.closeInventory();
                            plugin.getPartyManager().kickPlayer(player, target);
                        }
                    }
                }
                break;
            default:
                // Do nothing for other materials
                break;
        }
    }

    private String getPlayerName(UUID playerId) {
        Player player = Bukkit.getPlayer(playerId);
        return player != null ? player.getName() : "Unknown";
    }
}
