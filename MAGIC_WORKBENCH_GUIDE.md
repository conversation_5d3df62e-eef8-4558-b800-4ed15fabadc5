# 🔮 Magic Workbench System - Complete Guide

## Overview
The Magic Workbench is a powerful custom crafting system that allows players to create 15 unique magical items with special abilities. This system adds a new layer of progression and utility to your OrvynMMO server.

## Getting Started

### For Admins
1. **Get a Magic Bench**: Use `/ommo magicbench` (requires op or `orvynmmo.admin.magicbench` permission)
2. **Place the Magic Bench**: Right-click to place it in the world like a regular block
3. **Access Crafting**: Right-click the placed Magic Bench to open the custom crafting interface

### For Players
- Find Magic Benches placed by admins in spawn areas or special locations
- Right-click any Magic Bench to access the magical crafting system
- Use the Recipe Guide button in the crafting interface to see available recipes

## Magic Bench Crafting Recipe
To craft a Magic Bench itself (admin-only item):
```
[Enchanting Table] + Admin Command
Use: /ommo magicbench
```

## 🎯 Complete Item List & Recipes

### 1. **Vein Miner Pickaxe** 💎
**Recipe Pattern:**
```
[Redstone] [Redstone] [Redstone]
[Redstone] [Diamond Pickaxe] [ ]
[ ] [ ] [ ]
```
**Ability:** Mines all connected ore blocks of the same type when one block is mined
**Cooldown:** 3 seconds

### 2. **3x3 Excavator Pickaxe** 🧨
**Recipe Pattern:**
```
[TNT] [Nether Star] [TNT]
[TNT] [Iron Pickaxe] [TNT]
[ ] [ ] [ ]
```
**Ability:** Mines a 3x3x3 area centered on the block you mine
**Cooldown:** 5 seconds

### 3. **Lumber Axe** 🌳
**Recipe Pattern:**
```
[Oak Log] [Oak Log] [ ]
[Oak Log] [Diamond Axe] [Oak Log]
[ ] [ ] [ ]
```
**Ability:** Chops down entire trees when you break one log block
**Cooldown:** 2 seconds

### 4. **Harvester Hoe** 🌾
**Recipe Pattern:**
```
[Wheat Seeds] [Wheat Seeds] [ ]
[Wheat Seeds] [Diamond Hoe] [Wheat Seeds]
[ ] [ ] [ ]
```
**Ability:** Automatically harvests and replants crops in a 5x5 area
**Cooldown:** 1 second

### 5. **Teleportation Staff** ✨
**Recipe Pattern:**
```
[Ender Pearl] [Ender Pearl] [ ]
[Ender Pearl] [Stick] [Ender Pearl]
[ ] [ ] [ ]
```
**Ability:** Right-click to teleport to where you're looking (max 50 blocks)
**Cooldown:** 10 seconds

### 6. **Lightning Sword** ⚡
**Recipe Pattern:**
```
[Glowstone Dust] [Glowstone Dust] [ ]
[Glowstone Dust] [Diamond Sword] [Glowstone Dust]
[ ] [ ] [ ]
```
**Ability:** Right-click to summon lightning at target location
**Cooldown:** 8 seconds

### 7. **Explosive Bow** 💥
**Recipe Pattern:**
```
[TNT] [TNT] [ ]
[Gunpowder] [Bow] [TNT]
[ ] [TNT] [ ]
```
**Ability:** Arrows create small explosions on impact
**Cooldown:** 5 seconds per shot

### 8. **Grappling Hook** 🪝
**Recipe Pattern:**
```
[String] [String] [String]
[Iron Ingot] [Fishing Rod] [Iron Ingot]
[ ] [Iron Ingot] [ ]
```
**Ability:** Right-click to pull yourself toward blocks or entities
**Cooldown:** 3 seconds

### 9. **Auto-Smelting Pickaxe** 🔥
**Recipe Pattern:**
```
[Coal] [Furnace] [Coal]
[Coal] [Iron Pickaxe] [Coal]
[ ] [ ] [ ]
```
**Ability:** Automatically smelts ores as you mine them
**Cooldown:** None

### 10. **Magnet Tool** 🧲
**Recipe Pattern:**
```
[Iron Ingot] [Redstone] [Iron Ingot]
[Redstone] [Compass] [Redstone]
[Iron Ingot] [Redstone] [Iron Ingot]
```
**Ability:** Automatically picks up items in a 10-block radius when held
**Cooldown:** None

### 11. **Night Vision Goggles** 👓
**Recipe Pattern:**
```
[Glass] [Glowstone Dust] [Glass]
[Iron Ingot] [Leather Helmet] [Iron Ingot]
[ ] [Redstone] [ ]
```
**Ability:** Grants permanent night vision when worn as helmet
**Cooldown:** None

### 12. **Speed Boots** 👟
**Recipe Pattern:**
```
[Feather] [Sugar] [Feather]
[Leather] [Leather Boots] [Leather]
[ ] [Redstone] [ ]
```
**Ability:** Grants permanent speed effect when worn as boots
**Cooldown:** None

### 13. **Flame Sword** 🔥⚔️
**Recipe Pattern:**
```
[Blaze Powder] [Fire Charge] [Blaze Powder]
[Blaze Rod] [Iron Sword] [Blaze Rod]
[ ] [Magma Cream] [ ]
```
**Ability:** Sets enemies on fire on hit, right-click to shoot fireballs
**Cooldown:** 5 seconds (fireball)

### 14. **Ice Wand** ❄️
**Recipe Pattern:**
```
[Ice] [Packed Ice] [Ice]
[Snowball] [Stick] [Snowball]
[ ] [Blue Ice] [ ]
```
**Ability:** Freezes water, creates ice paths, right-click to slow enemies
**Cooldown:** 4 seconds

### 15. **Healing Staff** 💚
**Recipe Pattern:**
```
[Golden Apple] [Diamond] [Golden Apple]
[Gold Ingot] [Stick] [Gold Ingot]
[ ] [Emerald] [ ]
```
**Ability:** Right-click to heal nearby players (4 hearts in 5-block radius)
**Cooldown:** 15 seconds

## 🎮 Usage Instructions

### Basic Crafting
1. **Open Magic Bench**: Right-click a placed Magic Bench
2. **Place Materials**: Put crafting materials in the 3x3 grid (left side)
3. **View Result**: Crafted item appears in the result slot (center)
4. **Take Item**: Click the result to craft and take the item
5. **Recipe Guide**: Click the book icon to see all available recipes

### Item Abilities
- **Right-Click Abilities**: Most items have special abilities activated by right-clicking
- **Passive Abilities**: Some items (armor, tools) work automatically when equipped/held
- **Cooldowns**: Most abilities have cooldowns to prevent spam
- **Visual Effects**: All abilities include particle effects and sounds

### Special Requirements
- **Materials**: All recipes use vanilla Minecraft items
- **Permissions**: Players need access to a Magic Bench (placed by admins)
- **Durability**: Magic items have enhanced durability and enchantment glow
- **Stacking**: Magic items don't stack due to their special properties

## 🔧 Admin Features

### Commands
- `/ommo magicbench` - Give yourself a Magic Bench (requires op or permission)
- `/ommo help` - View all available commands including Magic Bench

### Permissions
- `orvynmmo.admin.magicbench` - Allows use of `/ommo magicbench` command
- `orvynmmo.admin` - General admin permission for all admin commands

### Server Integration
- **Automatic Saving**: Magic Bench locations are saved and persist through server restarts
- **Multi-World Support**: Magic Benches work in all worlds
- **Performance Optimized**: Efficient event handling and cooldown management
- **Conflict Prevention**: Magic items use persistent data containers to avoid conflicts

## 🎯 Balancing & Economy

### Material Costs
- **Common Items**: Use easily obtainable materials (iron, redstone, basic items)
- **Powerful Items**: Require rare materials (nether star, diamonds, emeralds)
- **Progression**: Items scale in power and cost appropriately

### Cooldown System
- **Prevents Spam**: All abilities have appropriate cooldowns
- **Balanced Usage**: More powerful abilities have longer cooldowns
- **Fair PvP**: Cooldowns prevent overpowered combinations in combat

### Server Economy
- **Material Demand**: Creates demand for various vanilla materials
- **Progression Goals**: Gives players long-term crafting objectives
- **Trade Opportunities**: Players can trade materials and finished items

## 🔍 Troubleshooting

### Common Issues
1. **Can't place Magic Bench**: Ensure you have building permissions in the area
2. **Recipe not working**: Check the Recipe Guide for exact material placement
3. **Ability not activating**: Check cooldown status and ensure proper right-click
4. **Items disappearing**: Magic items are saved with persistent data - contact admin if issues persist

### For Admins
- **Plugin Conflicts**: Magic items use namespaced keys to prevent conflicts
- **Performance**: System is optimized for minimal server impact
- **Backup**: Magic Bench locations are saved in plugin data
- **Updates**: System is designed to be compatible with future OrvynMMO updates

---

*This Magic Workbench system seamlessly integrates with the existing OrvynMMO class and skill systems, providing players with powerful tools to enhance their gameplay experience!*
