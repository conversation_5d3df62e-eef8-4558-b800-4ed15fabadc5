package com.orvyn.mmo.models;

import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.*;

public class EnhancedItem {
    
    public enum ItemType {
        WEAPON, ARMOR, TOOL, ACCESSORY
    }
    
    public enum SocketType {
        DAMAGE, DEFENSE, SPEED, CRITICAL, ELEMENTAL, SKILL
    }
    
    private final String itemId;
    private final ItemType type;
    private final Material baseMaterial;
    private final String name;
    private final int baseLevel;
    private int enhancementLevel;
    private final Map<String, Integer> baseStats;
    private final List<Socket> sockets;
    private final int maxSockets;
    
    public EnhancedItem(String itemId, ItemType type, Material baseMaterial, String name, 
                       int baseLevel, Map<String, Integer> baseStats, int maxSockets) {
        this.itemId = itemId;
        this.type = type;
        this.baseMaterial = baseMaterial;
        this.name = name;
        this.baseLevel = baseLevel;
        this.enhancementLevel = 0;
        this.baseStats = new HashMap<>(baseStats);
        this.sockets = new ArrayList<>();
        this.maxSockets = maxSockets;
        
        // Initialize empty sockets
        for (int i = 0; i < maxSockets; i++) {
            sockets.add(new Socket(SocketType.DAMAGE, null)); // Empty socket
        }
    }
    
    public ItemStack createItemStack() {
        ItemStack item = new ItemStack(baseMaterial);
        ItemMeta meta = item.getItemMeta();
        
        // Set display name with enhancement level
        String displayName = ChatColor.WHITE + name;
        if (enhancementLevel > 0) {
            displayName += ChatColor.GREEN + " +" + enhancementLevel;
        }
        meta.setDisplayName(displayName);
        
        // Create lore
        List<String> lore = new ArrayList<>();
        
        // Item type and level
        lore.add(ChatColor.GRAY + "Type: " + ChatColor.WHITE + capitalizeFirst(type.name()));
        lore.add(ChatColor.GRAY + "Level: " + ChatColor.WHITE + baseLevel);
        if (enhancementLevel > 0) {
            lore.add(ChatColor.GRAY + "Enhancement: " + ChatColor.GREEN + "+" + enhancementLevel);
        }
        lore.add("");
        
        // Stats
        lore.add(ChatColor.YELLOW + "Stats:");
        Map<String, Integer> totalStats = calculateTotalStats();
        for (Map.Entry<String, Integer> stat : totalStats.entrySet()) {
            String statName = capitalizeFirst(stat.getKey().replace("_", " "));
            int baseValue = baseStats.getOrDefault(stat.getKey(), 0);
            int totalValue = stat.getValue();
            
            if (totalValue > baseValue) {
                lore.add(ChatColor.GRAY + "• " + statName + ": " + ChatColor.WHITE + baseValue + 
                        ChatColor.GREEN + " (+" + (totalValue - baseValue) + ")");
            } else {
                lore.add(ChatColor.GRAY + "• " + statName + ": " + ChatColor.WHITE + totalValue);
            }
        }
        
        // Sockets
        if (maxSockets > 0) {
            lore.add("");
            lore.add(ChatColor.AQUA + "Sockets (" + getUsedSockets() + "/" + maxSockets + "):");
            for (int i = 0; i < maxSockets; i++) {
                Socket socket = sockets.get(i);
                if (socket.getGem() != null) {
                    Gem gem = socket.getGem();
                    lore.add(ChatColor.GRAY + "• " + gem.getDisplayName() + " " + gem.getDescription());
                } else {
                    lore.add(ChatColor.GRAY + "• " + ChatColor.DARK_GRAY + "[Empty Socket]");
                }
            }
        }
        
        // Enhancement info
        if (canBeEnhanced()) {
            lore.add("");
            lore.add(ChatColor.GREEN + "Can be enhanced further");
        }
        
        lore.add("");
        lore.add(ChatColor.GOLD + "Enhanced Item");
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        
        return item;
    }
    
    public Map<String, Integer> calculateTotalStats() {
        Map<String, Integer> totalStats = new HashMap<>(baseStats);
        
        // Add enhancement bonuses
        for (Map.Entry<String, Integer> stat : baseStats.entrySet()) {
            int enhancementBonus = (int) (stat.getValue() * 0.1 * enhancementLevel);
            totalStats.put(stat.getKey(), stat.getValue() + enhancementBonus);
        }
        
        // Add gem bonuses
        for (Socket socket : sockets) {
            if (socket.getGem() != null) {
                Gem gem = socket.getGem();
                for (Map.Entry<String, Integer> bonus : gem.getStatBonuses().entrySet()) {
                    totalStats.merge(bonus.getKey(), bonus.getValue(), Integer::sum);
                }
            }
        }
        
        return totalStats;
    }
    
    public boolean canSocketGem(Gem gem, int socketIndex) {
        if (socketIndex < 0 || socketIndex >= sockets.size()) return false;
        Socket socket = sockets.get(socketIndex);
        return socket.getGem() == null; // Socket must be empty
    }
    
    public boolean socketGem(Gem gem, int socketIndex) {
        if (!canSocketGem(gem, socketIndex)) return false;
        
        Socket socket = sockets.get(socketIndex);
        socket.setGem(gem);
        return true;
    }
    
    public Gem removeGem(int socketIndex) {
        if (socketIndex < 0 || socketIndex >= sockets.size()) return null;
        
        Socket socket = sockets.get(socketIndex);
        Gem removedGem = socket.getGem();
        socket.setGem(null);
        return removedGem;
    }
    
    public boolean enhance() {
        if (!canBeEnhanced()) return false;
        
        enhancementLevel++;
        return true;
    }
    
    public boolean canBeEnhanced() {
        return enhancementLevel < getMaxEnhancementLevel();
    }
    
    public int getMaxEnhancementLevel() {
        return baseLevel + 10; // Can enhance up to base level + 10
    }
    
    public int getUsedSockets() {
        int used = 0;
        for (Socket socket : sockets) {
            if (socket.getGem() != null) used++;
        }
        return used;
    }
    
    public int getEnhancementCost() {
        return (enhancementLevel + 1) * 100; // Increasing cost
    }
    
    private String capitalizeFirst(String str) {
        if (str == null || str.isEmpty()) return str;
        return str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase();
    }
    
    // Getters
    public String getItemId() { return itemId; }
    public ItemType getType() { return type; }
    public Material getBaseMaterial() { return baseMaterial; }
    public String getName() { return name; }
    public int getBaseLevel() { return baseLevel; }
    public int getEnhancementLevel() { return enhancementLevel; }
    public Map<String, Integer> getBaseStats() { return new HashMap<>(baseStats); }
    public List<Socket> getSockets() { return new ArrayList<>(sockets); }
    public int getMaxSockets() { return maxSockets; }
    
    public void setEnhancementLevel(int level) {
        this.enhancementLevel = Math.max(0, Math.min(level, getMaxEnhancementLevel()));
    }
    
    public static class Socket {
        private final SocketType type;
        private Gem gem;
        
        public Socket(SocketType type, Gem gem) {
            this.type = type;
            this.gem = gem;
        }
        
        public SocketType getType() { return type; }
        public Gem getGem() { return gem; }
        public void setGem(Gem gem) { this.gem = gem; }
        public boolean isEmpty() { return gem == null; }
    }
}
