package com.orvyn.mmo.managers;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import com.orvyn.mmo.enums.Attribute;
import com.orvyn.mmo.models.PlayerClass;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class ClassManager {
    
    private final OrvynMMOPlugin plugin;
    private final Map<String, PlayerClass> classes = new HashMap<>();
    
    public ClassManager(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
        loadClasses();
    }
    
    public void loadClasses() {
        classes.clear();
        
        FileConfiguration config = plugin.getConfigHub().getConfig("classes.yml");
        if (config == null) return;
        
        for (String className : config.getKeys(false)) {
            ConfigurationSection classSection = config.getConfigurationSection(className);
            if (classSection != null) {
                PlayerClass playerClass = loadClass(className, classSection);
                classes.put(className, playerClass);
            }
        }
        
        plugin.getLogger().info("Loaded " + classes.size() + " classes");
    }
    
    private PlayerClass loadClass(String name, ConfigurationSection section) {
        PlayerClass playerClass = new PlayerClass(name);
        
        // Load base attributes
        ConfigurationSection baseSection = section.getConfigurationSection("base");
        if (baseSection != null) {
            for (String attrName : baseSection.getKeys(false)) {
                Attribute attr = Attribute.fromString(attrName);
                if (attr != null) {
                    int value = baseSection.getInt(attrName);
                    playerClass.getBaseAttributes().put(attr, value);
                }
            }
        }
        
        // Load skills
        ConfigurationSection skillsSection = section.getConfigurationSection("skills");
        if (skillsSection != null) {
            for (String skillName : skillsSection.getKeys(false)) {
                ConfigurationSection skillSection = skillsSection.getConfigurationSection(skillName);
                if (skillSection != null) {
                    int level = skillSection.getInt("level", 1);
                    playerClass.getSkills().put(skillName, level);
                }
            }
        }
        
        return playerClass;
    }
    
    public boolean setPlayerClass(Player player, String className) {
        PlayerClass playerClass = classes.get(className);
        if (playerClass == null) {
            return false;
        }
        
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        data.setPlayerClass(className);
        
        // Reset base attributes to class defaults
        data.getBaseAttributes().clear();
        data.getBaseAttributes().putAll(playerClass.getBaseAttributes());
        
        // Unlock class skills based on player level
        data.getUnlockedSkills().clear();
        unlockSkillsForLevel(data, playerClass, data.getClassLevel());
        
        // Recalculate resources
        data.recalculateResources();

        // Give starting items
        giveStartingItems(player, className);

        return true;
    }

    private void unlockSkillsForLevel(PlayerData data, PlayerClass playerClass, int level) {
        // Unlock all skills that the player's level allows
        for (Map.Entry<String, Integer> skillEntry : playerClass.getSkills().entrySet()) {
            String skillId = skillEntry.getKey();
            int requiredLevel = skillEntry.getValue();

            if (level >= requiredLevel) {
                data.getUnlockedSkills().add(skillId);
            }
        }
    }

    public void checkLevelUpSkills(Player player, int newLevel) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        if (data == null) return;

        String className = data.getPlayerClass();
        PlayerClass playerClass = classes.get(className);
        if (playerClass == null) return;

        // Check for new skills to unlock
        for (Map.Entry<String, Integer> skillEntry : playerClass.getSkills().entrySet()) {
            String skillId = skillEntry.getKey();
            int requiredLevel = skillEntry.getValue();

            if (newLevel >= requiredLevel && !data.getUnlockedSkills().contains(skillId)) {
                data.getUnlockedSkills().add(skillId);
                player.sendMessage(ChatColor.GOLD + "✦ New Skill Unlocked: " + ChatColor.YELLOW + skillId + ChatColor.GOLD + "!");
                player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.5f);
            }
        }
    }

    private void giveStartingItems(Player player, String className) {
        // Clear inventory first
        player.getInventory().clear();

        switch (className.toLowerCase()) {
            case "warrior":
                giveWarriorItems(player);
                break;
            case "mage":
                giveMageItems(player);
                break;
            case "archer":
                giveArcherItems(player);
                break;
            case "rogue":
                giveRogueItems(player);
                break;
            case "paladin":
                givePaladinItems(player);
                break;
            case "druid":
                giveDruidItems(player);
                break;
            default:
                // Give basic items for unknown classes
                ItemStack sword = new ItemStack(Material.WOODEN_SWORD);
                ItemStack food = new ItemStack(Material.BREAD, 8);
                player.getInventory().addItem(sword, food);
                break;
        }

        player.sendMessage(ChatColor.GREEN + "You have received your starting equipment!");
    }

    private void giveWarriorItems(Player player) {
        // Iron Training Blade - Special sword with beam ability
        ItemStack sword = new ItemStack(Material.IRON_SWORD);
        ItemMeta swordMeta = sword.getItemMeta();
        if (swordMeta != null) {
            swordMeta.setDisplayName(ChatColor.RED + "Iron Training Blade");
            swordMeta.setLore(java.util.Arrays.asList(
                ChatColor.GRAY + "A warrior's first weapon",
                ChatColor.YELLOW + "Right-click to fire a sword beam!",
                ChatColor.BLUE + "Costs: 10 Mana",
                ChatColor.BLUE + "Cooldown: 5 seconds",
                ChatColor.BLUE + "Damage: 8"
            ));

            // Mark as special weapon
            NamespacedKey key = new NamespacedKey(plugin, "class_weapon");
            swordMeta.getPersistentDataContainer().set(key, PersistentDataType.STRING, "warrior_blade");

            sword.setItemMeta(swordMeta);
        }

        // Basic armor and food
        ItemStack helmet = new ItemStack(Material.LEATHER_HELMET);
        ItemStack chestplate = new ItemStack(Material.LEATHER_CHESTPLATE);
        ItemStack leggings = new ItemStack(Material.LEATHER_LEGGINGS);
        ItemStack boots = new ItemStack(Material.LEATHER_BOOTS);
        ItemStack food = new ItemStack(Material.BREAD, 16);

        player.getInventory().addItem(sword, helmet, chestplate, leggings, boots, food);
    }

    private void giveMageItems(Player player) {
        // Mage Staff - Special staff with fireball ability
        ItemStack staff = new ItemStack(Material.STICK);
        ItemMeta staffMeta = staff.getItemMeta();
        if (staffMeta != null) {
            staffMeta.setDisplayName(ChatColor.BLUE + "Mage Staff");
            staffMeta.setLore(java.util.Arrays.asList(
                ChatColor.GRAY + "A conduit for magical energy",
                ChatColor.YELLOW + "Right-click to cast fireball!",
                ChatColor.BLUE + "Costs: 15 Mana",
                ChatColor.BLUE + "Cooldown: 6 seconds",
                ChatColor.BLUE + "Damage: 10 + 3 AoE"
            ));

            // Mark as special weapon
            NamespacedKey key = new NamespacedKey(plugin, "class_weapon");
            staffMeta.getPersistentDataContainer().set(key, PersistentDataType.STRING, "mage_staff");

            staff.setItemMeta(staffMeta);
        }

        // Basic armor and food
        ItemStack helmet = new ItemStack(Material.LEATHER_HELMET);
        ItemStack chestplate = new ItemStack(Material.LEATHER_CHESTPLATE);
        ItemStack leggings = new ItemStack(Material.LEATHER_LEGGINGS);
        ItemStack boots = new ItemStack(Material.LEATHER_BOOTS);
        ItemStack food = new ItemStack(Material.BREAD, 16);

        player.getInventory().addItem(staff, helmet, chestplate, leggings, boots, food);
    }

    private void giveArcherItems(Player player) {
        // Hunter Bow - Special bow with power shot ability
        ItemStack bow = new ItemStack(Material.BOW);
        ItemMeta bowMeta = bow.getItemMeta();
        if (bowMeta != null) {
            bowMeta.setDisplayName(ChatColor.GREEN + "Hunter Bow");
            bowMeta.setLore(java.util.Arrays.asList(
                ChatColor.GRAY + "A precision hunting weapon",
                ChatColor.YELLOW + "Arrows cost 5 mana each",
                ChatColor.YELLOW + "Every 5th arrow is a power shot!",
                ChatColor.BLUE + "Power shot: 2x damage + homing",
                ChatColor.BLUE + "Regular damage: 6"
            ));

            // Mark as special weapon
            NamespacedKey key = new NamespacedKey(plugin, "class_weapon");
            bowMeta.getPersistentDataContainer().set(key, PersistentDataType.STRING, "hunter_bow");

            bow.setItemMeta(bowMeta);
        }

        // Basic armor, arrows, and food
        ItemStack helmet = new ItemStack(Material.LEATHER_HELMET);
        ItemStack chestplate = new ItemStack(Material.LEATHER_CHESTPLATE);
        ItemStack leggings = new ItemStack(Material.LEATHER_LEGGINGS);
        ItemStack boots = new ItemStack(Material.LEATHER_BOOTS);
        ItemStack arrows = new ItemStack(Material.ARROW, 64);
        ItemStack food = new ItemStack(Material.BREAD, 16);

        player.getInventory().addItem(bow, helmet, chestplate, leggings, boots, arrows, food);
    }

    private void giveRogueItems(Player player) {
        // Shadow Dagger - Special dagger with stealth ability
        ItemStack dagger = new ItemStack(Material.IRON_SWORD);
        ItemMeta daggerMeta = dagger.getItemMeta();
        if (daggerMeta != null) {
            daggerMeta.setDisplayName(ChatColor.DARK_PURPLE + "Shadow Dagger");
            daggerMeta.setLore(java.util.Arrays.asList(
                ChatColor.GRAY + "A rogue's silent blade",
                ChatColor.YELLOW + "Right-click to activate stealth!",
                ChatColor.BLUE + "Costs: 15 Mana",
                ChatColor.BLUE + "Cooldown: 8 seconds",
                ChatColor.BLUE + "Duration: 5 seconds"
            ));

            // Mark as special weapon
            NamespacedKey key = new NamespacedKey(plugin, "class_weapon");
            daggerMeta.getPersistentDataContainer().set(key, PersistentDataType.STRING, "rogue_dagger");

            dagger.setItemMeta(daggerMeta);
        }

        // Basic leather armor and food
        ItemStack helmet = new ItemStack(Material.LEATHER_HELMET);
        ItemStack chestplate = new ItemStack(Material.LEATHER_CHESTPLATE);
        ItemStack leggings = new ItemStack(Material.LEATHER_LEGGINGS);
        ItemStack boots = new ItemStack(Material.LEATHER_BOOTS);
        ItemStack food = new ItemStack(Material.BREAD, 16);
        ItemStack poison = new ItemStack(Material.SPIDER_EYE, 8);

        player.getInventory().addItem(dagger, helmet, chestplate, leggings, boots, food, poison);
    }

    private void givePaladinItems(Player player) {
        // Holy Mace - Special mace with healing ability
        ItemStack mace = new ItemStack(Material.IRON_AXE);
        ItemMeta maceMeta = mace.getItemMeta();
        if (maceMeta != null) {
            maceMeta.setDisplayName(ChatColor.GOLD + "Holy Mace");
            maceMeta.setLore(java.util.Arrays.asList(
                ChatColor.GRAY + "A paladin's blessed weapon",
                ChatColor.YELLOW + "Right-click to cast heal!",
                ChatColor.BLUE + "Costs: 20 Mana",
                ChatColor.BLUE + "Cooldown: 10 seconds",
                ChatColor.BLUE + "Healing: 6 hearts"
            ));

            // Mark as special weapon
            NamespacedKey key = new NamespacedKey(plugin, "class_weapon");
            maceMeta.getPersistentDataContainer().set(key, PersistentDataType.STRING, "paladin_mace");

            mace.setItemMeta(maceMeta);
        }

        // Iron armor and food
        ItemStack helmet = new ItemStack(Material.IRON_HELMET);
        ItemStack chestplate = new ItemStack(Material.IRON_CHESTPLATE);
        ItemStack leggings = new ItemStack(Material.IRON_LEGGINGS);
        ItemStack boots = new ItemStack(Material.IRON_BOOTS);
        ItemStack food = new ItemStack(Material.BREAD, 16);
        ItemStack shield = new ItemStack(Material.SHIELD);

        player.getInventory().addItem(mace, helmet, chestplate, leggings, boots, food, shield);
    }

    private void giveDruidItems(Player player) {
        // Nature Staff - Special staff with nature bolt ability
        ItemStack staff = new ItemStack(Material.STICK);
        ItemMeta staffMeta = staff.getItemMeta();
        if (staffMeta != null) {
            staffMeta.setDisplayName(ChatColor.GREEN + "Nature Staff");
            staffMeta.setLore(java.util.Arrays.asList(
                ChatColor.GRAY + "A druid's connection to nature",
                ChatColor.YELLOW + "Right-click to cast nature bolt!",
                ChatColor.BLUE + "Costs: 12 Mana",
                ChatColor.BLUE + "Cooldown: 4 seconds",
                ChatColor.BLUE + "Damage: 6"
            ));

            // Mark as special weapon
            NamespacedKey key = new NamespacedKey(plugin, "class_weapon");
            staffMeta.getPersistentDataContainer().set(key, PersistentDataType.STRING, "druid_staff");

            staff.setItemMeta(staffMeta);
        }

        // Basic leather armor and nature items
        ItemStack helmet = new ItemStack(Material.LEATHER_HELMET);
        ItemStack chestplate = new ItemStack(Material.LEATHER_CHESTPLATE);
        ItemStack leggings = new ItemStack(Material.LEATHER_LEGGINGS);
        ItemStack boots = new ItemStack(Material.LEATHER_BOOTS);
        ItemStack food = new ItemStack(Material.BREAD, 16);
        ItemStack seeds = new ItemStack(Material.WHEAT_SEEDS, 32);
        ItemStack saplings = new ItemStack(Material.OAK_SAPLING, 8);

        player.getInventory().addItem(staff, helmet, chestplate, leggings, boots, food, seeds, saplings);
    }

    public PlayerClass getClass(String name) {
        return classes.get(name);
    }
    
    public Set<String> getClassNames() {
        return classes.keySet();
    }
    
    public void reload() {
        loadClasses();
    }

    public void shutdown() {
        // Cleanup if needed
    }
}
