package com.orvyn.mmo.models;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

public class Party {
    
    private final UUID id;
    private final UUID leader;
    private final Set<UUID> members;
    private final long createdTime;
    
    public Party(UUID id, UUID leader) {
        this.id = id;
        this.leader = leader;
        this.members = new HashSet<>();
        this.members.add(leader); // Leader is also a member
        this.createdTime = System.currentTimeMillis();
    }
    
    public UUID getId() { return id; }
    public UUID getLeader() { return leader; }
    public Set<UUID> getMembers() { return members; }
    public long getCreatedTime() { return createdTime; }
    
    public boolean isMember(UUID playerId) {
        return members.contains(playerId);
    }

    public boolean isLeader(UUID playerId) {
        return leader.equals(playerId);
    }

    public int getSize() {
        return members.size();
    }

    public void addMember(UUID playerId) {
        members.add(playerId);
    }

    public void removeMember(UUID playerId) {
        members.remove(playerId);
    }
}
