package com.orvyn.mmo.managers;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import com.orvyn.mmo.models.Waypoint;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerMoveEvent;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class WaypointManager implements Listener {

    private final OrvynMMOPlugin plugin;
    private final Map<String, Waypoint> waypoints = new HashMap<>();
    private final Map<String, Long> teleportCooldowns = new HashMap<>();

    public WaypointManager(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
        loadWaypoints();
    }

    private void loadWaypoints() {
        waypoints.clear();

        FileConfiguration config = plugin.getConfigHub().getConfig("waypoints.yml");
        if (config == null) return;

        for (String waypointName : config.getKeys(false)) {
            ConfigurationSection section = config.getConfigurationSection(waypointName);
            if (section != null) {
                Waypoint waypoint = loadWaypoint(waypointName, section);
                if (waypoint != null) {
                    waypoints.put(waypointName, waypoint);
                }
            }
        }

        plugin.getLogger().info("Loaded " + waypoints.size() + " waypoints");
    }

    private Waypoint loadWaypoint(String name, ConfigurationSection section) {
        try {
            String worldName = section.getString("world");
            double x = section.getDouble("x");
            double y = section.getDouble("y");
            double z = section.getDouble("z");
            float yaw = (float) section.getDouble("yaw", 0);
            float pitch = (float) section.getDouble("pitch", 0);

            Location location = new Location(plugin.getServer().getWorld(worldName), x, y, z, yaw, pitch);

            String displayName = section.getString("display_name", name);
            String description = section.getString("description", "");
            int cost = section.getInt("cost", 0);
            int cooldown = section.getInt("cooldown", 30);
            boolean requiresDiscovery = section.getBoolean("requires_discovery", true);
            double discoveryRadius = section.getDouble("discovery_radius", 5.0);

            return new Waypoint(name, displayName, description, location, cost, cooldown, requiresDiscovery, discoveryRadius);

        } catch (Exception e) {
            plugin.getLogger().warning("Failed to load waypoint " + name + ": " + e.getMessage());
            return null;
        }
    }

    @EventHandler
    public void onPlayerMove(PlayerMoveEvent event) {
        Player player = event.getPlayer();
        Location playerLoc = player.getLocation();

        // Check for waypoint discovery
        for (Waypoint waypoint : waypoints.values()) {
            if (waypoint.requiresDiscovery() && waypoint.getLocation().getWorld().equals(playerLoc.getWorld())) {
                double distance = waypoint.getLocation().distance(playerLoc);

                if (distance <= waypoint.getDiscoveryRadius()) {
                    PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);

                    if (!data.getDiscoveredWaypoints().contains(waypoint.getName())) {
                        discoverWaypoint(player, waypoint);
                    }
                }
            }
        }
    }

    private void discoverWaypoint(Player player, Waypoint waypoint) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        data.getDiscoveredWaypoints().add(waypoint.getName());

        // Visual and audio feedback
        Location loc = waypoint.getLocation();
        loc.getWorld().spawnParticle(Particle.ENCHANT, loc, 50, 2, 2, 2, 0.1);
        player.playSound(player.getLocation(), Sound.BLOCK_ENCHANTMENT_TABLE_USE, 1.0f, 1.5f);

        // Message
        player.sendMessage(ChatColor.GOLD + "✦ Waypoint Discovered: " + waypoint.getDisplayName());
        player.sendMessage(ChatColor.GRAY + waypoint.getDescription());
        player.sendMessage(ChatColor.YELLOW + "Use /ommo waypoints to teleport here!");
    }

    public boolean teleportToWaypoint(Player player, String waypointName) {
        Waypoint waypoint = waypoints.get(waypointName);
        if (waypoint == null) {
            player.sendMessage(ChatColor.RED + "Waypoint not found!");
            return false;
        }

        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);

        // Check if waypoint is discovered
        if (waypoint.requiresDiscovery() && !data.getDiscoveredWaypoints().contains(waypointName)) {
            player.sendMessage(ChatColor.RED + "You haven't discovered this waypoint yet!");
            return false;
        }

        // Check cooldown
        String cooldownKey = player.getUniqueId().toString();
        long currentTime = System.currentTimeMillis();
        if (teleportCooldowns.containsKey(cooldownKey)) {
            long cooldownEnd = teleportCooldowns.get(cooldownKey);
            if (currentTime < cooldownEnd) {
                long remaining = (cooldownEnd - currentTime) / 1000;
                player.sendMessage(ChatColor.RED + "Teleport on cooldown for " + remaining + " seconds!");
                return false;
            }
        }

        // Check cost (mana)
        if (waypoint.getCost() > 0) {
            if (data.getCurrentMana() < waypoint.getCost()) {
                player.sendMessage(ChatColor.RED + "Not enough mana! Requires " + waypoint.getCost() + " mana.");
                return false;
            }
            data.consumeMana(waypoint.getCost());
        }

        // Teleport
        Location teleportLoc = waypoint.getLocation().clone();
        teleportLoc.add(0, 0.5, 0); // Slightly above ground

        // Departure effect
        player.getWorld().spawnParticle(Particle.PORTAL, player.getLocation().add(0, 1, 0), 30, 0.5, 1, 0.5, 0.1);
        player.playSound(player.getLocation(), Sound.BLOCK_PORTAL_TRAVEL, 0.5f, 1.5f);

        // Teleport
        player.teleport(teleportLoc);

        // Arrival effect
        teleportLoc.getWorld().spawnParticle(Particle.PORTAL, teleportLoc.add(0, 1, 0), 30, 0.5, 1, 0.5, 0.1);
        player.playSound(teleportLoc, Sound.BLOCK_PORTAL_TRAVEL, 0.5f, 1.5f);

        // Set cooldown
        teleportCooldowns.put(cooldownKey, currentTime + (waypoint.getCooldown() * 1000L));

        player.sendMessage(ChatColor.GREEN + "Teleported to " + waypoint.getDisplayName() + "!");

        return true;
    }

    public Set<String> getWaypointNames() {
        return waypoints.keySet();
    }

    public Waypoint getWaypoint(String name) {
        return waypoints.get(name);
    }

    public void reload() {
        loadWaypoints();
    }

    public void shutdown() {
        teleportCooldowns.clear();
    }
}
