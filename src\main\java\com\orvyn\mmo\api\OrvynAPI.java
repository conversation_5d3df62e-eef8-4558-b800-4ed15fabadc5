package com.orvyn.mmo.api;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import com.orvyn.mmo.enums.Attribute;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

public class OrvynAPI {
    
    private final OrvynMMOPlugin plugin;
    
    public OrvynAPI(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Get a player's attribute value
     */
    public int getPlayerAttribute(Player player, Attribute attribute) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        return data.getTotalAttribute(attribute);
    }
    
    /**
     * Get player's current HP
     */
    public double getPlayerHP(Player player) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        return data.getCurrentHP();
    }
    
    /**
     * Get player's current Mana
     */
    public double getPlayerMana(Player player) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        return data.getCurrentMana();
    }
    
    /**
     * Get player's class
     */
    public String getPlayerClass(Player player) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        return data.getPlayerClass();
    }
    
    /**
     * Get player's class level
     */
    public int getPlayerLevel(Player player) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        return data.getClassLevel();
    }
    
    /**
     * Use a skill
     */
    public boolean useSkill(Player player, String skillId) {
        return plugin.getSkillManager().useSkill(player, skillId);
    }
    
    /**
     * Give a custom item to player
     */
    public void giveItem(Player player, String itemId) {
        plugin.getItemManager().giveItem(player, itemId);
    }
    
    /**
     * Create a custom item
     */
    public ItemStack createItem(String itemId) {
        return plugin.getItemManager().createItem(itemId);
    }
    
    /**
     * Set player class
     */
    public boolean setPlayerClass(Player player, String className) {
        return plugin.getClassManager().setPlayerClass(player, className);
    }
    
    /**
     * Add experience to player
     */
    public void addExp(Player player, long amount) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        data.addExp(amount);
    }
    
    /**
     * Heal player
     */
    public void healPlayer(Player player, double amount) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        data.heal(amount);
    }
    
    /**
     * Restore player mana
     */
    public void restoreMana(Player player, double amount) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        data.restoreMana(amount);
    }
}
