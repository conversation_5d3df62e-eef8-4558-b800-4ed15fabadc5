package com.orvyn.mmo.managers;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.enums.Attribute;
import com.orvyn.mmo.models.ItemTemplate;
import com.orvyn.mmo.models.ItemTier;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.NamespacedKey;

import java.util.*;

public class ItemManager {
    
    private final OrvynMMOPlugin plugin;
    private final Map<String, ItemTemplate> itemTemplates = new HashMap<>();
    private final Map<String, ItemTier> tiers = new HashMap<>();
    
    private final NamespacedKey itemIdKey;
    private final NamespacedKey tierKey;
    private final NamespacedKey attributesKey;
    
    public ItemManager(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
        this.itemIdKey = new NamespacedKey(plugin, "item_id");
        this.tierKey = new NamespacedKey(plugin, "tier");
        this.attributesKey = new NamespacedKey(plugin, "attributes");
        
        loadTiers();
        loadItems();
    }
    
    public void loadTiers() {
        tiers.clear();
        
        FileConfiguration config = plugin.getConfigHub().getConfig("tiers.yml");
        if (config == null) return;
        
        for (String tierName : config.getKeys(false)) {
            ConfigurationSection tierSection = config.getConfigurationSection(tierName);
            if (tierSection != null) {
                ItemTier tier = loadTier(tierName, tierSection);
                tiers.put(tierName, tier);
            }
        }
        
        plugin.getLogger().info("Loaded " + tiers.size() + " item tiers");
    }
    
    private ItemTier loadTier(String name, ConfigurationSection section) {
        ItemTier tier = new ItemTier(name);
        
        ConfigurationSection rollSection = section.getConfigurationSection("roll");
        if (rollSection != null) {
            tier.setMinRoll(rollSection.getDouble("min", 1.0));
            tier.setMaxRoll(rollSection.getDouble("max", 1.0));
        }
        
        // Set tier color based on name
        switch (name.toLowerCase()) {
            case "common":
                tier.setColor(ChatColor.WHITE);
                break;
            case "uncommon":
                tier.setColor(ChatColor.GREEN);
                break;
            case "rare":
                tier.setColor(ChatColor.BLUE);
                break;
            case "epic":
                tier.setColor(ChatColor.DARK_PURPLE);
                break;
            case "legendary":
                tier.setColor(ChatColor.GOLD);
                break;
            default:
                tier.setColor(ChatColor.GRAY);
        }
        
        return tier;
    }
    
    public void loadItems() {
        itemTemplates.clear();
        
        FileConfiguration config = plugin.getConfigHub().getConfig("items.yml");
        if (config == null) return;
        
        for (String itemId : config.getKeys(false)) {
            ConfigurationSection itemSection = config.getConfigurationSection(itemId);
            if (itemSection != null) {
                ItemTemplate template = loadItemTemplate(itemId, itemSection);
                itemTemplates.put(itemId, template);
            }
        }
        
        plugin.getLogger().info("Loaded " + itemTemplates.size() + " item templates");
    }
    
    private ItemTemplate loadItemTemplate(String id, ConfigurationSection section) {
        ItemTemplate template = new ItemTemplate(id);
        
        // Load basic properties
        String materialName = section.getString("material", "STONE");
        try {
            template.setMaterial(Material.valueOf(materialName));
        } catch (IllegalArgumentException e) {
            plugin.getLogger().warning("Invalid material for item " + id + ": " + materialName);
            template.setMaterial(Material.STONE);
        }
        
        template.setTier(section.getString("tier", "common"));
        template.setDisplayName(section.getString("name", id));
        
        // Load attributes
        ConfigurationSection attributesSection = section.getConfigurationSection("attributes");
        if (attributesSection != null) {
            for (String attrName : attributesSection.getKeys(false)) {
                Attribute attr = Attribute.fromString(attrName);
                if (attr != null) {
                    int value = attributesSection.getInt(attrName);
                    template.getAttributes().put(attr, value);
                }
            }
        }
        
        return template;
    }
    
    public ItemStack createItem(String itemId) {
        return createItem(itemId, null);
    }
    
    public ItemStack createItem(String itemId, String tierOverride) {
        ItemTemplate template = itemTemplates.get(itemId);
        if (template == null) {
            return null;
        }
        
        String tierName = tierOverride != null ? tierOverride : template.getTier();
        ItemTier tier = tiers.get(tierName);
        if (tier == null) {
            tier = tiers.get("common");
        }
        
        // Create base item
        ItemStack item = new ItemStack(template.getMaterial());
        ItemMeta meta = item.getItemMeta();
        
        // Set display name with tier color
        String displayName = tier.getColor() + template.getDisplayName();
        meta.setDisplayName(displayName);

        // Calculate rolled attributes
        Map<Attribute, Integer> rolledAttributes = new HashMap<>();
        Random random = new Random();

        for (Map.Entry<Attribute, Integer> entry : template.getAttributes().entrySet()) {
            Attribute attr = entry.getKey();
            int baseValue = entry.getValue();

            // Apply tier roll
            double roll = tier.getMinRoll() + (tier.getMaxRoll() - tier.getMinRoll()) * random.nextDouble();
            int rolledValue = (int) Math.round(baseValue * roll);

            rolledAttributes.put(attr, rolledValue);
        }

        // Create lore
        List<String> lore = new ArrayList<>();
        lore.add(tier.getColor() + "Tier: " + tierName);
        lore.add("");

        for (Map.Entry<Attribute, Integer> entry : rolledAttributes.entrySet()) {
            Attribute attr = entry.getKey();
            int value = entry.getValue();

            String attrLine = ChatColor.GRAY + "+" + value + " " + attr.getDisplayName();
            lore.add(attrLine);
        }

        meta.setLore(lore);
        
        // Store data in persistent data container
        meta.getPersistentDataContainer().set(itemIdKey, PersistentDataType.STRING, itemId);
        meta.getPersistentDataContainer().set(tierKey, PersistentDataType.STRING, tierName);
        
        // Store attributes as a string (simple serialization)
        StringBuilder attrString = new StringBuilder();
        for (Map.Entry<Attribute, Integer> entry : rolledAttributes.entrySet()) {
            if (attrString.length() > 0) attrString.append(",");
            attrString.append(entry.getKey().name()).append(":").append(entry.getValue());
        }
        meta.getPersistentDataContainer().set(attributesKey, PersistentDataType.STRING, attrString.toString());
        
        item.setItemMeta(meta);
        return item;
    }
    
    public void giveItem(Player player, String itemId) {
        ItemStack item = createItem(itemId);
        if (item != null) {
            player.getInventory().addItem(item);
        }
    }
    
    public Map<Attribute, Integer> getItemAttributes(ItemStack item) {
        Map<Attribute, Integer> attributes = new HashMap<>();
        
        if (item == null || !item.hasItemMeta()) {
            return attributes;
        }
        
        ItemMeta meta = item.getItemMeta();
        String attrString = meta.getPersistentDataContainer().get(attributesKey, PersistentDataType.STRING);
        
        if (attrString != null && !attrString.isEmpty()) {
            String[] pairs = attrString.split(",");
            for (String pair : pairs) {
                String[] parts = pair.split(":");
                if (parts.length == 2) {
                    Attribute attr = Attribute.fromString(parts[0]);
                    if (attr != null) {
                        try {
                            int value = Integer.parseInt(parts[1]);
                            attributes.put(attr, value);
                        } catch (NumberFormatException ignored) {}
                    }
                }
            }
        }
        
        return attributes;
    }
    
    public String getItemId(ItemStack item) {
        if (item == null || !item.hasItemMeta()) {
            return null;
        }
        
        return item.getItemMeta().getPersistentDataContainer().get(itemIdKey, PersistentDataType.STRING);
    }
    
    public ItemTemplate getTemplate(String id) {
        return itemTemplates.get(id);
    }
    
    public ItemTier getTier(String name) {
        return tiers.get(name);
    }
    
    public void reload() {
        loadTiers();
        loadItems();
    }

    public void shutdown() {
        // Cleanup if needed
    }
}
