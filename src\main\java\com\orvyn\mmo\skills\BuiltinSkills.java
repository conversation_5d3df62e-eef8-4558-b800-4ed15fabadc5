package com.orvyn.mmo.skills;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import com.orvyn.mmo.enums.Attribute;
import com.orvyn.mmo.models.Skill;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;

import java.util.List;

public class BuiltinSkills {
    
    public static boolean execute(Player player, String type, Skill skill, OrvynMMOPlugin plugin) {
        switch (type) {
            case "arc_slash":
                return executeArcSlash(player, skill, plugin);
            case "fire_bolt":
                return executeFireBolt(player, skill, plugin);
            case "power_shot":
                return executePowerShot(player, skill, plugin);
            default:
                return false;
        }
    }
    
    private static boolean executeArcSlash(Player player, Skill skill, OrvynMMOPlugin plugin) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        
        // Calculate damage based on STR
        int str = data.getTotalAttribute(Attribute.STR);
        double damage = 8 + str * 0.8;
        
        // Get entities in front of player in an arc
        Location playerLoc = player.getLocation();
        Vector direction = playerLoc.getDirection();
        
        // Create arc effect
        for (int i = -30; i <= 30; i += 10) {
            Vector rotated = rotateVector(direction, Math.toRadians(i));
            Location effectLoc = playerLoc.clone().add(rotated.multiply(3));
            player.getWorld().spawnParticle(Particle.SWEEP_ATTACK, effectLoc, 1);
        }
        
        // Find targets in arc
        List<Entity> nearbyEntities = player.getNearbyEntities(5, 3, 5);
        for (Entity entity : nearbyEntities) {
            if (entity instanceof LivingEntity && entity != player) {
                LivingEntity target = (LivingEntity) entity;
                
                // Check if target is in front of player (simple angle check)
                Vector toTarget = target.getLocation().subtract(playerLoc).toVector().normalize();
                double angle = Math.acos(direction.dot(toTarget));
                
                if (angle <= Math.toRadians(45)) { // 45 degree arc
                    // Deal damage
                    plugin.getCombatManager().dealDamage(player, target, damage);
                }
            }
        }
        
        // Play sound and effects
        player.playSound(playerLoc, Sound.ENTITY_PLAYER_ATTACK_SWEEP, 1.0f, 1.0f);
        
        return true;
    }
    
    private static boolean executeFireBolt(Player player, Skill skill, OrvynMMOPlugin plugin) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        
        // Calculate damage based on INT
        int intel = data.getTotalAttribute(Attribute.INT);
        double damage = 6 + intel * 0.6;
        
        // Create projectile effect
        Location start = player.getEyeLocation();
        Vector direction = start.getDirection();
        
        // Raycast to find target
        for (double d = 0; d < skill.getRange(); d += 0.5) {
            Location current = start.clone().add(direction.clone().multiply(d));
            
            // Spawn particle
            player.getWorld().spawnParticle(Particle.FLAME, current, 1, 0, 0, 0, 0);
            
            // Check for hit
            for (Entity entity : current.getWorld().getNearbyEntities(current, 0.5, 0.5, 0.5)) {
                if (entity instanceof LivingEntity && entity != player) {
                    LivingEntity target = (LivingEntity) entity;
                    
                    // Deal damage
                    plugin.getCombatManager().dealDamage(player, target, damage);
                    
                    // Apply ignite effect
                    target.setFireTicks(60); // 3 seconds
                    
                    // Explosion effect
                    current.getWorld().spawnParticle(Particle.EXPLOSION, current, 1);
                    player.playSound(current, Sound.ENTITY_GENERIC_EXPLODE, 0.5f, 1.5f);
                    
                    return true;
                }
            }
            
            // Check for block collision
            if (current.getBlock().getType().isSolid()) {
                // Hit a block, create explosion effect
                current.getWorld().spawnParticle(Particle.EXPLOSION, current, 1);
                player.playSound(current, Sound.ENTITY_GENERIC_EXPLODE, 0.5f, 1.5f);
                return true;
            }
        }
        
        // Play cast sound
        player.playSound(start, Sound.ENTITY_BLAZE_SHOOT, 1.0f, 1.2f);
        
        return true;
    }

    private static boolean executePowerShot(Player player, Skill skill, OrvynMMOPlugin plugin) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);

        // Calculate damage based on AGI
        int agility = data.getTotalAttribute(Attribute.AGI);
        double damage = 8 + agility * 0.7;

        // Create powerful arrow effect
        Location start = player.getEyeLocation();
        Vector direction = start.getDirection();

        // Raycast to find target with longer range
        for (double d = 0; d < skill.getRange(); d += 0.3) {
            Location current = start.clone().add(direction.clone().multiply(d));

            // Spawn particle trail
            player.getWorld().spawnParticle(Particle.CRIT, current, 2, 0.1, 0.1, 0.1, 0);

            // Check for hit
            for (Entity entity : current.getWorld().getNearbyEntities(current, 0.8, 0.8, 0.8)) {
                if (entity instanceof LivingEntity && entity != player) {
                    LivingEntity target = (LivingEntity) entity;

                    // Deal damage with knockback
                    plugin.getCombatManager().dealDamage(player, target, damage);

                    // Apply knockback
                    Vector knockback = direction.clone().multiply(1.5);
                    knockback.setY(0.3); // Add upward component
                    target.setVelocity(knockback);

                    // Impact effect
                    current.getWorld().spawnParticle(Particle.EXPLOSION, current, 1);
                    player.playSound(current, Sound.ENTITY_ARROW_HIT, 1.0f, 0.8f);

                    return true;
                }
            }

            // Check for block collision
            if (current.getBlock().getType().isSolid()) {
                // Hit a block, create impact effect
                current.getWorld().spawnParticle(Particle.SMOKE, current, 10, 0.3, 0.3, 0.3, 0);
                player.playSound(current, Sound.ENTITY_ARROW_HIT, 1.0f, 0.8f);
                return true;
            }
        }

        // Play shot sound
        player.playSound(start, Sound.ENTITY_ARROW_SHOOT, 1.0f, 0.8f);

        return true;
    }

    private static Vector rotateVector(Vector vector, double angle) {
        double cos = Math.cos(angle);
        double sin = Math.sin(angle);

        double x = vector.getX() * cos - vector.getZ() * sin;
        double z = vector.getX() * sin + vector.getZ() * cos;

        return new Vector(x, vector.getY(), z);
    }
}
