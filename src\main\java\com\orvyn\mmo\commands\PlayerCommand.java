package com.orvyn.mmo.commands;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import com.orvyn.mmo.gui.PlayerManagementGUI;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class PlayerCommand implements CommandExecutor, TabCompleter {
    
    private final OrvynMMOPlugin plugin;
    
    public PlayerCommand(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "Only players can use this command!");
            return true;
        }
        
        Player player = (Player) sender;
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player.getUniqueId());
        
        if (data == null) {
            player.sendMessage(ChatColor.RED + "Player data not found! Please rejoin the server.");
            return true;
        }
        
        if (args.length == 0) {
            // Show deprecation message and redirect
            player.sendMessage(ChatColor.YELLOW + "⚠ " + ChatColor.GOLD + "The /player command is deprecated!");
            player.sendMessage(ChatColor.YELLOW + "Please use " + ChatColor.AQUA + "/mychar" + ChatColor.YELLOW + " instead for the new character hub.");
            player.sendMessage(ChatColor.GRAY + "Opening character hub...");

            // Open new character hub
            com.orvyn.mmo.gui.CharacterHubGUI gui = new com.orvyn.mmo.gui.CharacterHubGUI(plugin, player);
            gui.open();
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        // Show deprecation message for all subcommands
        player.sendMessage(ChatColor.YELLOW + "⚠ " + ChatColor.GOLD + "The /player command is deprecated!");
        player.sendMessage(ChatColor.YELLOW + "Please use " + ChatColor.AQUA + "/mychar" + ChatColor.YELLOW + " instead for the new character hub.");

        switch (subCommand) {
            case "skills":
            case "skill":
                player.sendMessage(ChatColor.GRAY + "Use " + ChatColor.AQUA + "/mychar skills" + ChatColor.GRAY + " instead.");
                com.orvyn.mmo.gui.CharacterHubGUI gui1 = new com.orvyn.mmo.gui.CharacterHubGUI(plugin, player);
                gui1.openSection("skills");
                break;

            case "stats":
            case "attributes":
                player.sendMessage(ChatColor.GRAY + "Use " + ChatColor.AQUA + "/mychar stats" + ChatColor.GRAY + " instead.");
                com.orvyn.mmo.gui.CharacterHubGUI gui2 = new com.orvyn.mmo.gui.CharacterHubGUI(plugin, player);
                gui2.openSection("stats");
                break;

            case "active":
                player.sendMessage(ChatColor.GRAY + "Use " + ChatColor.AQUA + "/mychar skillbar" + ChatColor.GRAY + " instead.");
                com.orvyn.mmo.gui.CharacterHubGUI gui3 = new com.orvyn.mmo.gui.CharacterHubGUI(plugin, player);
                gui3.openSection("skillbar");
                break;

            case "passive":
                player.sendMessage(ChatColor.GRAY + "Use " + ChatColor.AQUA + "/mychar skills" + ChatColor.GRAY + " instead.");
                com.orvyn.mmo.gui.CharacterHubGUI gui4 = new com.orvyn.mmo.gui.CharacterHubGUI(plugin, player);
                gui4.openSection("skills");
                break;

            case "info":
                showPlayerInfo(player, data);
                break;

            default:
                showHelp(player);
                break;
        }
        
        return true;
    }
    
    private void openPlayerManagementGUI(Player player) {
        PlayerManagementGUI gui = new PlayerManagementGUI(plugin, player);
        gui.open();
    }
    
    private void openSkillTreeGUI(Player player) {
        PlayerManagementGUI gui = new PlayerManagementGUI(plugin, player);
        gui.openSkillTree();
    }
    
    private void openStatsGUI(Player player) {
        PlayerManagementGUI gui = new PlayerManagementGUI(plugin, player);
        gui.openStatsAllocation();
    }
    
    private void openActiveSkillsGUI(Player player) {
        PlayerManagementGUI gui = new PlayerManagementGUI(plugin, player);
        gui.openActiveSkills();
    }
    
    private void openPassiveSkillsGUI(Player player) {
        PlayerManagementGUI gui = new PlayerManagementGUI(plugin, player);
        gui.openPassiveSkills();
    }
    
    private void showPlayerInfo(Player player, PlayerData data) {
        player.sendMessage("");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");
        player.sendMessage(ChatColor.YELLOW + "    Player Information");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");
        player.sendMessage(ChatColor.AQUA + "Class: " + ChatColor.WHITE + capitalizeFirst(data.getPlayerClass()));
        player.sendMessage(ChatColor.AQUA + "Level: " + ChatColor.WHITE + data.getClassLevel());
        
        // Calculate XP progress
        if (plugin.getExperienceManager() != null) {
            int progress = plugin.getExperienceManager().getProgressToNextLevel(data);
            player.sendMessage(ChatColor.AQUA + "XP Progress: " + ChatColor.WHITE + progress + "%");
        }
        
        player.sendMessage(ChatColor.AQUA + "Health: " + ChatColor.RED + (int)data.getCurrentHP() + 
                          ChatColor.GRAY + "/" + ChatColor.RED + (int)data.getMaxHP());
        player.sendMessage(ChatColor.AQUA + "Mana: " + ChatColor.BLUE + (int)data.getCurrentMana() + 
                          ChatColor.GRAY + "/" + ChatColor.BLUE + (int)data.getMaxMana());
        
        // Show unlocked skills count
        player.sendMessage(ChatColor.AQUA + "Unlocked Skills: " + ChatColor.WHITE + data.getUnlockedSkills().size());
        
        // Show discovered waypoints
        player.sendMessage(ChatColor.AQUA + "Discovered Waypoints: " + ChatColor.WHITE + data.getDiscoveredWaypoints().size());
        
        // Show completed quests
        player.sendMessage(ChatColor.AQUA + "Completed Quests: " + ChatColor.WHITE + data.getCompletedQuests().size());
        
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");
        player.sendMessage(ChatColor.GRAY + "Use " + ChatColor.YELLOW + "/player" + ChatColor.GRAY + " to open the management GUI");
        player.sendMessage("");
    }
    
    private void showHelp(Player player) {
        player.sendMessage("");
        player.sendMessage(ChatColor.GOLD + "╔═══════════════════════════════════════════════════════════╗");
        player.sendMessage(ChatColor.GOLD + "║" + ChatColor.RED + "                   ⚠️  DEPRECATED COMMAND ⚠️                  " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
        player.sendMessage(ChatColor.GOLD + "║" + ChatColor.YELLOW + "  This command has been replaced with a better system!      " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
        player.sendMessage(ChatColor.GOLD + "║" + ChatColor.AQUA + "                    Use Instead:                          " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
        player.sendMessage(ChatColor.GOLD + "║ " + ChatColor.GREEN + "/mychar" + ChatColor.GRAY + "                - Open the new character hub        " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "║ " + ChatColor.GREEN + "/mychar help" + ChatColor.GRAY + "            - View all available commands           " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
        player.sendMessage(ChatColor.GOLD + "║" + ChatColor.LIGHT_PURPLE + "  The new system is easier to use and has more features!    " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "╚═══════════════════════════════════════════════════════════╝");
        player.sendMessage("");
    }
    
    private String capitalizeFirst(String str) {
        if (str == null || str.isEmpty()) return str;
        return str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase();
    }
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        if (!(sender instanceof Player)) return new ArrayList<>();

        List<String> completions = new ArrayList<>();

        if (args.length == 1) {
            String input = args[0].toLowerCase();
            for (String subCommand : Arrays.asList("skills", "stats", "active", "passive", "info")) {
                if (subCommand.startsWith(input)) {
                    completions.add(subCommand);
                }
            }
        }

        return completions;
    }
}
