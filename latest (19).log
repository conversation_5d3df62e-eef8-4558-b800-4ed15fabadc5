[23:55:05] [ServerMain/INFO]: Environment: Environment[sessionHost=https://sessionserver.mojang.com, servicesHost=https://api.minecraftservices.com, name=PROD]
[23:55:07] [ServerMain/INFO]: Loaded 1373 recipes
[23:55:07] [Server thread/INFO]: Starting minecraft server version 1.21.5
[23:55:07] [Server thread/INFO]: Loading properties
[23:55:07] [Server thread/INFO]: This server is running CraftBukkit version 4503-Spigot-270012a-9119da2 (MC: 1.21.5) (Implementing API version 1.21.5-R0.1-SNAPSHOT)
[23:55:07] [Server thread/INFO]: *** This version of Spigot contains changes to some enums. If you notice that plugins no longer work after updating, please report this to the developers of those plugins first. ***
[23:55:07] [Server thread/INFO]: *** If you cannot update those plugins, you can try setting `settings.compatibility.enum-compatibility-mode` to `true` in `bukkit.yml`. ***
[23:55:07] [Server thread/INFO]: Server Ping Player Sample Count: 12
[23:55:07] [Server thread/INFO]: Using 4 threads for Netty based IO
[23:55:07] [Server thread/INFO]: Debug logging is disabled
[23:55:07] [Server thread/INFO]: Default game type: SURVIVAL
[23:55:07] [Server thread/INFO]: Generating keypair
[23:55:07] [Server thread/INFO]: Starting Minecraft server on 139.5.52.11:25800
[23:55:07] [Server thread/INFO]: Using epoll channel type
[23:55:08] [Server thread/INFO]: [GUIAdminToolsFree] Loading GUIAdminToolsFree v3.2.1
[23:55:08] [Server thread/INFO]: [FastAsyncWorldEdit] Loading FastAsyncWorldEdit v2.13.1
[23:55:08] [Server thread/INFO]: Got request to register class com.sk89q.worldedit.bukkit.BukkitServerInterface with WorldEdit [com.sk89q.worldedit.extension.platform.PlatformManager@7fbd7f88]
[23:55:08] [Server thread/INFO]: [ObsidianWarden] Loading ObsidianWarden v1.0.0
[23:55:08] [Server thread/INFO]: [OrvynMMO] Loading OrvynMMO v0.1.0
[23:55:08] [Server thread/INFO]: [ProtocolLib] Loading ProtocolLib v5.4.0
[23:55:09] [Server thread/INFO]: [packetevents] Loading packetevents v2.9.5
[23:55:09] [Server thread/INFO]: [LibsDisguises] Loading LibsDisguises v11.0.6
[23:55:09] [Server thread/INFO]: [BossScenes] Loading BossScenes v1.0.0
[23:55:09] [Server thread/INFO]: [OrvynMobs] Loading OrvynMobs v1.0.0-SNAPSHOT
[23:55:09] [Server thread/INFO]: [OrvynMobs] Loading OrvynMobs v1.0.0-SNAPSHOT
[23:55:09] [Server thread/ERROR]: com/orvyn/shaded/kotlin/jvm/internal/Intrinsics initializing OrvynMobs v1.0.0-SNAPSHOT (Is it up to date?)
java.lang.NoClassDefFoundError: com/orvyn/shaded/kotlin/jvm/internal/Intrinsics
	at com.orvyn.mobs.OrvynMobsPlugin.onLoad(OrvynMobsPlugin.kt:53) ~[?:?]
	at org.bukkit.craftbukkit.v1_21_R4.CraftServer.loadPlugins(CraftServer.java:472) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.server.dedicated.DedicatedServer.e(DedicatedServer.java:235) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.server.MinecraftServer.y(MinecraftServer.java:1007) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.server.MinecraftServer.lambda$spin$0(MinecraftServer.java:325) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at java.base/java.lang.Thread.run(Thread.java:1583) [?:?]
Caused by: java.lang.ClassNotFoundException: com.orvyn.shaded.kotlin.jvm.internal.Intrinsics
	at org.bukkit.plugin.java.PluginClassLoader.loadClass0(PluginClassLoader.java:160) ~[spigot-api-1.21.5-R0.1-SNAPSHOT.jar:?]
	at org.bukkit.plugin.java.PluginClassLoader.loadClass(PluginClassLoader.java:112) ~[spigot-api-1.21.5-R0.1-SNAPSHOT.jar:?]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526) ~[?:?]
	... 6 more
[23:55:09] [Server thread/INFO]: [SoapsMobs] Loading SoapsMobs v1.0.0
[23:55:09] [Server thread/INFO]: [SoapsDungeons] Loading SoapsDungeons v0.2.2
[23:55:09] [Server thread/INFO]: [FastAsyncWorldEdit] Enabling FastAsyncWorldEdit v2.13.1
[23:55:09] [Server thread/INFO]: LZ4 Compression Binding loaded successfully
[23:55:09] [Server thread/INFO]: ZSTD Compression Binding loaded successfully
[23:55:09] [Server thread/INFO]: Registering commands with com.sk89q.worldedit.bukkit.BukkitServerInterface
[23:55:09] [Server thread/INFO]: WEPIF: Using the Bukkit Permissions API.
[23:55:09] [Server thread/INFO]: Using com.sk89q.worldedit.bukkit.adapter.impl.fawe.v1_21_5.PaperweightFaweAdapter as the Bukkit adapter
[23:55:10] [Server thread/INFO]: [ProtocolLib] Enabling ProtocolLib v5.4.0
[23:55:10] [Server thread/INFO]: [OrvynMobs] Enabling OrvynMobs v1.0.0-SNAPSHOT
[23:55:10] [Server thread/INFO]: [OrvynMobs] Enabling OrvynMobs - The MythicMobs killer!
[23:55:10] [Server thread/INFO]: [OrvynMobs] Initializing core systems...
[23:55:10] [Server thread/ERROR]: Error occurred while enabling OrvynMobs v1.0.0-SNAPSHOT (Is it up to date?)
java.lang.NoClassDefFoundError: com/orvyn/shaded/kotlin/jvm/internal/Intrinsics
	at com.orvyn.mobs.core.scheduler.RegionScheduler.<init>(RegionScheduler.kt) ~[?:?]
	at com.orvyn.mobs.OrvynMobsPlugin.initializeCoreSystems(OrvynMobsPlugin.kt:131) ~[?:?]
	at com.orvyn.mobs.OrvynMobsPlugin.onEnable(OrvynMobsPlugin.kt:70) ~[?:?]
	at org.bukkit.plugin.java.JavaPlugin.setEnabled(JavaPlugin.java:267) ~[spigot-api-1.21.5-R0.1-SNAPSHOT.jar:?]
	at org.bukkit.plugin.java.JavaPluginLoader.enablePlugin(JavaPluginLoader.java:342) ~[spigot-api-1.21.5-R0.1-SNAPSHOT.jar:?]
	at org.bukkit.plugin.SimplePluginManager.enablePlugin(SimplePluginManager.java:492) ~[spigot-api-1.21.5-R0.1-SNAPSHOT.jar:?]
	at org.bukkit.craftbukkit.v1_21_R4.CraftServer.enablePlugin(CraftServer.java:578) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at org.bukkit.craftbukkit.v1_21_R4.CraftServer.enablePlugins(CraftServer.java:492) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.server.dedicated.DedicatedServer.e(DedicatedServer.java:236) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.server.MinecraftServer.y(MinecraftServer.java:1007) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.server.MinecraftServer.lambda$spin$0(MinecraftServer.java:325) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at java.base/java.lang.Thread.run(Thread.java:1583) [?:?]
Caused by: java.lang.ClassNotFoundException: com.orvyn.shaded.kotlin.jvm.internal.Intrinsics
	at org.bukkit.plugin.java.PluginClassLoader.loadClass0(PluginClassLoader.java:160) ~[spigot-api-1.21.5-R0.1-SNAPSHOT.jar:?]
	at org.bukkit.plugin.java.PluginClassLoader.loadClass(PluginClassLoader.java:112) ~[spigot-api-1.21.5-R0.1-SNAPSHOT.jar:?]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526) ~[?:?]
	... 12 more
[23:55:10] [Server thread/INFO]: Preparing level "world"
[23:55:10] [Server thread/INFO]: -------- World Settings For [world] --------
[23:55:10] [Server thread/INFO]: Mob Spawn Range: 6
[23:55:10] [Server thread/INFO]: Entity Activation Range: An 32 / Mo 32 / Ra 48 / Mi 16 / Tiv true / Isa false
[23:55:10] [Server thread/INFO]: Entity Tracking Range: Pl 48 / An 48 / Mo 48 / Mi 32 / Di 128 / Other 64
[23:55:10] [Server thread/INFO]: Hopper Transfer: 8 Hopper Check: 1 Hopper Amount: 1 Hopper Can Load Chunks: false
[23:55:10] [Server thread/INFO]: Custom Map Seeds:  Village: 10387312 Desert: 14357617 Igloo: 14357618 Jungle: 14357619 Swamp: 14357620 Monument: 10387313 Ocean: 14357621 Shipwreck: 165745295 End City: 10387313 Slime: 987234911 Nether: 30084232 Mansion: 10387319 Fossil: 14357921 Portal: 34222645
[23:55:10] [Server thread/INFO]: Experience Merge Radius: 3.0
[23:55:10] [Server thread/INFO]: Cactus Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Cane Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Melon Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Mushroom Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Pumpkin Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Sapling Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Beetroot Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Carrot Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Potato Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Wheat Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: NetherWart Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Vine Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Cocoa Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Bamboo Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: SweetBerry Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Kelp Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: TwistingVines Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: WeepingVines Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: CaveVines Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Max TNT Explosions: 100
[23:55:10] [Server thread/INFO]: Tile Max Tick Time: 50ms Entity max Tick Time: 50ms
[23:55:10] [Server thread/INFO]: View Distance: 10
[23:55:10] [Server thread/INFO]: Simulation Distance: 10
[23:55:10] [Server thread/INFO]: Item Despawn Rate: 6000
[23:55:10] [Server thread/INFO]: Item Merge Radius: 2.5
[23:55:10] [Server thread/INFO]: Arrow Despawn Rate: 1200 Trident Respawn Rate:1200
[23:55:10] [Server thread/INFO]: Allow Zombie Pigmen to spawn from portal blocks: true
[23:55:10] [Server thread/INFO]: Zombie Aggressive Towards Villager: true
[23:55:10] [Server thread/INFO]: Nerfing mobs spawned from spawners: false
[23:55:10] [Server thread/INFO]: -------- World Settings For [world_nether] --------
[23:55:10] [Server thread/INFO]: Mob Spawn Range: 6
[23:55:10] [Server thread/INFO]: Entity Activation Range: An 32 / Mo 32 / Ra 48 / Mi 16 / Tiv true / Isa false
[23:55:10] [Server thread/INFO]: Entity Tracking Range: Pl 48 / An 48 / Mo 48 / Mi 32 / Di 128 / Other 64
[23:55:10] [Server thread/INFO]: Hopper Transfer: 8 Hopper Check: 1 Hopper Amount: 1 Hopper Can Load Chunks: false
[23:55:10] [Server thread/INFO]: Custom Map Seeds:  Village: 10387312 Desert: 14357617 Igloo: 14357618 Jungle: 14357619 Swamp: 14357620 Monument: 10387313 Ocean: 14357621 Shipwreck: 165745295 End City: 10387313 Slime: 987234911 Nether: 30084232 Mansion: 10387319 Fossil: 14357921 Portal: 34222645
[23:55:10] [Server thread/INFO]: Experience Merge Radius: 3.0
[23:55:10] [Server thread/INFO]: Cactus Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Cane Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Melon Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Mushroom Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Pumpkin Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Sapling Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Beetroot Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Carrot Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Potato Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Wheat Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: NetherWart Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Vine Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Cocoa Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Bamboo Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: SweetBerry Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Kelp Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: TwistingVines Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: WeepingVines Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: CaveVines Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Max TNT Explosions: 100
[23:55:10] [Server thread/INFO]: Tile Max Tick Time: 50ms Entity max Tick Time: 50ms
[23:55:10] [Server thread/INFO]: View Distance: 10
[23:55:10] [Server thread/INFO]: Simulation Distance: 10
[23:55:10] [Server thread/INFO]: Item Despawn Rate: 6000
[23:55:10] [Server thread/INFO]: Item Merge Radius: 2.5
[23:55:10] [Server thread/INFO]: Arrow Despawn Rate: 1200 Trident Respawn Rate:1200
[23:55:10] [Server thread/INFO]: Allow Zombie Pigmen to spawn from portal blocks: true
[23:55:10] [Server thread/INFO]: Zombie Aggressive Towards Villager: true
[23:55:10] [Server thread/INFO]: Nerfing mobs spawned from spawners: false
[23:55:10] [Server thread/INFO]: -------- World Settings For [world_the_end] --------
[23:55:10] [Server thread/INFO]: Mob Spawn Range: 6
[23:55:10] [Server thread/INFO]: Entity Activation Range: An 32 / Mo 32 / Ra 48 / Mi 16 / Tiv true / Isa false
[23:55:10] [Server thread/INFO]: Entity Tracking Range: Pl 48 / An 48 / Mo 48 / Mi 32 / Di 128 / Other 64
[23:55:10] [Server thread/INFO]: Hopper Transfer: 8 Hopper Check: 1 Hopper Amount: 1 Hopper Can Load Chunks: false
[23:55:10] [Server thread/INFO]: Custom Map Seeds:  Village: 10387312 Desert: 14357617 Igloo: 14357618 Jungle: 14357619 Swamp: 14357620 Monument: 10387313 Ocean: 14357621 Shipwreck: 165745295 End City: 10387313 Slime: 987234911 Nether: 30084232 Mansion: 10387319 Fossil: 14357921 Portal: 34222645
[23:55:10] [Server thread/INFO]: Experience Merge Radius: 3.0
[23:55:10] [Server thread/INFO]: Cactus Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Cane Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Melon Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Mushroom Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Pumpkin Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Sapling Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Beetroot Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Carrot Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Potato Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Wheat Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: NetherWart Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Vine Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Cocoa Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Bamboo Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: SweetBerry Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Kelp Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: TwistingVines Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: WeepingVines Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: CaveVines Growth Modifier: 100%
[23:55:10] [Server thread/INFO]: Max TNT Explosions: 100
[23:55:10] [Server thread/INFO]: Tile Max Tick Time: 50ms Entity max Tick Time: 50ms
[23:55:10] [Server thread/INFO]: View Distance: 10
[23:55:10] [Server thread/INFO]: Simulation Distance: 10
[23:55:10] [Server thread/INFO]: Item Despawn Rate: 6000
[23:55:10] [Server thread/INFO]: Item Merge Radius: 2.5
[23:55:10] [Server thread/INFO]: Arrow Despawn Rate: 1200 Trident Respawn Rate:1200
[23:55:10] [Server thread/INFO]: Allow Zombie Pigmen to spawn from portal blocks: true
[23:55:10] [Server thread/INFO]: Zombie Aggressive Towards Villager: true
[23:55:10] [Server thread/INFO]: Nerfing mobs spawned from spawners: false
[23:55:10] [Server thread/INFO]: Preparing start region for dimension minecraft:overworld
[23:55:10] [Thread-5/WARN]: A new release for FastAsyncWorldEdit is available: 2.13.2. You are currently on 2.13.1.
Download from https://modrinth.com/plugin/fastasyncworldedit, https://hangar.papermc.io/IntellectualSites/FastAsyncWorldEdit or https://www.spigotmc.org/resources/13932
[23:55:11] [Thread-6/WARN]: An update for FastAsyncWorldEdit is available. You are 1175 build(s) out of date.
You are running build 0, the latest version is build 1175.
Update at https://ci.athion.net/job/FastAsyncWorldEdit
[23:55:18] [Worker-Main-1/INFO]: Preparing spawn area: 0%
[23:55:18] [Worker-Main-1/INFO]: Preparing spawn area: 0%
[23:55:18] [Worker-Main-1/INFO]: Preparing spawn area: 0%
[23:55:18] [Worker-Main-1/INFO]: Preparing spawn area: 0%
[23:55:18] [Worker-Main-1/INFO]: Preparing spawn area: 0%
[23:55:18] [Worker-Main-1/INFO]: Preparing spawn area: 0%
[23:55:18] [Worker-Main-1/INFO]: Preparing spawn area: 0%
[23:55:18] [Worker-Main-1/INFO]: Preparing spawn area: 0%
[23:55:18] [Worker-Main-1/INFO]: Preparing spawn area: 0%
[23:55:18] [Worker-Main-1/INFO]: Preparing spawn area: 0%
[23:55:18] [Worker-Main-1/INFO]: Preparing spawn area: 0%
[23:55:18] [Worker-Main-1/INFO]: Preparing spawn area: 0%
[23:55:18] [Worker-Main-1/INFO]: Preparing spawn area: 0%
[23:55:18] [Worker-Main-1/INFO]: Preparing spawn area: 0%
[23:55:18] [Worker-Main-1/INFO]: Preparing spawn area: 0%
[23:55:18] [Worker-Main-1/INFO]: Preparing spawn area: 0%
[23:55:18] [Server thread/INFO]: Time elapsed: 8086 ms
[23:55:18] [Server thread/INFO]: Preparing start region for dimension minecraft:the_nether
[23:55:18] [Worker-Main-1/INFO]: Preparing spawn area: 0%
[23:55:19] [Server thread/INFO]: Time elapsed: 130 ms
[23:55:19] [Server thread/INFO]: Preparing start region for dimension minecraft:the_end
[23:55:19] [Worker-Main-1/INFO]: Preparing spawn area: 0%
[23:55:19] [Server thread/INFO]: Time elapsed: 76 ms
[23:55:19] [Server thread/INFO]: [GUIAdminToolsFree] Enabling GUIAdminToolsFree v3.2.1
[23:55:19] [Server thread/INFO]: -----GUI ADMIN TOOLS FREE-----
[23:55:19] [Server thread/INFO]:  
[23:55:19] [Server thread/INFO]: VERSION: 3.2.1
[23:55:19] [Server thread/INFO]:  
[23:55:19] [Server thread/INFO]: LOADING ALL SERVICES...
[23:55:19] [Server thread/INFO]: LOADED ALL SERVICES
[23:55:19] [Server thread/INFO]:  
[23:55:19] [Server thread/INFO]: LOADED METRICS
[23:55:19] [Server thread/INFO]:  
[23:55:19] [Server thread/INFO]: LOADING SOFT DEPENDENCIES...
[23:55:19] [Server thread/INFO]: NO JoinLeaveManager
[23:55:19] [Server thread/INFO]:  
[23:55:19] [Server thread/INFO]: -----GUI ADMIN TOOLS FREE-----
[23:55:19] [Server thread/INFO]: [ObsidianWarden] Enabling ObsidianWarden v1.0.0
[23:55:19] [Server thread/INFO]: [ObsidianWarden] Configuration validated successfully
[23:55:19] [Server thread/INFO]: [ObsidianWarden] Cleaned up orphaned display entities from previous sessions
[23:55:19] [Server thread/INFO]: [ObsidianWarden] WardenBossManager initialized
[23:55:19] [Server thread/INFO]: [ObsidianWarden] Obsidian Warden plugin has been enabled successfully!
[23:55:19] [Server thread/INFO]: [ObsidianWarden] Version: 1.0.0
[23:55:19] [Server thread/INFO]: [ObsidianWarden] Use /warden spawn to create your first Obsidian Warden boss!
[23:55:19] [Server thread/INFO]: [OrvynMMO] Enabling OrvynMMO v0.1.0
[23:55:19] [Server thread/INFO]: [OrvynMMO] Starting OrvynMMO ALL v0.1.0
[23:55:19] [Server thread/INFO]: [OrvynMMO] Initializing core systems...
[23:55:19] [Server thread/INFO]: [OrvynMMO] Loading configurations...
[23:55:19] [Server thread/INFO]: [OrvynMMO] Loaded config: config.yml
[23:55:19] [Server thread/INFO]: [OrvynMMO] Loaded config: classes.yml
[23:55:19] [Server thread/INFO]: [OrvynMMO] Loaded config: skills.yml
[23:55:19] [Server thread/INFO]: [OrvynMMO] Loaded config: items.yml
[23:55:19] [Server thread/INFO]: [OrvynMMO] Loaded config: tiers.yml
[23:55:19] [Server thread/INFO]: [OrvynMMO] Loaded config: professions.yml
[23:55:19] [Server thread/INFO]: [OrvynMMO] Loaded config: waypoints.yml
[23:55:19] [Server thread/INFO]: [OrvynMMO] Loaded config: quests.yml
[23:55:19] [Server thread/INFO]: [OrvynMMO] Loaded config: drops.yml
[23:55:19] [Server thread/INFO]: [OrvynMMO] Loaded config: spawners.yml
[23:55:19] [Server thread/INFO]: [OrvynMMO] Loaded config: effects.yml
[23:55:19] [Server thread/INFO]: [OrvynMMO] Loaded config: npcs.yml
[23:55:19] [Server thread/INFO]: [OrvynMMO] Loaded config: ui.yml
[23:55:19] [Server thread/INFO]: [OrvynMMO] Initializing managers...
[23:55:19] [Server thread/INFO]: [OrvynMMO] Initializing PlayerDataManager...
[23:55:19] [Server thread/INFO]: [OrvynMMO] Initializing ClassManager...
[23:55:19] [Server thread/INFO]: [OrvynMMO] Loaded 3 classes
[23:55:19] [Server thread/INFO]: [OrvynMMO] Initializing SkillManager...
[23:55:19] [Server thread/INFO]: [OrvynMMO] Loaded 3 skills
[23:55:19] [Server thread/INFO]: [OrvynMMO] Initializing ItemManager...
[23:55:19] [Server thread/INFO]: [OrvynMMO] Loaded 5 item tiers
[23:55:19] [Server thread/INFO]: [OrvynMMO] Loaded 5 item templates
[23:55:19] [Server thread/INFO]: [OrvynMMO] Initializing ProfessionManager...
[23:55:19] [Server thread/INFO]: [OrvynMMO] Initializing WaypointManager...
[23:55:19] [Server thread/INFO]: [OrvynMMO] Loaded 4 waypoints
[23:55:19] [Server thread/INFO]: [OrvynMMO] Initializing QuestManager...
[23:55:19] [Server thread/INFO]: [OrvynMMO] Loaded 3 quests
[23:55:19] [Server thread/INFO]: [OrvynMMO] Initializing DropManager...
[23:55:19] [Server thread/INFO]: [OrvynMMO] Initializing SpawnerManager...
[23:55:19] [Server thread/WARN]: [OrvynMMO] Spawners configuration not found! Skipping spawner loading.
[23:55:19] [Server thread/INFO]: [OrvynMMO] Loaded 0 spawners
[23:55:19] [Server thread/INFO]: [OrvynMMO] Initializing CombatManager...
[23:55:19] [Server thread/INFO]: [OrvynMMO] Initializing EffectManager...
[23:55:19] [Server thread/ERROR]: [OrvynMMO] Failed to initialize managers: Cannot invoke "org.bukkit.configuration.file.FileConfiguration.getKeys(boolean)" because "config" is null
[23:55:19] [Server thread/WARN]: java.lang.NullPointerException: Cannot invoke "org.bukkit.configuration.file.FileConfiguration.getKeys(boolean)" because "config" is null
[23:55:19] [Server thread/WARN]: 	at com.orvyn.mmo.managers.EffectManager.loadEffects(EffectManager.java:42)
[23:55:19] [Server thread/WARN]: 	at com.orvyn.mmo.managers.EffectManager.reload(EffectManager.java:35)
[23:55:19] [Server thread/WARN]: 	at com.orvyn.mmo.managers.EffectManager.<init>(EffectManager.java:29)
[23:55:19] [Server thread/WARN]: 	at com.orvyn.mmo.OrvynMMOPlugin.initializeManagers(OrvynMMOPlugin.java:153)
[23:55:19] [Server thread/WARN]: 	at com.orvyn.mmo.OrvynMMOPlugin.onEnable(OrvynMMOPlugin.java:59)
[23:55:19] [Server thread/WARN]: 	at org.bukkit.plugin.java.JavaPlugin.setEnabled(JavaPlugin.java:267)
[23:55:19] [Server thread/WARN]: 	at org.bukkit.plugin.java.JavaPluginLoader.enablePlugin(JavaPluginLoader.java:342)
[23:55:19] [Server thread/WARN]: 	at org.bukkit.plugin.SimplePluginManager.enablePlugin(SimplePluginManager.java:492)
[23:55:19] [Server thread/WARN]: 	at org.bukkit.craftbukkit.v1_21_R4.CraftServer.enablePlugin(CraftServer.java:578)
[23:55:19] [Server thread/WARN]: 	at org.bukkit.craftbukkit.v1_21_R4.CraftServer.enablePlugins(CraftServer.java:492)
[23:55:19] [Server thread/WARN]: 	at net.minecraft.server.MinecraftServer.loadWorld0(MinecraftServer.java:652)
[23:55:19] [Server thread/WARN]: 	at net.minecraft.server.MinecraftServer.loadLevel(MinecraftServer.java:437)
[23:55:19] [Server thread/WARN]: 	at net.minecraft.server.dedicated.DedicatedServer.e(DedicatedServer.java:268)
[23:55:19] [Server thread/WARN]: 	at net.minecraft.server.MinecraftServer.y(MinecraftServer.java:1007)
[23:55:19] [Server thread/WARN]: 	at net.minecraft.server.MinecraftServer.lambda$spin$0(MinecraftServer.java:325)
[23:55:19] [Server thread/WARN]: 	at java.base/java.lang.Thread.run(Thread.java:1583)
[23:55:19] [Server thread/ERROR]: [OrvynMMO] Failed to enable OrvynMMO ALL
java.lang.NullPointerException: Cannot invoke "org.bukkit.configuration.file.FileConfiguration.getKeys(boolean)" because "config" is null
	at com.orvyn.mmo.managers.EffectManager.loadEffects(EffectManager.java:42) ~[?:?]
	at com.orvyn.mmo.managers.EffectManager.reload(EffectManager.java:35) ~[?:?]
	at com.orvyn.mmo.managers.EffectManager.<init>(EffectManager.java:29) ~[?:?]
	at com.orvyn.mmo.OrvynMMOPlugin.initializeManagers(OrvynMMOPlugin.java:153) ~[?:?]
	at com.orvyn.mmo.OrvynMMOPlugin.onEnable(OrvynMMOPlugin.java:59) ~[?:?]
	at org.bukkit.plugin.java.JavaPlugin.setEnabled(JavaPlugin.java:267) ~[spigot-api-1.21.5-R0.1-SNAPSHOT.jar:?]
	at org.bukkit.plugin.java.JavaPluginLoader.enablePlugin(JavaPluginLoader.java:342) ~[spigot-api-1.21.5-R0.1-SNAPSHOT.jar:?]
	at org.bukkit.plugin.SimplePluginManager.enablePlugin(SimplePluginManager.java:492) ~[spigot-api-1.21.5-R0.1-SNAPSHOT.jar:?]
	at org.bukkit.craftbukkit.v1_21_R4.CraftServer.enablePlugin(CraftServer.java:578) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at org.bukkit.craftbukkit.v1_21_R4.CraftServer.enablePlugins(CraftServer.java:492) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.server.MinecraftServer.loadWorld0(MinecraftServer.java:652) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.server.MinecraftServer.loadLevel(MinecraftServer.java:437) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.server.dedicated.DedicatedServer.e(DedicatedServer.java:268) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.server.MinecraftServer.y(MinecraftServer.java:1007) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.server.MinecraftServer.lambda$spin$0(MinecraftServer.java:325) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at java.base/java.lang.Thread.run(Thread.java:1583) [?:?]
[23:55:19] [Server thread/WARN]: java.lang.NullPointerException: Cannot invoke "org.bukkit.configuration.file.FileConfiguration.getKeys(boolean)" because "config" is null
[23:55:19] [Server thread/WARN]: 	at com.orvyn.mmo.managers.EffectManager.loadEffects(EffectManager.java:42)
[23:55:19] [Server thread/WARN]: 	at com.orvyn.mmo.managers.EffectManager.reload(EffectManager.java:35)
[23:55:19] [Server thread/WARN]: 	at com.orvyn.mmo.managers.EffectManager.<init>(EffectManager.java:29)
[23:55:19] [Server thread/WARN]: 	at com.orvyn.mmo.OrvynMMOPlugin.initializeManagers(OrvynMMOPlugin.java:153)
[23:55:19] [Server thread/WARN]: 	at com.orvyn.mmo.OrvynMMOPlugin.onEnable(OrvynMMOPlugin.java:59)
[23:55:19] [Server thread/WARN]: 	at org.bukkit.plugin.java.JavaPlugin.setEnabled(JavaPlugin.java:267)
[23:55:19] [Server thread/WARN]: 	at org.bukkit.plugin.java.JavaPluginLoader.enablePlugin(JavaPluginLoader.java:342)
[23:55:19] [Server thread/WARN]: 	at org.bukkit.plugin.SimplePluginManager.enablePlugin(SimplePluginManager.java:492)
[23:55:19] [Server thread/WARN]: 	at org.bukkit.craftbukkit.v1_21_R4.CraftServer.enablePlugin(CraftServer.java:578)
[23:55:19] [Server thread/WARN]: 	at org.bukkit.craftbukkit.v1_21_R4.CraftServer.enablePlugins(CraftServer.java:492)
[23:55:19] [Server thread/WARN]: 	at net.minecraft.server.MinecraftServer.loadWorld0(MinecraftServer.java:652)
[23:55:19] [Server thread/WARN]: 	at net.minecraft.server.MinecraftServer.loadLevel(MinecraftServer.java:437)
[23:55:19] [Server thread/WARN]: 	at net.minecraft.server.dedicated.DedicatedServer.e(DedicatedServer.java:268)
[23:55:19] [Server thread/WARN]: 	at net.minecraft.server.MinecraftServer.y(MinecraftServer.java:1007)
[23:55:19] [Server thread/WARN]: 	at net.minecraft.server.MinecraftServer.lambda$spin$0(MinecraftServer.java:325)
[23:55:19] [Server thread/WARN]: 	at java.base/java.lang.Thread.run(Thread.java:1583)
[23:55:19] [Server thread/INFO]: [OrvynMMO] Disabling OrvynMMO v0.1.0
[23:55:19] [Server thread/INFO]: [OrvynMMO] Disabling OrvynMMO ALL...
[23:55:19] [Server thread/INFO]: [OrvynMMO] OrvynMMO ALL disabled successfully!
[23:55:19] [Server thread/INFO]: [packetevents] Enabling packetevents v2.9.5
[23:55:19] [packetevents-update-check-thread/INFO]: [packetevents] Checking for updates, please wait...
[23:55:19] [Server thread/INFO]: [LibsDisguises] Enabling LibsDisguises v11.0.6
[23:55:19] [Server thread/INFO]: [LibsDisguises] File Name: LibsDisguises-11.0.6.jar
[23:55:19] [Server thread/INFO]: [LibsDisguises] Discovered nms version: (Package: v1_21_R4) (LD: v1_21_R4) (MC: 1.21.5)
[23:55:19] [Server thread/INFO]: [LibsDisguises] Jenkins Build: #1594
[23:55:19] [Server thread/INFO]: [LibsDisguises] Build Date: 21/05/2025 07:32
[23:55:19] [Server thread/INFO]: [LibsDisguises] If you own the plugin, place the premium jar downloaded from https://www.spigotmc.org/resources/libs-disguises.32453/ in plugins/LibsDisguises/
[23:55:19] [Server thread/INFO]: [LibsDisguises] You are running the free version, commands limited to non-players and operators. (Console, Command Blocks, Admins)
[23:55:19] [Server thread/INFO]: [LibsDisguises] Config 'TallSelfDisguises' is set to 'SCALED', LD will scale down (when possible) oversized disguises from self disguise. https://www.spigotmc.org/wiki/lib-s-disguises-faq/#tall-disguises-self-disguises
[23:55:19] [packetevents-update-check-thread/INFO]: [packetevents] You are running the latest release of PacketEvents. Your build: (2.9.5)
[23:55:20] [Server thread/INFO]: Loading block mappings for V_1_21_5/18...
[23:55:20] [Server thread/INFO]: Finished loading block mappings for V_1_21_5/18 in 518.157426ms
[23:55:21] [Server thread/INFO]: [LibsDisguises] Loaded custom disguise libraryaddict
[23:55:21] [Server thread/INFO]: [LibsDisguises] Loaded 1 custom disguise
[23:55:21] [Server thread/INFO]: [LibsDisguises] Config is up to date!
[23:55:21] [Server thread/INFO]: [BossScenes] Enabling BossScenes v1.0.0
[23:55:21] [Server thread/INFO]: [BossScenes] Registered 6 skill types
[23:55:21] [Server thread/INFO]: [BossScenes] Registered 6 epic skills!
[23:55:21] [Server thread/INFO]: [BossScenes] LibsDisguises detected - PlayerBoss will use realistic player disguises
[23:55:21] [Server thread/INFO]: [BossScenes] Registered boss type: warden_champion
[23:55:21] [Server thread/INFO]: [BossScenes] Registered boss type: glass_reaver
[23:55:21] [Server thread/INFO]: [BossScenes] BossScenes v1.0.0 enabled!
[23:55:21] [Server thread/INFO]: [BossScenes] Registered 2 boss types
[23:55:21] [Server thread/INFO]: [SoapsMobs] Enabling SoapsMobs v1.0.0
[23:55:21] [Server thread/INFO]: [SoapsMobs] Loaded 12 mobs: abyss_warden, tempest_seraph, ember_sovereign, glacial_matriarch, prism_tyrant, infernos_lord_of_flames, nyx_void_enchantress, magma_creeper, frost_guard, storm_runner
[23:55:21] [Server thread/INFO]: [SoapsMobs] Loaded items: [starborne_aegis, thunderstep_boots, riftwalkers_prism, void_lantern, gale_cloak, stone_kings_gauntlet, glacial_brand, flame_sword, lightning_wand, ember_boots, frost_wand, storm_sceptre, magma_gauntlet, thunder_amulet, inferno_blade, gale_boots, void_charm, prism_ring, earth_totem, tidal_boots, star_crown, smite_hammer, jump_blast_boots, stormcall_wand]
[23:55:21] [Server thread/INFO]: [SoapsMobs] SoapsMobs enabled.
[23:55:21] [Server thread/INFO]: [SoapsDungeons] Enabling SoapsDungeons v0.2.2
[23:55:21] [Server thread/WARN]: [SoapsDungeons] Could not save messages.yml to plugins/SoapsDungeons/messages.yml because messages.yml already exists.
[23:55:21] [Server thread/INFO]: -------- World Settings For [sd_instances] --------
[23:55:21] [Server thread/INFO]: Mob Spawn Range: 6
[23:55:21] [Server thread/INFO]: Entity Activation Range: An 32 / Mo 32 / Ra 48 / Mi 16 / Tiv true / Isa false
[23:55:21] [Server thread/INFO]: Entity Tracking Range: Pl 48 / An 48 / Mo 48 / Mi 32 / Di 128 / Other 64
[23:55:21] [Server thread/INFO]: Hopper Transfer: 8 Hopper Check: 1 Hopper Amount: 1 Hopper Can Load Chunks: false
[23:55:21] [Server thread/INFO]: Custom Map Seeds:  Village: 10387312 Desert: 14357617 Igloo: 14357618 Jungle: 14357619 Swamp: 14357620 Monument: 10387313 Ocean: 14357621 Shipwreck: 165745295 End City: 10387313 Slime: 987234911 Nether: 30084232 Mansion: 10387319 Fossil: 14357921 Portal: 34222645
[23:55:21] [Server thread/INFO]: Experience Merge Radius: 3.0
[23:55:21] [Server thread/INFO]: Cactus Growth Modifier: 100%
[23:55:21] [Server thread/INFO]: Cane Growth Modifier: 100%
[23:55:21] [Server thread/INFO]: Melon Growth Modifier: 100%
[23:55:21] [Server thread/INFO]: Mushroom Growth Modifier: 100%
[23:55:21] [Server thread/INFO]: Pumpkin Growth Modifier: 100%
[23:55:21] [Server thread/INFO]: Sapling Growth Modifier: 100%
[23:55:21] [Server thread/INFO]: Beetroot Growth Modifier: 100%
[23:55:21] [Server thread/INFO]: Carrot Growth Modifier: 100%
[23:55:21] [Server thread/INFO]: Potato Growth Modifier: 100%
[23:55:21] [Server thread/INFO]: Wheat Growth Modifier: 100%
[23:55:21] [Server thread/INFO]: NetherWart Growth Modifier: 100%
[23:55:21] [Server thread/INFO]: Vine Growth Modifier: 100%
[23:55:21] [Server thread/INFO]: Cocoa Growth Modifier: 100%
[23:55:21] [Server thread/INFO]: Bamboo Growth Modifier: 100%
[23:55:21] [Server thread/INFO]: SweetBerry Growth Modifier: 100%
[23:55:21] [Server thread/INFO]: Kelp Growth Modifier: 100%
[23:55:21] [Server thread/INFO]: TwistingVines Growth Modifier: 100%
[23:55:21] [Server thread/INFO]: WeepingVines Growth Modifier: 100%
[23:55:21] [Server thread/INFO]: CaveVines Growth Modifier: 100%
[23:55:21] [Server thread/INFO]: Max TNT Explosions: 100
[23:55:21] [Server thread/INFO]: Tile Max Tick Time: 50ms Entity max Tick Time: 50ms
[23:55:21] [Server thread/INFO]: View Distance: 10
[23:55:21] [Server thread/INFO]: Simulation Distance: 10
[23:55:21] [Server thread/INFO]: Item Despawn Rate: 6000
[23:55:21] [Server thread/INFO]: Item Merge Radius: 2.5
[23:55:21] [Server thread/INFO]: Arrow Despawn Rate: 1200 Trident Respawn Rate:1200
[23:55:21] [Server thread/INFO]: Allow Zombie Pigmen to spawn from portal blocks: true
[23:55:21] [Server thread/INFO]: Zombie Aggressive Towards Villager: true
[23:55:21] [Server thread/INFO]: Nerfing mobs spawned from spawners: false
[23:55:21] [Server thread/INFO]: Preparing start region for dimension minecraft:sd_instances
[23:55:21] [Server thread/INFO]: Time elapsed: 0 ms
[23:55:21] [Server thread/WARN]: [SoapsDungeons] SoapsMobs API not found. Falling back to vanilla spawning.
[23:55:21] [Server thread/INFO]: [SoapsDungeons] Restored 5 saved dungeon(s) from library.
[23:55:21] [Server thread/INFO]: [SoapsDungeons] SoapsDungeons enabled. Made by Vexy.
[23:55:21] [Server thread/INFO]: Server permissions file permissions.yml is empty, ignoring it
[23:55:21] [Server thread/INFO]: Done (11.102s)! For help, type "help"
[23:55:21] [Server thread/INFO]: [GUIAdminTools] New update available!
[23:55:21] [Server thread/INFO]: [GUIAdminTools] Current version: 3.2.1
[23:55:21] [Server thread/INFO]: [GUIAdminTools] New version: 3.2.7
[23:55:21] [Server thread/INFO]: [GUIAdminTools] [SpigotMC.org] https://www.spigotmc.org/resources/gui-admin-tools.108689/
[23:55:21] [Server thread/INFO]: [GUIAdminTools] [BuiltByBit.com] https://builtbybit.com/resources/gui-admin-tools-free.59991/
[23:55:21] [Server thread/INFO]: [GUIAdminTools] [curseforge.com] https://www.curseforge.com/minecraft/bukkit-plugins/gui-admin-tools-free
[23:55:22] [Netty Epoll Server IO #1/WARN]: [ProtocolLib] Loaded class net.kyori.adventure.text.Component from packetevents v2.9.5 which is not a depend or softdepend of this plugin.
[23:56:21] [Server thread/INFO]: Server empty for 60 seconds, pausing
[23:57:13] [User Authenticator #1/INFO]: UUID of player VexyAUS is 77df2456-0338-41ec-8344-96ea77b5328b
[23:57:13] [Server thread/INFO]: VexyAUS joined the game
[23:57:13] [Server thread/INFO]: VexyAUS[/159.196.168.4:5506] logged in with entity id 192 at ([world]136.45077306674384, 63.0, 204.5145823072369)
[23:57:18] [Server thread/INFO]: VexyAUS issued server command: /clearchat
[23:57:22] [Server thread/INFO]: VexyAUS issued server command: /ommo
[23:57:22] [Server thread/ERROR]: null
org.bukkit.command.CommandException: Cannot execute command 'ommo' in plugin OrvynMMO v0.1.0 - plugin is disabled.
	at org.bukkit.command.PluginCommand.execute(PluginCommand.java:37) ~[spigot-api-1.21.5-R0.1-SNAPSHOT.jar:?]
	at org.bukkit.command.SimpleCommandMap.dispatch(SimpleCommandMap.java:150) ~[spigot-api-1.21.5-R0.1-SNAPSHOT.jar:?]
	at org.bukkit.craftbukkit.v1_21_R4.CraftServer.dispatchCommand(CraftServer.java:924) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at org.bukkit.craftbukkit.v1_21_R4.command.BukkitCommandWrapper.run(BukkitCommandWrapper.java:50) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at com.mojang.brigadier.context.ContextChain.runExecutable(ContextChain.java:73) ~[brigadier-1.3.10.jar:?]
	at net.minecraft.commands.execution.tasks.ExecuteCommand.a(SourceFile:29) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.commands.execution.tasks.ExecuteCommand.execute(SourceFile:13) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.commands.execution.UnboundEntryAction.a(SourceFile:8) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.commands.execution.CommandQueueEntry.a(SourceFile:8) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.commands.execution.ExecutionContext.a(SourceFile:107) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.commands.CommandDispatcher.a(CommandDispatcher.java:418) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.commands.CommandDispatcher.performCommand(CommandDispatcher.java:340) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.commands.CommandDispatcher.a(CommandDispatcher.java:327) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.server.network.PlayerConnection.b(PlayerConnection.java:2028) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.server.network.PlayerConnection.lambda$handleChatCommand$8(PlayerConnection.java:2004) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.server.TickTask.run(SourceFile:18) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.util.thread.IAsyncTaskHandler.d(SourceFile:164) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.util.thread.IAsyncTaskHandlerReentrant.d(SourceFile:23) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.server.MinecraftServer.b(MinecraftServer.java:1290) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.server.MinecraftServer.d(MinecraftServer.java:206) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.util.thread.IAsyncTaskHandler.B(SourceFile:138) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.server.MinecraftServer.bv(MinecraftServer.java:1273) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.server.MinecraftServer.B(MinecraftServer.java:1266) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.util.thread.IAsyncTaskHandler.b(SourceFile:147) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.server.MinecraftServer.b(MinecraftServer.java:1223) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.server.MinecraftServer.w_(MinecraftServer.java:1233) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.server.MinecraftServer.y(MinecraftServer.java:1076) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at net.minecraft.server.MinecraftServer.lambda$spin$0(MinecraftServer.java:325) ~[spigot-1.21.5-R0.1-SNAPSHOT.jar:4503-Spigot-270012a-9119da2]
	at java.base/java.lang.Thread.run(Thread.java:1583) [?:?]
