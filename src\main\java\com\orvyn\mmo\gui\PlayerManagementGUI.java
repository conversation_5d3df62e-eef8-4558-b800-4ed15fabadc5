package com.orvyn.mmo.gui;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import com.orvyn.mmo.enums.Attribute;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class PlayerManagementGUI implements Listener {
    
    private final OrvynMMOPlugin plugin;
    private final Player player;
    private final PlayerData data;
    private Inventory currentInventory;
    private String currentGUIType;
    
    // GUI Types
    private static final String MAIN_GUI = "main";
    private static final String SKILL_TREE = "skills";
    private static final String STATS_ALLOCATION = "stats";
    private static final String ACTIVE_SKILLS = "active";
    private static final String PASSIVE_SKILLS = "passive";
    
    public PlayerManagementGUI(OrvynMMOPlugin plugin, Player player) {
        this.plugin = plugin;
        this.player = player;
        this.data = plugin.getPlayerDataManager().getPlayerData(player.getUniqueId());
        
        // Register events
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }
    
    public void open() {
        openMainGUI();
    }
    
    public void openMainGUI() {
        currentGUIType = MAIN_GUI;
        currentInventory = Bukkit.createInventory(null, 45, ChatColor.DARK_PURPLE + "Player Management");
        
        // Player info item
        ItemStack playerInfo = createItem(Material.PLAYER_HEAD, 
            ChatColor.YELLOW + player.getName(),
            ChatColor.GRAY + "Level " + data.getClassLevel() + " " + capitalizeFirst(data.getPlayerClass()),
            "",
            ChatColor.AQUA + "Health: " + ChatColor.RED + (int)data.getCurrentHP() + "/" + (int)data.getMaxHP(),
            ChatColor.AQUA + "Mana: " + ChatColor.BLUE + (int)data.getCurrentMana() + "/" + (int)data.getMaxMana(),
            "",
            ChatColor.GREEN + "Click to view detailed stats"
        );
        currentInventory.setItem(4, playerInfo);
        
        // Skill Tree
        ItemStack skillTree = createItem(Material.ENCHANTED_BOOK,
            ChatColor.LIGHT_PURPLE + "Skill Tree",
            ChatColor.GRAY + "Manage your class skills",
            "",
            ChatColor.YELLOW + "Unlocked Skills: " + data.getUnlockedSkills().size(),
            "",
            ChatColor.GREEN + "Click to open skill tree"
        );
        currentInventory.setItem(19, skillTree);
        
        // Stats Allocation
        ItemStack statsAllocation = createItem(Material.DIAMOND,
            ChatColor.AQUA + "Stats & Attributes",
            ChatColor.GRAY + "Allocate attribute points",
            "",
            ChatColor.YELLOW + "Available Points: " + getAvailableAttributePoints(),
            "",
            ChatColor.GREEN + "Click to allocate stats"
        );
        currentInventory.setItem(21, statsAllocation);
        
        // Skill Bar Management
        ItemStack skillBar = createItem(Material.BLAZE_POWDER,
            ChatColor.RED + "Skill Bar",
            ChatColor.GRAY + "Configure your skill hotkeys",
            "",
            ChatColor.YELLOW + "Equipped Skills: " + getEquippedSkillBarSkills(),
            ChatColor.GRAY + "Use F + [1-5] to activate skills",
            "",
            ChatColor.GREEN + "Click to manage skill bar"
        );
        currentInventory.setItem(23, skillBar);
        
        // Passive Skills
        ItemStack passiveSkills = createItem(Material.EXPERIENCE_BOTTLE,
            ChatColor.GREEN + "Passive Skills",
            ChatColor.GRAY + "View your passive bonuses",
            "",
            ChatColor.YELLOW + "Active Passives: " + getActivePassives(),
            "",
            ChatColor.GREEN + "Click to view passives"
        );
        currentInventory.setItem(25, passiveSkills);
        
        // Close button
        ItemStack close = createItem(Material.BARRIER,
            ChatColor.RED + "Close",
            ChatColor.GRAY + "Close this menu"
        );
        currentInventory.setItem(40, close);
        
        player.openInventory(currentInventory);
    }
    
    public void openSkillTree() {
        currentGUIType = SKILL_TREE;
        currentInventory = Bukkit.createInventory(null, 54, ChatColor.DARK_PURPLE + "Skill Tree - " + capitalizeFirst(data.getPlayerClass()));
        
        String playerClass = data.getPlayerClass().toLowerCase();
        
        switch (playerClass) {
            case "warrior":
                setupWarriorSkillTree();
                break;
            case "mage":
                setupMageSkillTree();
                break;
            case "archer":
                setupArcherSkillTree();
                break;
            case "rogue":
                setupRogueSkillTree();
                break;
            case "paladin":
                setupPaladinSkillTree();
                break;
            case "druid":
                setupDruidSkillTree();
                break;
            default:
                setupDefaultSkillTree();
                break;
        }
        
        // Back button
        ItemStack back = createItem(Material.ARROW,
            ChatColor.YELLOW + "Back",
            ChatColor.GRAY + "Return to main menu"
        );
        currentInventory.setItem(45, back);
        
        player.openInventory(currentInventory);
    }
    
    private void setupWarriorSkillTree() {
        // Tier 1 Skills
        addSkillItem(10, "charge", Material.IRON_BOOTS, "Charge", 1, 
            "Dash forward and deal damage", "Cooldown: 8 seconds", "Mana Cost: 20");
        
        addSkillItem(12, "shield_bash", Material.SHIELD, "Shield Bash", 1,
            "Stun enemies with your shield", "Cooldown: 12 seconds", "Mana Cost: 25");
        
        addSkillItem(14, "battle_cry", Material.IRON_SWORD, "Battle Cry", 1,
            "Boost damage for a short time", "Duration: 10 seconds", "Mana Cost: 30");
        
        // Tier 2 Skills
        addSkillItem(19, "whirlwind", Material.DIAMOND_SWORD, "Whirlwind", 2,
            "Spin attack hitting all nearby enemies", "Cooldown: 15 seconds", "Mana Cost: 40");
        
        addSkillItem(21, "berserker_rage", Material.REDSTONE, "Berserker Rage", 2,
            "Increase attack speed and damage", "Duration: 15 seconds", "Mana Cost: 50");
        
        addSkillItem(23, "iron_skin", Material.IRON_CHESTPLATE, "Iron Skin", 2,
            "Reduce incoming damage", "Duration: 20 seconds", "Mana Cost: 35");
        
        // Tier 3 Skills
        addSkillItem(28, "earthquake", Material.STONE, "Earthquake", 3,
            "Cause massive area damage", "Cooldown: 30 seconds", "Mana Cost: 80");
        
        addSkillItem(30, "weapon_master", Material.NETHERITE_SWORD, "Weapon Master", 3,
            "Ultimate warrior technique", "Cooldown: 60 seconds", "Mana Cost: 100");
    }
    
    private void setupMageSkillTree() {
        // Tier 1 Skills
        addSkillItem(10, "fireball", Material.FIRE_CHARGE, "Fireball", 1,
            "Launch a burning projectile", "Cooldown: 3 seconds", "Mana Cost: 15");
        
        addSkillItem(12, "ice_blast", Material.ICE, "Ice Blast", 1,
            "Freeze enemies in place", "Cooldown: 5 seconds", "Mana Cost: 20");
        
        addSkillItem(14, "mana_shield", Material.ENCHANTED_BOOK, "Mana Shield", 1,
            "Absorb damage with mana", "Duration: 30 seconds", "Mana Cost: 25");
        
        // Tier 2 Skills
        addSkillItem(19, "lightning_bolt", Material.TRIDENT, "Lightning Bolt", 2,
            "Strike enemies with lightning", "Cooldown: 8 seconds", "Mana Cost: 35");
        
        addSkillItem(21, "teleport", Material.ENDER_PEARL, "Teleport", 2,
            "Instantly move to target location", "Cooldown: 10 seconds", "Mana Cost: 40");
        
        addSkillItem(23, "arcane_explosion", Material.TNT, "Arcane Explosion", 2,
            "Explode with magical energy", "Cooldown: 12 seconds", "Mana Cost: 45");
        
        // Tier 3 Skills
        addSkillItem(28, "meteor", Material.MAGMA_BLOCK, "Meteor", 3,
            "Call down a devastating meteor", "Cooldown: 45 seconds", "Mana Cost: 100");
        
        addSkillItem(30, "time_stop", Material.CLOCK, "Time Stop", 3,
            "Freeze time around you", "Cooldown: 120 seconds", "Mana Cost: 150");
    }
    
    private void setupArcherSkillTree() {
        // Tier 1 Skills
        addSkillItem(10, "power_shot", Material.ARROW, "Power Shot", 1,
            "Fire a powerful arrow", "Cooldown: 4 seconds", "Mana Cost: 15");
        
        addSkillItem(12, "multi_shot", Material.SPECTRAL_ARROW, "Multi Shot", 1,
            "Fire multiple arrows at once", "Cooldown: 6 seconds", "Mana Cost: 25");
        
        addSkillItem(14, "eagle_eye", Material.SPYGLASS, "Eagle Eye", 1,
            "Increase accuracy and range", "Duration: 20 seconds", "Mana Cost: 20");
        
        // Tier 2 Skills
        addSkillItem(19, "explosive_arrow", Material.TNT_MINECART, "Explosive Arrow", 2,
            "Arrow explodes on impact", "Cooldown: 10 seconds", "Mana Cost: 40");
        
        addSkillItem(21, "dodge_roll", Material.LEATHER_BOOTS, "Dodge Roll", 2,
            "Quickly dodge attacks", "Cooldown: 8 seconds", "Mana Cost: 30");
        
        addSkillItem(23, "piercing_shot", Material.CROSSBOW, "Piercing Shot", 2,
            "Arrow pierces through enemies", "Cooldown: 12 seconds", "Mana Cost: 35");
        
        // Tier 3 Skills
        addSkillItem(28, "arrow_storm", Material.TIPPED_ARROW, "Arrow Storm", 3,
            "Rain arrows from the sky", "Cooldown: 30 seconds", "Mana Cost: 80");
        
        addSkillItem(30, "phantom_archer", Material.PHANTOM_MEMBRANE, "Phantom Archer", 3,
            "Become one with the shadows", "Cooldown: 90 seconds", "Mana Cost: 120");
    }

    private void setupRogueSkillTree() {
        // Tier 1 Skills
        addSkillItem(10, "stealth", Material.POTION, "Stealth", 1,
            "Become invisible for a short time", "Duration: 5 seconds", "Mana Cost: 15");

        addSkillItem(12, "backstab", Material.IRON_SWORD, "Backstab", 1,
            "Deal extra damage from behind", "Damage: +50%", "Mana Cost: 20");

        addSkillItem(14, "poison_blade", Material.SPIDER_EYE, "Poison Blade", 1,
            "Coat weapon with poison", "Duration: 30 seconds", "Mana Cost: 25");

        // Tier 2 Skills
        addSkillItem(19, "shadow_step", Material.ENDER_PEARL, "Shadow Step", 2,
            "Teleport behind target", "Range: 8 blocks", "Mana Cost: 35");

        addSkillItem(21, "smoke_bomb", Material.GUNPOWDER, "Smoke Bomb", 2,
            "Create a cloud of smoke", "Duration: 10 seconds", "Mana Cost: 40");

        addSkillItem(23, "caltrops", Material.IRON_NUGGET, "Caltrops", 2,
            "Scatter spikes on the ground", "Duration: 60 seconds", "Mana Cost: 30");

        // Tier 3 Skills
        addSkillItem(28, "assassinate", Material.DIAMOND_SWORD, "Assassinate", 3,
            "Instant kill low-health enemies", "Threshold: 25% HP", "Mana Cost: 80");

        addSkillItem(30, "shadow_clone", Material.ARMOR_STAND, "Shadow Clone", 3,
            "Create a shadow duplicate", "Duration: 20 seconds", "Mana Cost: 120");
    }

    private void setupPaladinSkillTree() {
        // Tier 1 Skills
        addSkillItem(10, "holy_strike", Material.GOLDEN_SWORD, "Holy Strike", 1,
            "Strike with divine power", "Damage: +Holy", "Mana Cost: 20");

        addSkillItem(12, "divine_protection", Material.SHIELD, "Divine Protection", 1,
            "Reduce incoming damage", "Duration: 15 seconds", "Mana Cost: 25");

        addSkillItem(14, "heal", Material.GOLDEN_APPLE, "Heal", 1,
            "Restore health to self or ally", "Healing: 6 hearts", "Mana Cost: 30");

        // Tier 2 Skills
        addSkillItem(19, "consecration", Material.GLOWSTONE_DUST, "Consecration", 2,
            "Sanctify the ground", "Duration: 20 seconds", "Mana Cost: 40");

        addSkillItem(21, "divine_shield", Material.ENCHANTED_BOOK, "Divine Shield", 2,
            "Become immune to damage", "Duration: 3 seconds", "Mana Cost: 50");

        addSkillItem(23, "turn_undead", Material.BONE, "Turn Undead", 2,
            "Repel undead creatures", "Range: 10 blocks", "Mana Cost: 35");

        // Tier 3 Skills
        addSkillItem(28, "smite", Material.LIGHTNING_ROD, "Smite", 3,
            "Call down divine lightning", "Damage: Massive", "Mana Cost: 80");

        addSkillItem(30, "resurrection", Material.TOTEM_OF_UNDYING, "Resurrection", 3,
            "Revive fallen allies", "Range: 5 blocks", "Mana Cost: 150");
    }

    private void setupDruidSkillTree() {
        // Tier 1 Skills
        addSkillItem(10, "nature_bolt", Material.OAK_LEAVES, "Nature Bolt", 1,
            "Launch a bolt of nature energy", "Damage: Nature", "Mana Cost: 12");

        addSkillItem(12, "entangle", Material.VINE, "Entangle", 1,
            "Root enemies in place", "Duration: 5 seconds", "Mana Cost: 20");

        addSkillItem(14, "bark_skin", Material.OAK_LOG, "Bark Skin", 1,
            "Increase natural armor", "Duration: 30 seconds", "Mana Cost: 25");

        // Tier 2 Skills
        addSkillItem(19, "wolf_form", Material.WOLF_SPAWN_EGG, "Wolf Form", 2,
            "Transform into a wolf", "Duration: 60 seconds", "Mana Cost: 50");

        addSkillItem(21, "nature_heal", Material.SWEET_BERRIES, "Nature Heal", 2,
            "Heal using nature's power", "Healing: 8 hearts", "Mana Cost: 40");

        addSkillItem(23, "thorn_wall", Material.ROSE_BUSH, "Thorn Wall", 2,
            "Create a wall of thorns", "Duration: 30 seconds", "Mana Cost: 45");

        // Tier 3 Skills
        addSkillItem(28, "earthquake", Material.COBBLESTONE, "Earthquake", 3,
            "Shake the earth violently", "Range: 15 blocks", "Mana Cost: 100");

        addSkillItem(30, "storm_call", Material.TRIDENT, "Storm Call", 3,
            "Summon a lightning storm", "Duration: 30 seconds", "Mana Cost: 120");
    }

    private void setupDefaultSkillTree() {
        // Basic skills for unknown classes
        addSkillItem(22, "basic_attack", Material.WOODEN_SWORD, "Basic Attack", 1,
            "A simple attack", "No cooldown", "No mana cost");
    }
    
    private void addSkillItem(int slot, String skillId, Material material, String name, int tier, String... lore) {
        boolean unlocked = data.getUnlockedSkills().contains(skillId);
        boolean canUnlock = canUnlockSkill(skillId, tier);
        
        List<String> loreList = new ArrayList<>();
        loreList.add(ChatColor.GRAY + "Tier " + tier + " Skill");
        loreList.add("");
        
        for (String line : lore) {
            loreList.add(ChatColor.YELLOW + line);
        }
        
        loreList.add("");
        
        if (unlocked) {
            loreList.add(ChatColor.GREEN + "✓ UNLOCKED");
            loreList.add(ChatColor.GRAY + "Click to view details");
        } else if (canUnlock) {
            loreList.add(ChatColor.YELLOW + "Click to unlock");
            loreList.add(ChatColor.GRAY + "Cost: 1 Skill Point");
        } else {
            loreList.add(ChatColor.RED + "✗ LOCKED");
            loreList.add(ChatColor.GRAY + "Requires level " + (tier * 5));
        }
        
        ItemStack item = createItem(material, 
            (unlocked ? ChatColor.GREEN : canUnlock ? ChatColor.YELLOW : ChatColor.RED) + name,
            loreList.toArray(new String[0])
        );
        
        currentInventory.setItem(slot, item);
    }
    
    private boolean canUnlockSkill(String skillId, int tier) {
        // Check if player has required level
        int requiredLevel = tier * 5;
        if (data.getClassLevel() < requiredLevel) return false;
        
        // Check if player has skill points
        return getAvailableSkillPoints() > 0;
    }
    
    public void openStatsAllocation() {
        currentGUIType = STATS_ALLOCATION;
        currentInventory = Bukkit.createInventory(null, 45, ChatColor.DARK_PURPLE + "Stats & Attributes");
        
        // Available points display
        ItemStack availablePoints = createItem(Material.EXPERIENCE_BOTTLE,
            ChatColor.GOLD + "Available Points: " + getAvailableAttributePoints(),
            ChatColor.GRAY + "Gain points by leveling up"
        );
        currentInventory.setItem(4, availablePoints);
        
        // Attribute allocation
        setupAttributeAllocation();
        
        // Back button
        ItemStack back = createItem(Material.ARROW,
            ChatColor.YELLOW + "Back",
            ChatColor.GRAY + "Return to main menu"
        );
        currentInventory.setItem(36, back);
        
        player.openInventory(currentInventory);
    }
    
    private void setupAttributeAllocation() {
        // Strength
        addAttributeItem(19, Attribute.STR, Material.IRON_SWORD, "Strength",
            "Increases physical damage", "Current: " + data.getTotalAttribute(Attribute.STR));
        
        // Agility
        addAttributeItem(21, Attribute.AGI, Material.FEATHER, "Agility", 
            "Increases attack speed and crit chance", "Current: " + data.getTotalAttribute(Attribute.AGI));
        
        // Intelligence
        addAttributeItem(23, Attribute.INT, Material.ENCHANTED_BOOK, "Intelligence",
            "Increases mana and spell damage", "Current: " + data.getTotalAttribute(Attribute.INT));
        
        // Health
        addAttributeItem(25, Attribute.HP, Material.APPLE, "Health",
            "Increases maximum health", "Current: " + data.getTotalAttribute(Attribute.HP));
    }
    
    private void addAttributeItem(int slot, Attribute attribute, Material material, String name, String description, String current) {
        ItemStack item = createItem(material,
            ChatColor.AQUA + name,
            ChatColor.GRAY + description,
            "",
            ChatColor.YELLOW + current,
            "",
            ChatColor.GREEN + "Left click: +1 point",
            ChatColor.RED + "Right click: -1 point"
        );
        
        currentInventory.setItem(slot, item);
    }
    
    public void openActiveSkills() {
        currentGUIType = ACTIVE_SKILLS;
        currentInventory = Bukkit.createInventory(null, 36, ChatColor.DARK_PURPLE + "Active Skills");
        
        // Show equipped active skills
        setupActiveSkillsDisplay();
        
        // Back button
        ItemStack back = createItem(Material.ARROW,
            ChatColor.YELLOW + "Back",
            ChatColor.GRAY + "Return to main menu"
        );
        currentInventory.setItem(27, back);
        
        player.openInventory(currentInventory);
    }
    
    private void setupActiveSkillsDisplay() {
        // This would show the player's equipped active skills
        // For now, show placeholder
        ItemStack placeholder = createItem(Material.GRAY_STAINED_GLASS_PANE,
            ChatColor.GRAY + "Active Skills",
            ChatColor.YELLOW + "Feature coming soon!"
        );
        
        for (int i = 0; i < 9; i++) {
            currentInventory.setItem(i + 9, placeholder);
        }
    }
    
    public void openPassiveSkills() {
        currentGUIType = PASSIVE_SKILLS;
        currentInventory = Bukkit.createInventory(null, 36, ChatColor.DARK_PURPLE + "Passive Skills");
        
        // Show passive skills
        setupPassiveSkillsDisplay();
        
        // Back button
        ItemStack back = createItem(Material.ARROW,
            ChatColor.YELLOW + "Back",
            ChatColor.GRAY + "Return to main menu"
        );
        currentInventory.setItem(27, back);
        
        player.openInventory(currentInventory);
    }
    
    private void setupPassiveSkillsDisplay() {
        // This would show the player's passive skills
        // For now, show placeholder
        ItemStack placeholder = createItem(Material.GRAY_STAINED_GLASS_PANE,
            ChatColor.GRAY + "Passive Skills",
            ChatColor.YELLOW + "Feature coming soon!"
        );
        
        for (int i = 0; i < 9; i++) {
            currentInventory.setItem(i + 9, placeholder);
        }
    }
    
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        if (!event.getWhoClicked().equals(player)) return;
        if (!event.getInventory().equals(currentInventory)) return;
        
        event.setCancelled(true);
        
        ItemStack clicked = event.getCurrentItem();
        if (clicked == null || clicked.getType() == Material.AIR) return;
        
        int slot = event.getSlot();
        
        switch (currentGUIType) {
            case MAIN_GUI:
                handleMainGUIClick(slot);
                break;
            case SKILL_TREE:
                handleSkillTreeClick(slot);
                break;
            case STATS_ALLOCATION:
                handleStatsClick(slot, event.isLeftClick());
                break;
            case ACTIVE_SKILLS:
                handleActiveSkillsClick(slot);
                break;
            case PASSIVE_SKILLS:
                handlePassiveSkillsClick(slot);
                break;
        }
    }
    
    private void handleMainGUIClick(int slot) {
        switch (slot) {
            case 19: // Skill Tree
                openSkillTree();
                break;
            case 21: // Stats Allocation
                openStatsAllocation();
                break;
            case 23: // Skill Bar
                openSkillBarManagement();
                break;
            case 25: // Passive Skills
                openPassiveSkills();
                break;
            case 40: // Close
                player.closeInventory();
                break;
        }
    }
    
    private void handleSkillTreeClick(int slot) {
        if (slot == 45) { // Back button
            openMainGUI();
            return;
        }
        
        // Handle skill unlocking
        // This would be implemented based on the specific skill system
        player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
    }
    
    private void handleStatsClick(int slot, boolean leftClick) {
        if (slot == 36) { // Back button
            openMainGUI();
            return;
        }
        
        // Handle attribute allocation
        Attribute attribute = null;
        switch (slot) {
            case 19: attribute = Attribute.STR; break;
            case 21: attribute = Attribute.AGI; break;
            case 23: attribute = Attribute.INT; break;
            case 25: attribute = Attribute.HP; break;
        }
        
        if (attribute != null) {
            if (leftClick && getAvailableAttributePoints() > 0) {
                // Add point
                int current = data.getBaseAttributes().getOrDefault(attribute, 0);
                data.getBaseAttributes().put(attribute, current + 1);
                data.markDirty();
                player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.0f);
                openStatsAllocation(); // Refresh
            } else if (!leftClick) {
                // Remove point (if possible)
                int current = data.getBaseAttributes().getOrDefault(attribute, 0);
                if (current > 0) {
                    data.getBaseAttributes().put(attribute, current - 1);
                    data.markDirty();
                    player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 0.5f);
                    openStatsAllocation(); // Refresh
                }
            }
        }
    }
    
    private void handleActiveSkillsClick(int slot) {
        if (slot == 27) { // Back button
            openMainGUI();
        }
    }
    
    private void handlePassiveSkillsClick(int slot) {
        if (slot == 27) { // Back button
            openMainGUI();
        }
    }
    
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (event.getPlayer().equals(player) && event.getInventory().equals(currentInventory)) {
            // Clean up if needed
        }
    }
    
    // Helper methods
    private ItemStack createItem(Material material, String name, String... lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            meta.setDisplayName(name);
            
            if (lore.length > 0) {
                List<String> loreList = new ArrayList<>();
                for (String line : lore) {
                    loreList.add(line);
                }
                meta.setLore(loreList);
            }
            
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    private String capitalizeFirst(String str) {
        if (str == null || str.isEmpty()) return str;
        return str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase();
    }
    
    private int getAvailableSkillPoints() {
        // Calculate based on level - spent points
        int earnedPoints = data.getClassLevel() - 1; // 1 point per level after 1
        int spentPoints = data.getUnlockedSkills().size();
        return Math.max(0, earnedPoints - spentPoints);
    }
    
    private int getAvailableAttributePoints() {
        // Calculate based on level - this would need to be tracked properly
        return (data.getClassLevel() - 1) * 2; // 2 points per level for now
    }
    
    private int getEquippedSkillBarSkills() {
        // Count equipped skills in skill bar
        int count = 0;
        for (int i = 1; i <= 5; i++) {
            if (data.getSkillBarSlot(i) != null) {
                count++;
            }
        }
        return count;
    }
    
    private int getActivePassives() {
        // Count active passive skills
        return 0; // Placeholder
    }

    private void openSkillBarManagement() {
        player.closeInventory();
        com.orvyn.mmo.gui.SkillBarManagementGUI skillBarGUI = new com.orvyn.mmo.gui.SkillBarManagementGUI(plugin);
        skillBarGUI.openSkillBarGUI(player);
    }
}
