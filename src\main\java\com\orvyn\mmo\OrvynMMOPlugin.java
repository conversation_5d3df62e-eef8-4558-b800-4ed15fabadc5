package com.orvyn.mmo;

import com.orvyn.mmo.api.OrvynAPI;
import com.orvyn.mmo.commands.OrvynCommand;
import com.orvyn.mmo.config.ConfigHub;
import com.orvyn.mmo.listeners.PlayerListener;
import com.orvyn.mmo.listeners.CombatListener;
import com.orvyn.mmo.listeners.SkillListener;
import com.orvyn.mmo.listeners.ClassWeaponListener;
import com.orvyn.mmo.managers.*;
import com.orvyn.mmo.scheduler.TickScheduler;
import org.bukkit.plugin.java.JavaPlugin;

import java.util.logging.Level;

public class OrvynMMOPlugin extends JavaPlugin {
    
    private static OrvynMMOPlugin instance;
    
    // Core managers
    private ConfigHub configHub;
    private PlayerDataManager playerDataManager;
    private ClassManager classManager;
    private SkillManager skillManager;
    private ItemManager itemManager;
    private ProfessionManager professionManager;
    private WaypointManager waypointManager;
    private QuestManager questManager;
    private EquipmentManager equipmentManager;
    private DropManager dropManager;
    private SpawnerManager spawnerManager;
    private CombatManager combatManager;
    private EffectManager effectManager;
    private ExperienceManager experienceManager;
    private HolographicHealthBarManager holographicHealthBarManager;
    private RegenerationManager regenerationManager;
    private com.orvyn.mmo.skills.SkillExecutor skillExecutor;
    private SkillBarManager skillBarManager;
    private PartyManager partyManager;
    private UIManager uiManager;
    private HUDManager hudManager;
    private NPCManager npcManager;
    private TickScheduler tickScheduler;
    private MagicWorkbenchManager magicWorkbenchManager;
    private MagicItemManager magicItemManager;

    // API
    private OrvynAPI api;
    
    @Override
    public void onEnable() {
        instance = this;

        getLogger().info("Starting OrvynMMO ALL v" + getDescription().getVersion());

        try {
            // Initialize core systems
            getLogger().info("Initializing core systems...");
            initializeCore();

            // Load configurations
            getLogger().info("Loading configurations...");
            loadConfigurations();

            // Initialize managers
            getLogger().info("Initializing managers...");
            initializeManagers();

            // Register listeners
            getLogger().info("Registering listeners...");
            registerListeners();

            // Register commands
            getLogger().info("Registering commands...");
            registerCommands();

            // Start schedulers
            getLogger().info("Starting schedulers...");
            startSchedulers();

            // Initialize API
            getLogger().info("Initializing API...");
            api = new OrvynAPI(this);

            getLogger().info("OrvynMMO ALL enabled successfully!");

        } catch (Exception e) {
            getLogger().log(Level.SEVERE, "Failed to enable OrvynMMO ALL", e);
            e.printStackTrace();
            getServer().getPluginManager().disablePlugin(this);
        }
    }
    
    @Override
    public void onDisable() {
        getLogger().info("Disabling OrvynMMO ALL...");
        
        // Stop schedulers
        if (tickScheduler != null) {
            tickScheduler.shutdown();
        }

        // Stop HUD for all players
        if (hudManager != null) {
            hudManager.shutdown();
        }

        // Save player data
        if (playerDataManager != null) {
            playerDataManager.saveAllPlayers();
        }

        // Cleanup GUI event listeners
        try {
            com.orvyn.mmo.gui.EquipmentGUI.cleanupAllGUIs();
            com.orvyn.mmo.gui.QuestGUI.cleanupAllGUIs();
            com.orvyn.mmo.gui.CharacterHubGUI.cleanupAllGUIs();
        } catch (Exception e) {
            getLogger().warning("Error cleaning up GUI listeners: " + e.getMessage());
        }

        // Cleanup managers
        cleanupManagers();

        getLogger().info("OrvynMMO ALL disabled successfully!");
    }
    
    private void initializeCore() {
        configHub = new ConfigHub(this);
        tickScheduler = new TickScheduler(this);
    }
    
    private void loadConfigurations() {
        configHub.loadAllConfigs();
    }
    
    private void initializeManagers() {
        try {
            getLogger().info("Initializing PlayerDataManager...");
            playerDataManager = new PlayerDataManager(this);

            // Log storage statistics
            playerDataManager.logStorageStatistics();

            getLogger().info("Initializing ClassManager...");
            classManager = new ClassManager(this);

            getLogger().info("Initializing SkillManager...");
            skillManager = new SkillManager(this);

            getLogger().info("Initializing ItemManager...");
            itemManager = new ItemManager(this);

            getLogger().info("Initializing ProfessionManager...");
            professionManager = new ProfessionManager(this);

            getLogger().info("Initializing WaypointManager...");
            waypointManager = new WaypointManager(this);

            getLogger().info("Initializing QuestManager...");
            questManager = new QuestManager(this);

            getLogger().info("Initializing EquipmentManager...");
            equipmentManager = new EquipmentManager(this);

            getLogger().info("Initializing DropManager...");
            dropManager = new DropManager(this);

            getLogger().info("Initializing SpawnerManager...");
            spawnerManager = new SpawnerManager(this);

            getLogger().info("Initializing CombatManager...");
            combatManager = new CombatManager(this);

            getLogger().info("Initializing EffectManager...");
            effectManager = new EffectManager(this);

            getLogger().info("Initializing ExperienceManager...");
            experienceManager = new ExperienceManager(this);

            getLogger().info("Initializing HolographicHealthBarManager...");
            holographicHealthBarManager = new HolographicHealthBarManager(this);

            getLogger().info("Initializing RegenerationManager...");
            regenerationManager = new RegenerationManager(this);

            getLogger().info("Initializing SkillExecutor...");
            skillExecutor = new com.orvyn.mmo.skills.SkillExecutor(this);

            getLogger().info("Initializing SkillBarManager...");
            skillBarManager = new SkillBarManager(this);

            getLogger().info("Initializing PartyManager...");
            partyManager = new PartyManager(this);

            getLogger().info("Initializing UIManager...");
            uiManager = new UIManager(this);

            getLogger().info("Initializing HUDManager...");
            hudManager = new HUDManager(this);

            getLogger().info("Initializing NPCManager...");
            npcManager = new NPCManager(this);

            getLogger().info("Initializing MagicItemManager...");
            magicItemManager = new MagicItemManager(this);

            getLogger().info("Initializing MagicWorkbenchManager...");
            magicWorkbenchManager = new MagicWorkbenchManager(this);

            getLogger().info("Initializing GUIs...");
            new com.orvyn.mmo.gui.PartyManagementGUI(this);
            new com.orvyn.mmo.gui.SkillBarManagementGUI(this);
            new com.orvyn.mmo.gui.EffectsGUI(this);

            getLogger().info("All managers initialized successfully!");
        } catch (Exception e) {
            getLogger().severe("Failed to initialize managers: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
    
    private void registerListeners() {
        getServer().getPluginManager().registerEvents(new PlayerListener(this), this);
        getServer().getPluginManager().registerEvents(new CombatListener(this), this);
        getServer().getPluginManager().registerEvents(new SkillListener(this), this);
        getServer().getPluginManager().registerEvents(new ClassWeaponListener(this), this);
    }
    
    private void registerCommands() {
        OrvynCommand orvynCommand = new OrvynCommand(this);
        getCommand("ommo").setExecutor(orvynCommand);
        getCommand("ommo").setTabCompleter(orvynCommand);

        // New consolidated command
        com.orvyn.mmo.commands.MyCharCommand myCharCommand = new com.orvyn.mmo.commands.MyCharCommand(this);
        getCommand("mychar").setExecutor(myCharCommand);
        getCommand("mychar").setTabCompleter(myCharCommand);

        // Legacy commands (redirect to new system)
        com.orvyn.mmo.commands.PlayerCommand playerCommand = new com.orvyn.mmo.commands.PlayerCommand(this);
        getCommand("player").setExecutor(playerCommand);
        getCommand("player").setTabCompleter(playerCommand);

        com.orvyn.mmo.commands.SkillCommand skillCommand = new com.orvyn.mmo.commands.SkillCommand(this);
        getCommand("skill").setExecutor(skillCommand);
        getCommand("skill").setTabCompleter(skillCommand);

        // Help command
        com.orvyn.mmo.commands.HelpCommand helpCommand = new com.orvyn.mmo.commands.HelpCommand(this);
        getCommand("help").setExecutor(helpCommand);
        getCommand("help").setTabCompleter(helpCommand);
    }
    
    private void startSchedulers() {
        tickScheduler.start();
    }
    
    private void cleanupManagers() {
        // Cleanup in reverse order
        if (npcManager != null) npcManager.shutdown();
        if (uiManager != null) uiManager.shutdown();
        if (partyManager != null) partyManager.shutdown();
        if (holographicHealthBarManager != null) holographicHealthBarManager.shutdown();
        if (skillBarManager != null) skillBarManager.shutdown();
        if (regenerationManager != null) regenerationManager.shutdown();
        if (effectManager != null) effectManager.shutdown();
        if (combatManager != null) combatManager.shutdown();
        if (spawnerManager != null) spawnerManager.shutdown();
        if (dropManager != null) dropManager.shutdown();
        if (questManager != null) questManager.shutdown();
        if (waypointManager != null) waypointManager.shutdown();
        if (professionManager != null) professionManager.shutdown();
        if (itemManager != null) itemManager.shutdown();
        if (skillManager != null) skillManager.shutdown();
        if (classManager != null) classManager.shutdown();
        if (playerDataManager != null) playerDataManager.shutdown();
        if (magicWorkbenchManager != null) magicWorkbenchManager.shutdown();
    }
    
    public boolean reload() {
        try {
            getLogger().info("Reloading OrvynMMO ALL...");

            // Create backup before reload
            if (playerDataManager != null) {
                playerDataManager.createBackup("reload");
            }

            // Reload configurations
            configHub.loadAllConfigs();

            // Reload managers
            reloadManagers();

            getLogger().info("OrvynMMO ALL reloaded successfully!");
            return true;

        } catch (Exception e) {
            getLogger().log(Level.SEVERE, "Failed to reload OrvynMMO ALL", e);
            return false;
        }
    }
    
    private void reloadManagers() {
        classManager.reload();
        skillManager.reload();
        itemManager.reload();
        professionManager.reload();
        waypointManager.reload();
        questManager.reload();
        dropManager.reload();
        spawnerManager.reload();
    }
    
    // Getters
    public static OrvynMMOPlugin getInstance() { return instance; }
    public ConfigHub getConfigHub() { return configHub; }
    public PlayerDataManager getPlayerDataManager() { return playerDataManager; }
    public ClassManager getClassManager() { return classManager; }
    public SkillManager getSkillManager() { return skillManager; }
    public ItemManager getItemManager() { return itemManager; }
    public ProfessionManager getProfessionManager() { return professionManager; }
    public WaypointManager getWaypointManager() { return waypointManager; }
    public QuestManager getQuestManager() { return questManager; }
    public EquipmentManager getEquipmentManager() { return equipmentManager; }
    public DropManager getDropManager() { return dropManager; }
    public SpawnerManager getSpawnerManager() { return spawnerManager; }
    public CombatManager getCombatManager() { return combatManager; }
    public EffectManager getEffectManager() { return effectManager; }
    public ExperienceManager getExperienceManager() { return experienceManager; }
    public HolographicHealthBarManager getHolographicHealthBarManager() { return holographicHealthBarManager; }
    public RegenerationManager getRegenerationManager() { return regenerationManager; }
    public com.orvyn.mmo.skills.SkillExecutor getSkillExecutor() { return skillExecutor; }
    public SkillBarManager getSkillBarManager() { return skillBarManager; }
    public PartyManager getPartyManager() { return partyManager; }
    public UIManager getUIManager() { return uiManager; }
    public HUDManager getHUDManager() { return hudManager; }
    public NPCManager getNPCManager() { return npcManager; }
    public TickScheduler getTickScheduler() { return tickScheduler; }
    public MagicWorkbenchManager getMagicWorkbenchManager() { return magicWorkbenchManager; }
    public MagicItemManager getMagicItemManager() { return magicItemManager; }
    public OrvynAPI getAPI() { return api; }
}
