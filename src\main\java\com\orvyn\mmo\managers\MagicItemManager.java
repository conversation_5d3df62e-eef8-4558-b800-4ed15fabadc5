package com.orvyn.mmo.managers;

import com.orvyn.mmo.OrvynMMOPlugin;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class MagicItemManager implements Listener {
    
    private final OrvynMMOPlugin plugin;
    private final NamespacedKey magicItemKey;
    private final Map<String, MagicRecipe> recipes = new HashMap<>();
    private final Map<UUID, Long> lastAbilityUse = new ConcurrentHashMap<>();
    
    public MagicItemManager(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
        this.magicItemKey = new NamespacedKey(plugin, "magic_item");
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
        initializeRecipes();
    }
    
    private void initializeRecipes() {
        // Vein Miner Pickaxe
        recipes.put("vein_miner_pickaxe", new MagicRecipe(
            new ItemStack[]{
                new ItemStack(Material.REDSTONE), new ItemStack(Material.REDSTONE), new ItemStack(Material.REDSTONE),
                new ItemStack(Material.REDSTONE), new ItemStack(Material.DIAMOND_PICKAXE), null,
                null, null, null
            },
            createVeinMinerPickaxe()
        ));
        
        // 3x3 Excavator Pickaxe
        recipes.put("excavator_pickaxe", new MagicRecipe(
            new ItemStack[]{
                new ItemStack(Material.TNT), new ItemStack(Material.NETHER_STAR), new ItemStack(Material.TNT),
                new ItemStack(Material.TNT), new ItemStack(Material.IRON_PICKAXE), new ItemStack(Material.TNT),
                null, null, null
            },
            createExcavatorPickaxe()
        ));
        
        // Lumber Axe
        recipes.put("lumber_axe", new MagicRecipe(
            new ItemStack[]{
                new ItemStack(Material.OAK_LOG), new ItemStack(Material.OAK_LOG), null,
                new ItemStack(Material.OAK_LOG), new ItemStack(Material.DIAMOND_AXE), new ItemStack(Material.OAK_LOG),
                null, null, null
            },
            createLumberAxe()
        ));
        
        // Harvester Hoe
        recipes.put("harvester_hoe", new MagicRecipe(
            new ItemStack[]{
                new ItemStack(Material.WHEAT_SEEDS), new ItemStack(Material.WHEAT_SEEDS), null,
                new ItemStack(Material.WHEAT_SEEDS), new ItemStack(Material.DIAMOND_HOE), new ItemStack(Material.WHEAT_SEEDS),
                null, null, null
            },
            createHarvesterHoe()
        ));
        
        // Teleportation Staff
        recipes.put("teleport_staff", new MagicRecipe(
            new ItemStack[]{
                new ItemStack(Material.ENDER_PEARL), new ItemStack(Material.ENDER_PEARL), null,
                new ItemStack(Material.ENDER_PEARL), new ItemStack(Material.STICK), new ItemStack(Material.ENDER_PEARL),
                null, null, null
            },
            createTeleportationStaff()
        ));
        
        // Lightning Sword
        recipes.put("lightning_sword", new MagicRecipe(
            new ItemStack[]{
                new ItemStack(Material.GLOWSTONE_DUST), new ItemStack(Material.GLOWSTONE_DUST), null,
                new ItemStack(Material.GLOWSTONE_DUST), new ItemStack(Material.DIAMOND_SWORD), new ItemStack(Material.GLOWSTONE_DUST),
                null, null, null
            },
            createLightningSword()
        ));
        
        // Explosive Bow
        recipes.put("explosive_bow", new MagicRecipe(
            new ItemStack[]{
                new ItemStack(Material.TNT), new ItemStack(Material.TNT), null,
                new ItemStack(Material.GUNPOWDER), new ItemStack(Material.BOW), new ItemStack(Material.TNT),
                null, new ItemStack(Material.TNT), null
            },
            createExplosiveBow()
        ));
        
        // Grappling Hook
        recipes.put("grappling_hook", new MagicRecipe(
            new ItemStack[]{
                new ItemStack(Material.STRING), new ItemStack(Material.STRING), new ItemStack(Material.STRING),
                new ItemStack(Material.IRON_INGOT), new ItemStack(Material.FISHING_ROD), new ItemStack(Material.IRON_INGOT),
                null, new ItemStack(Material.IRON_INGOT), null
            },
            createGrapplingHook()
        ));

        // Auto-Smelting Pickaxe
        recipes.put("auto_smelting_pickaxe", new MagicRecipe(
            new ItemStack[]{
                new ItemStack(Material.COAL), new ItemStack(Material.FURNACE), new ItemStack(Material.COAL),
                new ItemStack(Material.COAL), new ItemStack(Material.IRON_PICKAXE), new ItemStack(Material.COAL),
                null, null, null
            },
            createAutoSmeltingPickaxe()
        ));

        // Magnet Tool
        recipes.put("magnet_tool", new MagicRecipe(
            new ItemStack[]{
                new ItemStack(Material.IRON_INGOT), new ItemStack(Material.REDSTONE), new ItemStack(Material.IRON_INGOT),
                new ItemStack(Material.REDSTONE), new ItemStack(Material.COMPASS), new ItemStack(Material.REDSTONE),
                new ItemStack(Material.IRON_INGOT), new ItemStack(Material.REDSTONE), new ItemStack(Material.IRON_INGOT)
            },
            createMagnetTool()
        ));

        // Night Vision Goggles
        recipes.put("night_vision_goggles", new MagicRecipe(
            new ItemStack[]{
                new ItemStack(Material.GLASS), new ItemStack(Material.GLOWSTONE_DUST), new ItemStack(Material.GLASS),
                new ItemStack(Material.IRON_INGOT), new ItemStack(Material.LEATHER_HELMET), new ItemStack(Material.IRON_INGOT),
                null, new ItemStack(Material.REDSTONE), null
            },
            createNightVisionGoggles()
        ));

        // Speed Boots
        recipes.put("speed_boots", new MagicRecipe(
            new ItemStack[]{
                new ItemStack(Material.FEATHER), new ItemStack(Material.SUGAR), new ItemStack(Material.FEATHER),
                new ItemStack(Material.LEATHER), new ItemStack(Material.LEATHER_BOOTS), new ItemStack(Material.LEATHER),
                null, new ItemStack(Material.REDSTONE), null
            },
            createSpeedBoots()
        ));

        // Flame Sword
        recipes.put("flame_sword", new MagicRecipe(
            new ItemStack[]{
                new ItemStack(Material.BLAZE_POWDER), new ItemStack(Material.FIRE_CHARGE), new ItemStack(Material.BLAZE_POWDER),
                new ItemStack(Material.BLAZE_ROD), new ItemStack(Material.IRON_SWORD), new ItemStack(Material.BLAZE_ROD),
                null, new ItemStack(Material.MAGMA_CREAM), null
            },
            createFlameSword()
        ));

        // Ice Wand
        recipes.put("ice_wand", new MagicRecipe(
            new ItemStack[]{
                new ItemStack(Material.ICE), new ItemStack(Material.PACKED_ICE), new ItemStack(Material.ICE),
                new ItemStack(Material.SNOWBALL), new ItemStack(Material.STICK), new ItemStack(Material.SNOWBALL),
                null, new ItemStack(Material.BLUE_ICE), null
            },
            createIceWand()
        ));

        // Healing Staff
        recipes.put("healing_staff", new MagicRecipe(
            new ItemStack[]{
                new ItemStack(Material.GOLDEN_APPLE), new ItemStack(Material.DIAMOND), new ItemStack(Material.GOLDEN_APPLE),
                new ItemStack(Material.GOLD_INGOT), new ItemStack(Material.STICK), new ItemStack(Material.GOLD_INGOT),
                null, new ItemStack(Material.EMERALD), null
            },
            createHealingStaff()
        ));
    }
    
    public ItemStack checkRecipe(ItemStack[] craftingGrid) {
        for (MagicRecipe recipe : recipes.values()) {
            if (recipe.matches(craftingGrid)) {
                return recipe.getResult().clone();
            }
        }
        return null;
    }
    
    private ItemStack createMagicItem(Material material, String name, String id, String... lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        meta.setDisplayName(ChatColor.DARK_PURPLE + name);
        List<String> loreList = new ArrayList<>();
        loreList.add(ChatColor.GRAY + "✨ Magical Item ✨");
        loreList.add("");
        for (String line : lore) {
            loreList.add(ChatColor.YELLOW + line);
        }
        loreList.add("");
        loreList.add(ChatColor.BLUE + "Right-click to use special ability");
        meta.setLore(loreList);
        
        // Mark as magic item
        meta.getPersistentDataContainer().set(magicItemKey, PersistentDataType.STRING, id);
        
        // Add enchantment glow
        meta.addEnchant(Enchantment.UNBREAKING, 1, true);
        
        item.setItemMeta(meta);
        return item;
    }
    
    private ItemStack createVeinMinerPickaxe() {
        return createMagicItem(Material.DIAMOND_PICKAXE, "Vein Miner Pickaxe", "vein_miner_pickaxe",
            "Mines all connected ore blocks",
            "of the same type when one is mined",
            "Cooldown: 3 seconds");
    }
    
    private ItemStack createExcavatorPickaxe() {
        return createMagicItem(Material.IRON_PICKAXE, "3x3 Excavator Pickaxe", "excavator_pickaxe",
            "Mines a 3x3x3 area centered",
            "on the block you mine",
            "Cooldown: 5 seconds");
    }
    
    private ItemStack createLumberAxe() {
        return createMagicItem(Material.DIAMOND_AXE, "Lumber Axe", "lumber_axe",
            "Chops down entire trees when",
            "you break one log block",
            "Cooldown: 2 seconds");
    }
    
    private ItemStack createHarvesterHoe() {
        return createMagicItem(Material.DIAMOND_HOE, "Harvester Hoe", "harvester_hoe",
            "Automatically harvests and replants",
            "crops in a 5x5 area",
            "Cooldown: 1 second");
    }
    
    private ItemStack createTeleportationStaff() {
        return createMagicItem(Material.STICK, "Teleportation Staff", "teleport_staff",
            "Right-click to teleport to where",
            "you're looking (max 50 blocks)",
            "Cooldown: 10 seconds");
    }
    
    private ItemStack createLightningSword() {
        return createMagicItem(Material.DIAMOND_SWORD, "Lightning Sword", "lightning_sword",
            "Right-click to summon lightning",
            "at target location",
            "Sets enemies on fire on hit",
            "Cooldown: 8 seconds");
    }
    
    private ItemStack createExplosiveBow() {
        return createMagicItem(Material.BOW, "Explosive Bow", "explosive_bow",
            "Arrows create small explosions",
            "on impact",
            "Cooldown: 5 seconds per shot");
    }
    
    private ItemStack createGrapplingHook() {
        return createMagicItem(Material.FISHING_ROD, "Grappling Hook", "grappling_hook",
            "Right-click to pull yourself",
            "toward blocks or entities",
            "Cooldown: 3 seconds");
    }

    private ItemStack createAutoSmeltingPickaxe() {
        return createMagicItem(Material.IRON_PICKAXE, "Auto-Smelting Pickaxe", "auto_smelting_pickaxe",
            "Automatically smelts ores",
            "as you mine them",
            "Cooldown: None");
    }

    private ItemStack createMagnetTool() {
        return createMagicItem(Material.COMPASS, "Magnet Tool", "magnet_tool",
            "Automatically picks up items",
            "in a 10-block radius",
            "Hold in hand to activate");
    }

    private ItemStack createNightVisionGoggles() {
        return createMagicItem(Material.LEATHER_HELMET, "Night Vision Goggles", "night_vision_goggles",
            "Grants permanent night vision",
            "when worn as helmet",
            "No cooldown");
    }

    private ItemStack createSpeedBoots() {
        return createMagicItem(Material.LEATHER_BOOTS, "Speed Boots", "speed_boots",
            "Grants permanent speed effect",
            "when worn as boots",
            "No cooldown");
    }

    private ItemStack createFlameSword() {
        return createMagicItem(Material.IRON_SWORD, "Flame Sword", "flame_sword",
            "Sets enemies on fire on hit",
            "Right-click to shoot fireballs",
            "Cooldown: 5 seconds");
    }

    private ItemStack createIceWand() {
        return createMagicItem(Material.STICK, "Ice Wand", "ice_wand",
            "Freezes water and creates ice paths",
            "Right-click to slow enemies",
            "Cooldown: 4 seconds");
    }

    private ItemStack createHealingStaff() {
        return createMagicItem(Material.STICK, "Healing Staff", "healing_staff",
            "Right-click to heal nearby players",
            "Restores 4 hearts in 5-block radius",
            "Cooldown: 15 seconds");
    }
    
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItem();
        
        if (item == null || !item.hasItemMeta()) return;
        
        String magicItemId = item.getItemMeta().getPersistentDataContainer().get(magicItemKey, PersistentDataType.STRING);
        if (magicItemId == null) return;
        
        // Check cooldown
        UUID playerId = player.getUniqueId();
        long currentTime = System.currentTimeMillis();
        long lastUse = lastAbilityUse.getOrDefault(playerId, 0L);
        
        long cooldown = getCooldown(magicItemId);
        if (currentTime - lastUse < cooldown) {
            long remaining = (cooldown - (currentTime - lastUse)) / 1000;
            player.sendMessage(ChatColor.RED + "Ability on cooldown for " + remaining + " seconds!");
            return;
        }
        
        // Use magic item ability
        if (useMagicItemAbility(player, magicItemId, event)) {
            lastAbilityUse.put(playerId, currentTime);
            event.setCancelled(true);
        }
    }
    

    
    private boolean useMagicItemAbility(Player player, String itemId, PlayerInteractEvent event) {
        switch (itemId) {
            case "teleportation_staff":
                return useTeleportationStaff(player);
            case "lightning_sword":
                return useLightningSword(player);
            case "grappling_hook":
                return useGrapplingHook(player);
            case "flame_sword":
                return useFlameSword(player);
            case "ice_wand":
                return useIceWand(player);
            case "healing_staff":
                return useHealingStaff(player);
            case "explosive_bow":
                return useExplosiveBow(player);
            default:
                return false;
        }
    }
    
    private boolean useTeleportationStaff(Player player) {
        org.bukkit.block.Block target = player.getTargetBlock(null, 50);
        if (target.getType() == Material.AIR) {
            player.sendMessage(ChatColor.RED + "No valid teleport target!");
            return false;
        }
        
        org.bukkit.Location teleportLoc = target.getLocation().add(0, 1, 0);
        player.teleport(teleportLoc);
        player.sendMessage(ChatColor.DARK_PURPLE + "✨ Teleported!");
        player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.0f);
        player.getWorld().spawnParticle(org.bukkit.Particle.PORTAL, teleportLoc, 20);
        
        return true;
    }
    
    private boolean useLightningSword(Player player) {
        org.bukkit.block.Block target = player.getTargetBlock(null, 20);
        org.bukkit.Location strikeLoc = target.getLocation();
        
        strikeLoc.getWorld().strikeLightningEffect(strikeLoc);
        player.sendMessage(ChatColor.YELLOW + "⚡ Lightning strikes!");
        
        return true;
    }
    
    private boolean useGrapplingHook(Player player) {
        org.bukkit.block.Block target = player.getTargetBlock(null, 30);
        if (target.getType() == Material.AIR) {
            player.sendMessage(ChatColor.RED + "No valid grappling target!");
            return false;
        }

        org.bukkit.util.Vector direction = target.getLocation().toVector().subtract(player.getLocation().toVector()).normalize();
        player.setVelocity(direction.multiply(2.0));
        player.sendMessage(ChatColor.GRAY + "🪝 Grappling!");
        player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_FISHING_BOBBER_THROW, 1.0f, 1.0f);

        return true;
    }

    private boolean useFlameSword(Player player) {
        // Shoot fireball
        org.bukkit.entity.Fireball fireball = player.launchProjectile(org.bukkit.entity.Fireball.class);
        fireball.setYield(1.0f);
        fireball.setIsIncendiary(true);

        player.sendMessage(ChatColor.RED + "🔥 Fireball launched!");
        player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_GHAST_SHOOT, 1.0f, 1.2f);
        player.getWorld().spawnParticle(org.bukkit.Particle.FLAME, player.getLocation().add(0, 1.5, 0), 10);

        return true;
    }

    private boolean useIceWand(Player player) {
        // Freeze water and slow enemies
        org.bukkit.block.Block target = player.getTargetBlock(null, 15);
        org.bukkit.Location targetLoc = target.getLocation();

        // Freeze water blocks in area
        for (int x = -2; x <= 2; x++) {
            for (int z = -2; z <= 2; z++) {
                org.bukkit.block.Block block = targetLoc.clone().add(x, 0, z).getBlock();
                if (block.getType() == Material.WATER) {
                    block.setType(Material.ICE);
                }
            }
        }

        // Slow nearby enemies
        for (org.bukkit.entity.Entity entity : targetLoc.getWorld().getNearbyEntities(targetLoc, 5, 5, 5)) {
            if (entity instanceof org.bukkit.entity.LivingEntity && !(entity instanceof Player)) {
                org.bukkit.entity.LivingEntity living = (org.bukkit.entity.LivingEntity) entity;
                living.addPotionEffect(new org.bukkit.potion.PotionEffect(org.bukkit.potion.PotionEffectType.SLOWNESS, 100, 2));
                living.getWorld().spawnParticle(org.bukkit.Particle.SNOWFLAKE, living.getLocation(), 10);
            }
        }

        player.sendMessage(ChatColor.AQUA + "❄️ Ice magic activated!");
        player.playSound(player.getLocation(), org.bukkit.Sound.BLOCK_GLASS_BREAK, 1.0f, 0.5f);

        return true;
    }

    private boolean useHealingStaff(Player player) {
        // Heal nearby players
        boolean healed = false;
        for (org.bukkit.entity.Entity entity : player.getNearbyEntities(5, 5, 5)) {
            if (entity instanceof Player) {
                Player target = (Player) entity;
                double currentHealth = target.getHealth();
                double maxHealth = target.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).getValue();
                double healAmount = 8.0; // 4 hearts

                target.setHealth(Math.min(maxHealth, currentHealth + healAmount));
                target.sendMessage(ChatColor.GREEN + "✨ You have been healed!");
                target.getWorld().spawnParticle(org.bukkit.Particle.HEART, target.getLocation().add(0, 2, 0), 5);
                healed = true;
            }
        }

        if (healed) {
            player.sendMessage(ChatColor.GREEN + "💚 Healing magic flows through nearby allies!");
            player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.5f);
            return true;
        } else {
            player.sendMessage(ChatColor.RED + "No allies nearby to heal!");
            return false;
        }
    }

    private boolean useExplosiveBow(Player player) {
        // This ability is handled in EntityShootBowEvent and ProjectileHitEvent
        // For now, just give feedback that the bow is ready
        player.sendMessage(ChatColor.GOLD + "💥 Explosive arrows ready! Shoot to create explosions!");
        player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_TNT_PRIMED, 0.5f, 1.5f);
        return true;
    }

    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        Player player = event.getPlayer();
        ItemStack item = player.getInventory().getItemInMainHand();

        if (item == null || !item.hasItemMeta()) return;

        String magicItemId = item.getItemMeta().getPersistentDataContainer().get(magicItemKey, PersistentDataType.STRING);
        if (magicItemId == null) return;

        // Check cooldown
        UUID playerId = player.getUniqueId();
        long currentTime = System.currentTimeMillis();
        long lastUse = lastAbilityUse.getOrDefault(playerId, 0L);

        long cooldown = getCooldown(magicItemId);
        if (currentTime - lastUse < cooldown) {
            return; // Don't show message for mining tools
        }

        // Handle mining tool abilities
        boolean abilityUsed = false;
        switch (magicItemId) {
            case "vein_miner_pickaxe":
                abilityUsed = useVeinMinerPickaxe(player, event.getBlock());
                break;
            case "excavator_pickaxe":
                abilityUsed = useExcavatorPickaxe(player, event.getBlock());
                break;
            case "lumber_axe":
                abilityUsed = useLumberAxe(player, event.getBlock());
                break;
            case "auto_smelting_pickaxe":
                abilityUsed = useAutoSmeltingPickaxe(player, event.getBlock());
                break;
            case "harvester_hoe":
                abilityUsed = useHarvesterHoe(player, event.getBlock());
                break;
        }

        if (abilityUsed) {
            lastAbilityUse.put(playerId, currentTime);
        }
    }

    @EventHandler
    public void onEntityDamageByEntity(EntityDamageByEntityEvent event) {
        if (!(event.getDamager() instanceof Player)) return;
        Player player = (Player) event.getDamager();

        ItemStack item = player.getInventory().getItemInMainHand();
        if (item == null || !item.hasItemMeta()) return;

        String magicItemId = item.getItemMeta().getPersistentDataContainer().get(magicItemKey, PersistentDataType.STRING);
        if (magicItemId == null) return;

        // Handle combat item effects
        switch (magicItemId) {
            case "flame_sword":
                if (event.getEntity() instanceof org.bukkit.entity.LivingEntity) {
                    org.bukkit.entity.LivingEntity target = (org.bukkit.entity.LivingEntity) event.getEntity();
                    target.setFireTicks(100); // 5 seconds of fire
                    target.getWorld().spawnParticle(org.bukkit.Particle.FLAME, target.getLocation(), 10);
                    player.sendMessage(ChatColor.RED + "🔥 Enemy set ablaze!");
                }
                break;
        }
    }

    @EventHandler
    public void onPlayerMove(PlayerMoveEvent event) {
        Player player = event.getPlayer();

        // Check armor effects (every 20 ticks to avoid spam)
        if (player.getTicksLived() % 20 == 0) {
            checkArmorEffects(player);
        }

        // Check magnet tool (every 10 ticks)
        if (player.getTicksLived() % 10 == 0) {
            checkMagnetTool(player);
        }
    }

    // Mining tool abilities
    private boolean useVeinMinerPickaxe(Player player, org.bukkit.block.Block block) {
        if (!isOre(block.getType())) return false;

        Material oreType = block.getType();
        java.util.Set<org.bukkit.block.Block> connectedOres = new java.util.HashSet<>();
        findConnectedOres(block, oreType, connectedOres, 64); // Max 64 blocks

        for (org.bukkit.block.Block ore : connectedOres) {
            if (ore.equals(block)) continue; // Skip the original block
            ore.breakNaturally(player.getInventory().getItemInMainHand());
        }

        player.sendMessage(ChatColor.GOLD + "⛏️ Vein mined " + connectedOres.size() + " blocks!");
        player.playSound(player.getLocation(), org.bukkit.Sound.BLOCK_STONE_BREAK, 1.0f, 1.2f);

        return connectedOres.size() > 1;
    }

    private boolean useExcavatorPickaxe(Player player, org.bukkit.block.Block block) {
        org.bukkit.Location center = block.getLocation();
        int broken = 0;

        for (int x = -1; x <= 1; x++) {
            for (int y = -1; y <= 1; y++) {
                for (int z = -1; z <= 1; z++) {
                    if (x == 0 && y == 0 && z == 0) continue; // Skip center block

                    org.bukkit.block.Block targetBlock = center.clone().add(x, y, z).getBlock();
                    if (targetBlock.getType() != Material.AIR && targetBlock.getType() != Material.BEDROCK) {
                        targetBlock.breakNaturally(player.getInventory().getItemInMainHand());
                        broken++;
                    }
                }
            }
        }

        if (broken > 0) {
            player.sendMessage(ChatColor.GOLD + "💥 Excavated " + (broken + 1) + " blocks!");
            player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_GENERIC_EXPLODE, 0.5f, 1.2f);
        }

        return broken > 0;
    }

    private boolean useLumberAxe(Player player, org.bukkit.block.Block block) {
        if (!isLog(block.getType())) return false;

        java.util.Set<org.bukkit.block.Block> treeBlocks = new java.util.HashSet<>();
        findTreeBlocks(block, treeBlocks, 100); // Max 100 blocks

        for (org.bukkit.block.Block treeBlock : treeBlocks) {
            if (treeBlock.equals(block)) continue; // Skip the original block
            treeBlock.breakNaturally(player.getInventory().getItemInMainHand());
        }

        player.sendMessage(ChatColor.GREEN + "🌳 Chopped down tree (" + treeBlocks.size() + " blocks)!");
        player.playSound(player.getLocation(), org.bukkit.Sound.BLOCK_WOOD_BREAK, 1.0f, 0.8f);

        return treeBlocks.size() > 1;
    }

    private boolean useAutoSmeltingPickaxe(Player player, org.bukkit.block.Block block) {
        Material blockType = block.getType();
        ItemStack smeltedItem = getSmeltedResult(blockType);

        if (smeltedItem != null) {
            // Cancel the normal drop and give smelted item instead
            block.setType(Material.AIR);
            player.getInventory().addItem(smeltedItem);

            player.sendMessage(ChatColor.GOLD + "🔥 Auto-smelted " + smeltedItem.getType().name().toLowerCase() + "!");
            player.getWorld().spawnParticle(org.bukkit.Particle.FLAME, block.getLocation().add(0.5, 0.5, 0.5), 5);

            return true;
        }

        return false;
    }

    private boolean useHarvesterHoe(Player player, org.bukkit.block.Block block) {
        org.bukkit.Location center = block.getLocation();
        int harvested = 0;

        for (int x = -2; x <= 2; x++) {
            for (int z = -2; z <= 2; z++) {
                org.bukkit.block.Block cropBlock = center.clone().add(x, 0, z).getBlock();
                if (isMatureCrop(cropBlock)) {
                    // Harvest and replant
                    Material cropType = cropBlock.getType();
                    cropBlock.breakNaturally();

                    // Replant the crop
                    org.bukkit.scheduler.BukkitRunnable replantTask = new org.bukkit.scheduler.BukkitRunnable() {
                        @Override
                        public void run() {
                            if (cropBlock.getType() == Material.FARMLAND || cropBlock.getType() == Material.DIRT) {
                                cropBlock.setType(getSeedType(cropType));
                            }
                        }
                    };
                    replantTask.runTaskLater(plugin, 1L);

                    harvested++;
                }
            }
        }

        if (harvested > 0) {
            player.sendMessage(ChatColor.GREEN + "🌾 Harvested " + harvested + " crops!");
            player.playSound(player.getLocation(), org.bukkit.Sound.BLOCK_CROP_BREAK, 1.0f, 1.2f);
        }

        return harvested > 0;
    }

    // Helper methods for armor effects and magnet tool
    private void checkArmorEffects(Player player) {
        // Check helmet for night vision
        ItemStack helmet = player.getInventory().getHelmet();
        if (helmet != null && helmet.hasItemMeta()) {
            String magicItemId = helmet.getItemMeta().getPersistentDataContainer().get(magicItemKey, PersistentDataType.STRING);
            if ("night_vision_goggles".equals(magicItemId)) {
                player.addPotionEffect(new PotionEffect(PotionEffectType.NIGHT_VISION, 300, 0, true, false));
            }
        }

        // Check boots for speed
        ItemStack boots = player.getInventory().getBoots();
        if (boots != null && boots.hasItemMeta()) {
            String magicItemId = boots.getItemMeta().getPersistentDataContainer().get(magicItemKey, PersistentDataType.STRING);
            if ("speed_boots".equals(magicItemId)) {
                player.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, 300, 1, true, false));
            }
        }
    }

    private void checkMagnetTool(Player player) {
        ItemStack mainHand = player.getInventory().getItemInMainHand();
        if (mainHand != null && mainHand.hasItemMeta()) {
            String magicItemId = mainHand.getItemMeta().getPersistentDataContainer().get(magicItemKey, PersistentDataType.STRING);
            if ("magnet_tool".equals(magicItemId)) {
                // Attract nearby items
                for (org.bukkit.entity.Entity entity : player.getNearbyEntities(8, 8, 8)) {
                    if (entity instanceof org.bukkit.entity.Item) {
                        org.bukkit.entity.Item item = (org.bukkit.entity.Item) entity;
                        org.bukkit.util.Vector direction = player.getLocation().toVector().subtract(item.getLocation().toVector()).normalize();
                        item.setVelocity(direction.multiply(0.3));
                    }
                }
            }
        }
    }

    // Helper methods for mining tools
    private boolean isOre(Material material) {
        return material == Material.COAL_ORE || material == Material.IRON_ORE ||
               material == Material.GOLD_ORE || material == Material.DIAMOND_ORE ||
               material == Material.EMERALD_ORE || material == Material.LAPIS_ORE ||
               material == Material.REDSTONE_ORE || material == Material.COPPER_ORE ||
               material == Material.DEEPSLATE_COAL_ORE || material == Material.DEEPSLATE_IRON_ORE ||
               material == Material.DEEPSLATE_GOLD_ORE || material == Material.DEEPSLATE_DIAMOND_ORE ||
               material == Material.DEEPSLATE_EMERALD_ORE || material == Material.DEEPSLATE_LAPIS_ORE ||
               material == Material.DEEPSLATE_REDSTONE_ORE || material == Material.DEEPSLATE_COPPER_ORE;
    }

    private void findConnectedOres(org.bukkit.block.Block block, Material oreType, java.util.Set<org.bukkit.block.Block> found, int maxBlocks) {
        if (found.size() >= maxBlocks || found.contains(block) || block.getType() != oreType) {
            return;
        }

        found.add(block);

        // Check all 6 adjacent blocks
        for (int x = -1; x <= 1; x++) {
            for (int y = -1; y <= 1; y++) {
                for (int z = -1; z <= 1; z++) {
                    if (Math.abs(x) + Math.abs(y) + Math.abs(z) == 1) { // Only adjacent, not diagonal
                        org.bukkit.block.Block adjacent = block.getRelative(x, y, z);
                        findConnectedOres(adjacent, oreType, found, maxBlocks);
                    }
                }
            }
        }
    }

    private boolean isLog(Material material) {
        return material.name().contains("_LOG") || material.name().contains("_WOOD");
    }

    private void findTreeBlocks(org.bukkit.block.Block block, java.util.Set<org.bukkit.block.Block> found, int maxBlocks) {
        if (found.size() >= maxBlocks || found.contains(block)) {
            return;
        }

        Material blockType = block.getType();
        if (!isLog(blockType) && !blockType.name().contains("LEAVES")) {
            return;
        }

        found.add(block);

        // Check all 26 surrounding blocks for tree parts
        for (int x = -1; x <= 1; x++) {
            for (int y = -1; y <= 1; y++) {
                for (int z = -1; z <= 1; z++) {
                    if (x == 0 && y == 0 && z == 0) continue;

                    org.bukkit.block.Block adjacent = block.getRelative(x, y, z);
                    Material adjType = adjacent.getType();
                    if (isLog(adjType) || adjType.name().contains("LEAVES")) {
                        findTreeBlocks(adjacent, found, maxBlocks);
                    }
                }
            }
        }
    }

    private ItemStack getSmeltedResult(Material material) {
        switch (material) {
            case IRON_ORE:
            case DEEPSLATE_IRON_ORE:
                return new ItemStack(Material.IRON_INGOT);
            case GOLD_ORE:
            case DEEPSLATE_GOLD_ORE:
                return new ItemStack(Material.GOLD_INGOT);
            case COPPER_ORE:
            case DEEPSLATE_COPPER_ORE:
                return new ItemStack(Material.COPPER_INGOT);
            case COBBLESTONE:
                return new ItemStack(Material.STONE);
            case SAND:
                return new ItemStack(Material.GLASS);
            case CLAY:
                return new ItemStack(Material.TERRACOTTA);
            default:
                return null;
        }
    }

    private boolean isMatureCrop(org.bukkit.block.Block block) {
        Material type = block.getType();
        if (type == Material.WHEAT || type == Material.CARROTS || type == Material.POTATOES || type == Material.BEETROOTS) {
            org.bukkit.block.data.Ageable ageable = (org.bukkit.block.data.Ageable) block.getBlockData();
            return ageable.getAge() == ageable.getMaximumAge();
        }
        return false;
    }

    private Material getSeedType(Material cropType) {
        switch (cropType) {
            case WHEAT:
                return Material.WHEAT;
            case CARROTS:
                return Material.CARROTS;
            case POTATOES:
                return Material.POTATOES;
            case BEETROOTS:
                return Material.BEETROOTS;
            default:
                return Material.WHEAT;
        }
    }

    private long getCooldown(String magicItemId) {
        switch (magicItemId) {
            case "teleportation_staff":
                return 10000; // 10 seconds
            case "lightning_sword":
                return 8000; // 8 seconds
            case "explosive_bow":
                return 5000; // 5 seconds
            case "grappling_hook":
                return 3000; // 3 seconds
            case "flame_sword":
                return 6000; // 6 seconds
            case "ice_wand":
                return 7000; // 7 seconds
            case "healing_staff":
                return 15000; // 15 seconds
            default:
                return 1000; // 1 second default
        }
    }

    private static class MagicRecipe {
        private final ItemStack[] pattern;
        private final ItemStack result;
        
        public MagicRecipe(ItemStack[] pattern, ItemStack result) {
            this.pattern = pattern;
            this.result = result;
        }
        
        public boolean matches(ItemStack[] craftingGrid) {
            for (int i = 0; i < 9; i++) {
                ItemStack patternItem = pattern[i];
                ItemStack gridItem = craftingGrid[i];
                
                if (patternItem == null) {
                    if (gridItem != null && gridItem.getType() != Material.AIR) {
                        return false;
                    }
                } else {
                    if (gridItem == null || gridItem.getType() != patternItem.getType()) {
                        return false;
                    }
                }
            }
            return true;
        }
        
        public ItemStack getResult() {
            return result;
        }
    }
}
