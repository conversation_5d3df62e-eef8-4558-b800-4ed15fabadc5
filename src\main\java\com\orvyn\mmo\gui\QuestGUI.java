package com.orvyn.mmo.gui;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import com.orvyn.mmo.data.QuestProgress;
import com.orvyn.mmo.models.Quest;
import com.orvyn.mmo.models.QuestObjective;
import com.orvyn.mmo.models.QuestReward;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.Set;

public class QuestGUI implements Listener {

    private final OrvynMMOPlugin plugin;
    private final Player player;
    private final PlayerData data;
    private static final Map<UUID, QuestGUI> activeGUIs = new HashMap<>();

    public QuestGUI(OrvynMMOPlugin plugin, Player player) {
        this.plugin = plugin;
        this.player = player;
        this.data = plugin.getPlayerDataManager().getPlayerData(player);

        // Unregister any existing GUI for this player
        QuestGUI existing = activeGUIs.get(player.getUniqueId());
        if (existing != null) {
            HandlerList.unregisterAll(existing);
        }

        // Register this as a listener and track it
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
        activeGUIs.put(player.getUniqueId(), this);
    }
    
    public void openQuestHub() {
        Inventory gui = Bukkit.createInventory(null, 54, ChatColor.DARK_GREEN + "Quest Hub - " + player.getName());
        
        // Active Quests
        int activeQuestCount = data.getActiveQuests().size();
        ItemStack activeQuests = createItem(Material.WRITABLE_BOOK,
            ChatColor.GREEN + "Active Quests",
            ChatColor.GRAY + "View your current quests",
            "",
            ChatColor.YELLOW + "Active: " + activeQuestCount,
            "",
            ChatColor.GREEN + "Click to view active quests"
        );
        gui.setItem(10, activeQuests);
        
        // Available Quests
        List<Quest> availableQuests = plugin.getQuestManager().getAvailableQuests(player);
        ItemStack available = createItem(Material.BOOK,
            ChatColor.AQUA + "Available Quests",
            ChatColor.GRAY + "Browse quests you can start",
            "",
            ChatColor.YELLOW + "Available: " + availableQuests.size(),
            "",
            ChatColor.GREEN + "Click to browse available quests"
        );
        gui.setItem(12, available);
        
        // Daily Quest
        Quest dailyQuest = plugin.getQuestManager().getDailyQuest();
        ItemStack daily = createItem(Material.CLOCK,
            ChatColor.GOLD + "Daily Quest",
            ChatColor.GRAY + "Special quest that rotates daily",
            "",
            dailyQuest != null ? ChatColor.YELLOW + "Title: " + dailyQuest.getName() : ChatColor.RED + "No daily quest available",
            dailyQuest != null ? ChatColor.YELLOW + "Time Remaining: " + plugin.getQuestManager().getFormattedDailyQuestTimeRemaining() : "",
            "",
            ChatColor.GREEN + "Click to view daily quest"
        );
        gui.setItem(14, daily);
        
        // Completed Quests
        int completedQuestCount = data.getCompletedQuests().size();
        ItemStack completed = createItem(Material.ENCHANTED_BOOK,
            ChatColor.DARK_PURPLE + "Completed Quests",
            ChatColor.GRAY + "View your quest achievements",
            "",
            ChatColor.YELLOW + "Completed: " + completedQuestCount,
            "",
            ChatColor.GREEN + "Click to view completed quests"
        );
        gui.setItem(16, completed);
        
        // Back button
        ItemStack back = createItem(Material.ARROW,
            ChatColor.YELLOW + "Back",
            ChatColor.GRAY + "Return to character hub"
        );
        gui.setItem(49, back);
        
        player.openInventory(gui);
    }
    
    public void openActiveQuests() {
        Inventory gui = Bukkit.createInventory(null, 54, ChatColor.GREEN + "Active Quests");

        // Load active quests from player data
        Map<String, QuestProgress> activeQuests = data.getActiveQuests();

        if (activeQuests.isEmpty()) {
            ItemStack noQuests = createItem(Material.BARRIER,
                ChatColor.RED + "No Active Quests",
                ChatColor.GRAY + "You don't have any active quests.",
                "",
                ChatColor.YELLOW + "Visit the Available Quests section to start some!"
            );
            gui.setItem(22, noQuests);
        } else {
            int slot = 9;
            for (Map.Entry<String, QuestProgress> entry : activeQuests.entrySet()) {
                if (slot >= 45) break; // Don't overflow

                String questId = entry.getKey();
                QuestProgress progress = entry.getValue();
                Quest quest = plugin.getQuestManager().findQuest(questId);

                if (quest != null) {
                    ItemStack questItem = createActiveQuestItem(quest, progress);
                    gui.setItem(slot, questItem);
                    slot++;
                }
            }
        }

        // Back button
        ItemStack back = createItem(Material.ARROW,
            ChatColor.YELLOW + "Back",
            ChatColor.GRAY + "Return to quest hub"
        );
        gui.setItem(49, back);

        player.openInventory(gui);
    }
    
    public void openAvailableQuests() {
        Inventory gui = Bukkit.createInventory(null, 54, ChatColor.AQUA + "Available Quests");
        
        List<Quest> availableQuests = plugin.getQuestManager().getAvailableQuests(player);
        
        int slot = 9;
        for (Quest quest : availableQuests) {
            if (slot >= 45) break; // Don't overflow
            
            ItemStack questItem = createSimpleQuestItem(quest);
            gui.setItem(slot, questItem);
            slot++;
        }
        
        if (availableQuests.isEmpty()) {
            ItemStack noQuests = createItem(Material.BARRIER,
                ChatColor.RED + "No Available Quests",
                ChatColor.GRAY + "Check back later for new quests!"
            );
            gui.setItem(22, noQuests);
        }
        
        // Back button
        ItemStack back = createItem(Material.ARROW,
            ChatColor.YELLOW + "Back",
            ChatColor.GRAY + "Return to quest hub"
        );
        gui.setItem(49, back);
        
        player.openInventory(gui);
    }
    
    public void openDailyQuest() {
        Quest dailyQuest = plugin.getQuestManager().getDailyQuest();
        if (dailyQuest == null) {
            player.sendMessage(ChatColor.RED + "No daily quest is currently available!");
            return;
        }

        Inventory gui = Bukkit.createInventory(null, 27, ChatColor.GOLD + "Daily Quest");

        // Daily quest item - simplified
        ItemStack questItem = createItem(Material.CLOCK,
            ChatColor.GOLD + dailyQuest.getName(),
            ChatColor.GRAY + dailyQuest.getDescription(),
            "",
            ChatColor.YELLOW + "Daily Quest",
            ChatColor.YELLOW + "Time Remaining: " + plugin.getQuestManager().getFormattedDailyQuestTimeRemaining()
        );
        gui.setItem(13, questItem);

        // Accept button (simplified)
        ItemStack start = createItem(Material.EMERALD,
            ChatColor.GREEN + "Accept Quest",
            ChatColor.GRAY + "Start this daily quest",
            "",
            ChatColor.GREEN + "Click to accept"
        );
        gui.setItem(15, start);

        // Back button
        ItemStack back = createItem(Material.ARROW,
            ChatColor.YELLOW + "Back",
            ChatColor.GRAY + "Return to quest hub"
        );
        gui.setItem(22, back);

        player.openInventory(gui);
    }

    public void openCompletedQuests() {
        Inventory gui = Bukkit.createInventory(null, 54, ChatColor.DARK_PURPLE + "Completed Quests");

        // Load completed quests from player data
        Set<String> completedQuestIds = data.getCompletedQuests();

        if (completedQuestIds.isEmpty()) {
            ItemStack noQuests = createItem(Material.BARRIER,
                ChatColor.RED + "No Completed Quests",
                ChatColor.GRAY + "You haven't completed any quests yet.",
                "",
                ChatColor.YELLOW + "Complete some quests to see them here!"
            );
            gui.setItem(22, noQuests);
        } else {
            int slot = 9;
            for (String questId : completedQuestIds) {
                if (slot >= 45) break; // Don't overflow

                Quest quest = plugin.getQuestManager().findQuest(questId);
                if (quest != null) {
                    ItemStack questItem = createCompletedQuestItem(quest);
                    gui.setItem(slot, questItem);
                    slot++;
                }
            }
        }

        // Back button
        ItemStack back = createItem(Material.ARROW,
            ChatColor.YELLOW + "Back",
            ChatColor.GRAY + "Return to quest hub"
        );
        gui.setItem(49, back);

        player.openInventory(gui);
    }

    private ItemStack createSimpleQuestItem(Quest quest) {
        Material material = Material.BOOK;
        if (quest.getType() == Quest.QuestType.DAILY) {
            material = Material.CLOCK;
        } else if (quest.getType() == Quest.QuestType.PERSONAL) {
            material = Material.WRITTEN_BOOK;
        }

        List<String> lore = new ArrayList<>();
        lore.add(ChatColor.GRAY + quest.getDescription());
        lore.add("");

        // Quest type
        String typeColor = quest.getType() == Quest.QuestType.DAILY ? ChatColor.GOLD.toString() :
                          quest.getType() == Quest.QuestType.PERSONAL ? ChatColor.LIGHT_PURPLE.toString() :
                          ChatColor.AQUA.toString();
        lore.add(typeColor + "Type: " + quest.getType().name());
        lore.add(ChatColor.YELLOW + "Required Level: " + quest.getMinLevel());

        lore.add("");
        lore.add(ChatColor.GREEN + "Click to accept quest");

        return createItem(material, ChatColor.WHITE + quest.getName(), lore.toArray(new String[0]));
    }

    private ItemStack createActiveQuestItem(Quest quest, QuestProgress progress) {
        List<String> lore = new ArrayList<>();
        lore.add(ChatColor.GRAY + quest.getDescription());
        lore.add("");

        // Add objectives with progress
        lore.add(ChatColor.YELLOW + "Objectives:");
        for (QuestObjective objective : quest.getObjectives()) {
            int currentProgress = progress.getObjectiveProgress(objective.getId());
            int required = objective.getAmount();

            String progressText = ChatColor.WHITE + "• " + objective.getFormattedDescription();
            if (currentProgress >= required) {
                progressText += ChatColor.GREEN + " ✓ COMPLETE";
            } else {
                progressText += ChatColor.YELLOW + " (" + currentProgress + "/" + required + ")";
            }
            lore.add(progressText);
        }

        lore.add("");

        // Add rewards
        lore.add(ChatColor.GREEN + "Rewards:");
        for (QuestReward reward : quest.getRewards()) {
            lore.add(ChatColor.WHITE + "• " + reward.getDescription());
        }

        lore.add("");
        lore.add(ChatColor.RED + "Right-click to abandon quest");

        return createItem(Material.WRITABLE_BOOK, ChatColor.GREEN + quest.getName(), lore.toArray(new String[0]));
    }

    private ItemStack createCompletedQuestItem(Quest quest) {
        List<String> lore = new ArrayList<>();
        lore.add(ChatColor.GRAY + quest.getDescription());
        lore.add("");

        // Quest type
        String typeColor = quest.getType() == Quest.QuestType.DAILY ? ChatColor.GOLD.toString() :
                          quest.getType() == Quest.QuestType.PERSONAL ? ChatColor.LIGHT_PURPLE.toString() :
                          ChatColor.AQUA.toString();
        lore.add(typeColor + "Type: " + quest.getType().name());

        lore.add("");

        // Add rewards that were received
        lore.add(ChatColor.GREEN + "Rewards Received:");
        for (QuestReward reward : quest.getRewards()) {
            lore.add(ChatColor.WHITE + "• " + reward.getDescription());
        }

        lore.add("");
        lore.add(ChatColor.GOLD + "✓ COMPLETED");

        return createItem(Material.ENCHANTED_BOOK, ChatColor.GOLD + quest.getName(), lore.toArray(new String[0]));
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        Player clickedPlayer = (Player) event.getWhoClicked();
        
        if (!clickedPlayer.equals(player)) return;
        
        String title = event.getView().getTitle();
        if (!title.startsWith(ChatColor.DARK_GREEN + "Quest Hub") &&
            !title.equals(ChatColor.GREEN + "Active Quests") &&
            !title.equals(ChatColor.AQUA + "Available Quests") &&
            !title.equals(ChatColor.GOLD + "Daily Quest") &&
            !title.equals(ChatColor.DARK_PURPLE + "Completed Quests")) return;
        
        event.setCancelled(true);
        
        ItemStack clicked = event.getCurrentItem();
        if (clicked == null || clicked.getType() == Material.AIR) return;
        
        int slot = event.getSlot();
        
        // Handle different GUI clicks
        if (title.startsWith(ChatColor.DARK_GREEN + "Quest Hub")) {
            handleQuestHubClick(slot);
        } else if (title.equals(ChatColor.GREEN + "Active Quests")) {
            handleActiveQuestsClick(slot, event.isRightClick());
        } else if (title.equals(ChatColor.AQUA + "Available Quests")) {
            handleAvailableQuestsClick(slot);
        } else if (title.equals(ChatColor.GOLD + "Daily Quest")) {
            handleDailyQuestClick(slot);
        } else if (title.equals(ChatColor.DARK_PURPLE + "Completed Quests")) {
            handleCompletedQuestsClick(slot);
        }

        player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
    }

    @EventHandler
    public void onInventoryClose(org.bukkit.event.inventory.InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) return;
        Player closingPlayer = (Player) event.getPlayer();

        if (closingPlayer.equals(player)) {
            // Unregister this GUI when the player closes any inventory
            HandlerList.unregisterAll(this);
            activeGUIs.remove(player.getUniqueId());
        }
    }

    public static void cleanupAllGUIs() {
        for (QuestGUI gui : activeGUIs.values()) {
            HandlerList.unregisterAll(gui);
        }
        activeGUIs.clear();
    }
    
    private void handleQuestHubClick(int slot) {
        switch (slot) {
            case 10: // Active Quests
                openActiveQuests();
                break;
            case 12: // Available Quests
                openAvailableQuests();
                break;
            case 14: // Daily Quest
                openDailyQuest();
                break;
            case 16: // Completed Quests
                openCompletedQuests();
                break;
            case 49: // Back
                CharacterHubGUI hubGUI = new CharacterHubGUI(plugin, player);
                hubGUI.open();
                break;
        }
    }
    
    private void handleActiveQuestsClick(int slot, boolean rightClick) {
        if (slot == 49) { // Back button
            openQuestHub();
            return;
        }

        // Handle quest abandonment with right-click
        if (rightClick && slot >= 9 && slot < 45) {
            Map<String, QuestProgress> activeQuests = data.getActiveQuests();
            List<String> questIds = new ArrayList<>(activeQuests.keySet());

            int questIndex = slot - 9;
            if (questIndex < questIds.size()) {
                String questId = questIds.get(questIndex);
                Quest quest = plugin.getQuestManager().findQuest(questId);

                if (quest != null && plugin.getQuestManager().abandonQuest(player, questId)) {
                    player.sendMessage(ChatColor.YELLOW + "Abandoned quest: " + quest.getName());
                    openActiveQuests(); // Refresh the GUI
                }
            }
        }
    }
    
    private void handleAvailableQuestsClick(int slot) {
        if (slot == 49) { // Back button
            openQuestHub();
            return;
        }
        
        // Find quest at this slot
        int questIndex = slot - 9;
        List<Quest> availableQuests = plugin.getQuestManager().getAvailableQuests(player);
        
        if (questIndex >= 0 && questIndex < availableQuests.size()) {
            Quest quest = availableQuests.get(questIndex);
            
            // Accept quest
            if (plugin.getQuestManager().startQuest(player, quest.getId())) {
                openAvailableQuests(); // Refresh
            }
        }
    }
    
    private void handleDailyQuestClick(int slot) {
        Quest dailyQuest = plugin.getQuestManager().getDailyQuest();
        if (dailyQuest == null) return;

        switch (slot) {
            case 15: // Action button
                // Check if quest is already active or completed
                if (data.getActiveQuests().containsKey(dailyQuest.getId())) {
                    player.sendMessage(ChatColor.YELLOW + "You already have this quest active!");
                } else if (data.getCompletedQuests().contains(dailyQuest.getId())) {
                    player.sendMessage(ChatColor.YELLOW + "You have already completed this quest!");
                } else if (plugin.getQuestManager().startQuest(player, dailyQuest.getId())) {
                    player.sendMessage(ChatColor.GREEN + "Quest accepted: " + dailyQuest.getName());
                    openDailyQuest(); // Refresh
                } else {
                    player.sendMessage(ChatColor.RED + "Failed to accept quest!");
                }
                break;
            case 22: // Back
                openQuestHub();
                break;
        }
    }

    private void handleCompletedQuestsClick(int slot) {
        if (slot == 49) { // Back button
            openQuestHub();
            return;
        }

        // Completed quests are just for viewing, no actions needed
        // Could add quest details view here in the future
    }

    private ItemStack createItem(Material material, String name, String... lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(name);
        
        List<String> loreList = new ArrayList<>();
        for (String line : lore) {
            loreList.add(line);
        }
        meta.setLore(loreList);
        
        item.setItemMeta(meta);
        return item;
    }
}
