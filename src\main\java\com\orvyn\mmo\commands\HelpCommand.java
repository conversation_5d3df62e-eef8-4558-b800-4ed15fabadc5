package com.orvyn.mmo.commands;

import com.orvyn.mmo.OrvynMMOPlugin;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class HelpCommand implements CommandExecutor, TabCompleter {
    
    private final OrvynMMOPlugin plugin;
    
    public HelpCommand(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        showMainHelp(sender);
        return true;
    }
    
    private void showMainHelp(CommandSender sender) {
        boolean isPlayer = sender instanceof Player;
        
        sender.sendMessage("");
        sender.sendMessage(ChatColor.GOLD + "╔═══════════════════════════════════════════════════════════╗");
        sender.sendMessage(ChatColor.GOLD + "║" + ChatColor.YELLOW + "                🎮 Welcome to OrvynMMO! 🎮                   " + ChatColor.GOLD + "║");
        sender.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
        sender.sendMessage(ChatColor.GOLD + "║" + ChatColor.AQUA + "      A complete MMO experience for Minecraft!             " + ChatColor.GOLD + "║");
        sender.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
        
        if (isPlayer) {
            sender.sendMessage(ChatColor.GOLD + "║" + ChatColor.YELLOW + "                   🚀 Getting Started                        " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.WHITE + "1. " + ChatColor.GREEN + "/ommo class" + ChatColor.GRAY + "           - Choose Warrior, Mage, or Archer      " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.WHITE + "2. " + ChatColor.GREEN + "/mychar stats" + ChatColor.GRAY + "         - Allocate your attribute points         " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.WHITE + "3. " + ChatColor.GREEN + "/mychar skills" + ChatColor.GRAY + "        - Learn new skills for your class         " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.WHITE + "4. " + ChatColor.GREEN + "/mychar skillbar" + ChatColor.GRAY + "      - Set up hotkeys (Sneak + 1-5)             " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.WHITE + "5. " + ChatColor.GREEN + "/mychar quests" + ChatColor.GRAY + "        - Accept quests to gain XP & rewards       " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
            sender.sendMessage(ChatColor.GOLD + "║" + ChatColor.YELLOW + "                    💎 Main Features                         " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.LIGHT_PURPLE + "⚔️  Classes:" + ChatColor.GRAY + " Warrior, Mage, Archer with unique skills    " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.LIGHT_PURPLE + "📊 Stats:" + ChatColor.GRAY + " STR, INT, AGI, VIT - customize your build      " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.LIGHT_PURPLE + "🎯 Skills:" + ChatColor.GRAY + " 20+ unique skills with hotkey system          " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.LIGHT_PURPLE + "📜 Quests:" + ChatColor.GRAY + " Daily quests, personal & shared objectives    " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.LIGHT_PURPLE + "⚒️  Professions:" + ChatColor.GRAY + " Mining, Farming, Combat progression      " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.LIGHT_PURPLE + "💎 Equipment:" + ChatColor.GRAY + " Enhance gear, socket gems, craft materials   " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.LIGHT_PURPLE + "👥 Parties:" + ChatColor.GRAY + " Team up with friends for adventures         " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
            sender.sendMessage(ChatColor.GOLD + "║" + ChatColor.YELLOW + "                   📋 Essential Commands                     " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.GREEN + "/mychar" + ChatColor.GRAY + "                - Main character management hub       " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.GREEN + "/mychar help" + ChatColor.GRAY + "            - Complete command list                  " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.GREEN + "/skill list" + ChatColor.GRAY + "             - View your unlocked skills             " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.GREEN + "/ommo class" + ChatColor.GRAY + "             - Change your class                     " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
            sender.sendMessage(ChatColor.GOLD + "║" + ChatColor.YELLOW + "                    🎮 How to Play                           " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.WHITE + "• Use skills:" + ChatColor.AQUA + " Hold Sneak + Press 1-5" + ChatColor.GRAY + "                  " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.WHITE + "• Level up:" + ChatColor.AQUA + " Kill mobs, complete quests, mine/farm" + ChatColor.GRAY + "       " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.WHITE + "• Get stronger:" + ChatColor.AQUA + " Enhance gear, socket gems" + ChatColor.GRAY + "             " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.WHITE + "• Team up:" + ChatColor.AQUA + " Create parties for group adventures" + ChatColor.GRAY + "        " + ChatColor.GOLD + "║");
        } else {
            sender.sendMessage(ChatColor.GOLD + "║" + ChatColor.RED + "                    Console Commands                        " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.RED + "/ommo reload" + ChatColor.GRAY + "             - Reload plugin configuration           " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.GRAY + "Players should use " + ChatColor.WHITE + "/mychar help" + ChatColor.GRAY + " for player commands   " + ChatColor.GOLD + "║");
        }
        
        sender.sendMessage(ChatColor.GOLD + "╚═══════════════════════════════════════════════════════════╝");
        sender.sendMessage("");
        
        if (isPlayer) {
            sender.sendMessage(ChatColor.LIGHT_PURPLE + "💡 " + ChatColor.YELLOW + "Tip: " + ChatColor.WHITE + "Type " + ChatColor.AQUA + "/mychar help" + ChatColor.WHITE + " for detailed command information!");
            sender.sendMessage("");
        }
    }

    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();

        if (args.length == 1) {
            // Help command can have topics like "commands", "classes", "skills", etc.
            completions.addAll(Arrays.asList("commands", "classes", "skills", "quests", "party", "professions", "getting-started"));
        }

        return completions;
    }
}
