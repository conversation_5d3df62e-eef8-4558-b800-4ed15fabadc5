package com.orvyn.mmo.gui;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.NamespacedKey;
import org.bukkit.persistence.PersistentDataType;

import java.util.ArrayList;
import java.util.List;

public class SkillBarManagementGUI implements Listener {

    private final OrvynMMOPlugin plugin;

    public SkillBarManagementGUI(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    public void openSkillBarGUI(Player player) {
        Inventory gui = Bukkit.createInventory(null, 54, ChatColor.DARK_BLUE + "Skill Bar Management");

        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        if (data == null) return;

        // Title
        ItemStack title = new ItemStack(Material.ENCHANTED_BOOK);
        ItemMeta titleMeta = title.getItemMeta();
        titleMeta.setDisplayName(ChatColor.GOLD + "Skill Bar Configuration");
        List<String> titleLore = new ArrayList<>();
        titleLore.add(ChatColor.GRAY + "Drag skills to slots 1-5");
        titleLore.add(ChatColor.GRAY + "Use F + [1-5] to activate skills");
        titleLore.add("");
        titleLore.add(ChatColor.YELLOW + "Hold F and press number keys to use skills!");
        titleMeta.setLore(titleLore);
        title.setItemMeta(titleMeta);
        gui.setItem(4, title);

        // Skill bar slots (slots 19-23)
        for (int i = 1; i <= 5; i++) {
            int slot = 18 + i; // Slots 19-23
            String skillId = data.getSkillBarSlot(i);
            
            if (skillId != null && !skillId.isEmpty()) {
                // Skill is equipped
                ItemStack skillItem = createSkillItem(skillId, data, true);
                gui.setItem(slot, skillItem);
            } else {
                // Empty slot
                ItemStack emptySlot = new ItemStack(Material.GRAY_STAINED_GLASS_PANE);
                ItemMeta emptyMeta = emptySlot.getItemMeta();
                emptyMeta.setDisplayName(ChatColor.GRAY + "Skill Slot " + i);
                List<String> emptyLore = new ArrayList<>();
                emptyLore.add(ChatColor.GRAY + "Drag a skill here to equip it");
                emptyLore.add("");
                emptyLore.add(ChatColor.YELLOW + "Hotkey: F + " + i);
                emptyMeta.setLore(emptyLore);
                emptySlot.setItemMeta(emptyMeta);
                gui.setItem(slot, emptySlot);
            }
        }

        // Available skills (slots 28-43)
        int skillSlot = 28;
        for (String skillId : data.getUnlockedSkills()) {
            if (skillSlot > 43) break; // Don't overflow
            
            ItemStack skillItem = createSkillItem(skillId, data, false);
            gui.setItem(skillSlot, skillItem);
            skillSlot++;
        }

        // Clear all button
        ItemStack clearAll = new ItemStack(Material.BARRIER);
        ItemMeta clearMeta = clearAll.getItemMeta();
        clearMeta.setDisplayName(ChatColor.RED + "Clear All Slots");
        List<String> clearLore = new ArrayList<>();
        clearLore.add(ChatColor.GRAY + "Remove all skills from the skill bar");
        clearLore.add("");
        clearLore.add(ChatColor.RED + "Click to clear all");
        clearMeta.setLore(clearLore);
        clearAll.setItemMeta(clearMeta);
        gui.setItem(49, clearAll);

        player.openInventory(gui);
    }

    private ItemStack createSkillItem(String skillId, PlayerData data, boolean isEquipped) {
        Material material = getSkillMaterial(skillId);
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();

        String skillName = formatSkillName(skillId);
        meta.setDisplayName((isEquipped ? ChatColor.GREEN : ChatColor.YELLOW) + skillName);

        // Store skill ID in persistent data for reliable retrieval
        NamespacedKey skillKey = new NamespacedKey(plugin, "skill_id");
        meta.getPersistentDataContainer().set(skillKey, PersistentDataType.STRING, skillId);

        List<String> lore = new ArrayList<>();
        lore.add(ChatColor.GRAY + "Skill ID: " + skillId);
        lore.add("");

        // Add skill description
        String description = getSkillDescription(skillId);
        lore.add(ChatColor.AQUA + description);
        lore.add("");

        // Mana cost
        int manaCost = getSkillManaCost(skillId);
        lore.add(ChatColor.BLUE + "Mana Cost: " + manaCost);
        
        // Cooldown
        int cooldown = getSkillCooldown(skillId);
        lore.add(ChatColor.GRAY + "Cooldown: " + cooldown + "s");
        
        if (isEquipped) {
            lore.add("");
            lore.add(ChatColor.GREEN + "✓ Equipped in skill bar");
            lore.add(ChatColor.GRAY + "Click to remove");
        } else {
            lore.add("");
            lore.add(ChatColor.YELLOW + "Click to equip in skill bar");
        }
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }

    private Material getSkillMaterial(String skillId) {
        switch (skillId.toLowerCase()) {
            case "fireball": return Material.FIRE_CHARGE;
            case "ice_blast": return Material.SNOWBALL;
            case "lightning_bolt": return Material.BLAZE_ROD;
            case "teleport": return Material.ENDER_PEARL;
            case "charge": return Material.IRON_SWORD;
            case "shield_bash": return Material.SHIELD;
            case "battle_cry": return Material.HORN_CORAL;
            case "whirlwind": return Material.IRON_AXE;
            case "power_shot": return Material.ARROW;
            case "multi_shot": return Material.SPECTRAL_ARROW;
            case "explosive_arrow": return Material.TNT;
            case "eagle_eye": return Material.SPYGLASS;
            default: return Material.BOOK;
        }
    }

    private String formatSkillName(String skillId) {
        String[] words = skillId.replace("_", " ").toLowerCase().split(" ");
        StringBuilder result = new StringBuilder();
        for (String word : words) {
            if (result.length() > 0) result.append(" ");
            if (word.length() > 0) {
                result.append(Character.toUpperCase(word.charAt(0)));
                if (word.length() > 1) {
                    result.append(word.substring(1));
                }
            }
        }
        return result.toString();
    }

    private String getSkillDescription(String skillId) {
        switch (skillId.toLowerCase()) {
            case "fireball": return "Launch a fiery projectile";
            case "ice_blast": return "Freeze enemies with ice";
            case "lightning_bolt": return "Strike with lightning";
            case "teleport": return "Instantly teleport forward";
            case "charge": return "Rush forward dealing damage";
            case "shield_bash": return "Stun enemies with your shield";
            case "battle_cry": return "Boost nearby allies";
            case "whirlwind": return "Spin attack hitting all nearby";
            case "power_shot": return "Charged arrow with extra damage";
            case "multi_shot": return "Fire multiple arrows";
            case "explosive_arrow": return "Arrow that explodes on impact";
            case "eagle_eye": return "Increase accuracy and range";
            default: return "A powerful skill";
        }
    }

    private int getSkillManaCost(String skillId) {
        switch (skillId.toLowerCase()) {
            case "fireball":
            case "ice_blast":
            case "lightning_bolt":
                return 20;
            case "teleport":
                return 30;
            case "charge":
            case "shield_bash":
                return 15;
            case "battle_cry":
            case "whirlwind":
                return 25;
            case "power_shot":
            case "multi_shot":
                return 10;
            case "explosive_arrow":
                return 20;
            case "eagle_eye":
                return 15;
            default:
                return 10;
        }
    }

    private int getSkillCooldown(String skillId) {
        switch (skillId.toLowerCase()) {
            case "fireball":
            case "ice_blast":
                return 3;
            case "lightning_bolt":
                return 5;
            case "teleport":
                return 10;
            case "charge":
                return 8;
            case "shield_bash":
                return 6;
            case "battle_cry":
                return 30;
            case "whirlwind":
                return 12;
            case "power_shot":
                return 2;
            case "multi_shot":
                return 4;
            case "explosive_arrow":
                return 8;
            case "eagle_eye":
                return 15;
            default:
                return 5;
        }
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!event.getView().getTitle().equals(ChatColor.DARK_BLUE + "Skill Bar Management")) {
            return;
        }

        event.setCancelled(true);
        
        if (!(event.getWhoClicked() instanceof Player)) return;
        Player player = (Player) event.getWhoClicked();
        
        ItemStack clicked = event.getCurrentItem();
        if (clicked == null || clicked.getType() == Material.AIR) return;

        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        if (data == null) return;

        int slot = event.getSlot();

        // Handle skill bar slots (19-23)
        if (slot >= 19 && slot <= 23) {
            int skillBarSlot = slot - 18; // Convert to 1-5
            data.setSkillBarSlot(skillBarSlot, null); // Remove skill
            player.sendMessage(ChatColor.YELLOW + "Removed skill from slot " + skillBarSlot);
            openSkillBarGUI(player); // Refresh GUI
            return;
        }

        // Handle available skills (28-43)
        if (slot >= 28 && slot <= 43) {
            // Get skill ID from persistent data
            NamespacedKey skillKey = new NamespacedKey(plugin, "skill_id");
            String skillId = clicked.getItemMeta().getPersistentDataContainer().get(skillKey, PersistentDataType.STRING);

            if (skillId != null) {
                // Check if skill is already equipped to prevent duplicates
                boolean alreadyEquipped = false;
                for (int i = 1; i <= 5; i++) {
                    if (skillId.equals(data.getSkillBarSlot(i))) {
                        alreadyEquipped = true;
                        break;
                    }
                }

                if (alreadyEquipped) {
                    player.sendMessage(ChatColor.RED + "This skill is already equipped!");
                    return;
                }

                // Find empty slot
                for (int i = 1; i <= 5; i++) {
                    if (data.getSkillBarSlot(i) == null) {
                        data.setSkillBarSlot(i, skillId);
                        String skillName = ChatColor.stripColor(clicked.getItemMeta().getDisplayName());
                        player.sendMessage(ChatColor.GREEN + "Equipped " + skillName + " to slot " + i);
                        openSkillBarGUI(player); // Refresh GUI
                        return;
                    }
                }

                player.sendMessage(ChatColor.RED + "Skill bar is full! Remove a skill first.");
            }
            return;
        }

        // Handle clear all button
        if (slot == 49 && clicked.getType() == Material.BARRIER) {
            data.clearSkillBar();
            player.sendMessage(ChatColor.YELLOW + "Cleared all skill bar slots");
            openSkillBarGUI(player); // Refresh GUI
        }
    }
}
