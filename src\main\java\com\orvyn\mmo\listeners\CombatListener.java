package com.orvyn.mmo.listeners;

import com.orvyn.mmo.OrvynMMOPlugin;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.entity.Player;

public class CombatListener implements Listener {
    
    private final OrvynMMOPlugin plugin;
    
    public CombatListener(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler
    public void onEntityDamageByEntity(EntityDamageByEntityEvent event) {
        if (event.getDamager() instanceof Player) {
            Player player = (Player) event.getDamager();
            
            // Custom combat handling would go here
            // For now, let vanilla damage work
        }
    }
}
