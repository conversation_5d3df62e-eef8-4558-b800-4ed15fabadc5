package com.orvyn.mmo.skills;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import org.bukkit.*;
import org.bukkit.entity.*;
import org.bukkit.event.Listener;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;

import java.util.*;

public class SkillExecutor implements Listener {
    
    private final OrvynMMOPlugin plugin;
    private final Map<String, SkillDefinition> skills = new HashMap<>();
    
    public SkillExecutor(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
        registerSkills();
    }
    
    private void registerSkills() {
        // Warrior Skills
        registerWarriorSkills();

        // Mage Skills
        registerMageSkills();

        // Archer Skills
        registerArcherSkills();

        // Rogue Skills
        registerRogueSkills();

        // Paladin Skills
        registerPaladinSkills();

        // Druid Skills
        registerDruidSkills();
    }
    
    private void registerWarriorSkills() {
        // Charge - Dash forward and deal damage
        skills.put("charge", new SkillDefinition("charge", 8000, 20, (player, data) -> {
            Vector direction = player.getLocation().getDirection().normalize();
            Vector velocity = direction.multiply(2.0);
            velocity.setY(0.3); // Add slight upward movement
            
            player.setVelocity(velocity);
            player.getWorld().spawnParticle(Particle.CLOUD, player.getLocation(), 20, 0.5, 0.5, 0.5, 0.1);
            player.playSound(player.getLocation(), Sound.ENTITY_ENDER_DRAGON_FLAP, 1.0f, 1.5f);
            
            // Damage nearby enemies after a short delay
            new BukkitRunnable() {
                @Override
                public void run() {
                    for (Entity entity : player.getNearbyEntities(3, 3, 3)) {
                        if (entity instanceof LivingEntity && !(entity instanceof Player)) {
                            LivingEntity target = (LivingEntity) entity;
                            target.damage(8.0, player);
                            target.getWorld().spawnParticle(Particle.CRIT, target.getLocation().add(0, 1, 0), 10);
                        }
                    }
                }
            }.runTaskLater(plugin, 10L);
            
            return true;
        }));
        
        // Shield Bash - Stun enemies
        skills.put("shield_bash", new SkillDefinition("shield_bash", 12000, 25, (player, data) -> {
            for (Entity entity : player.getNearbyEntities(4, 4, 4)) {
                if (entity instanceof LivingEntity && !(entity instanceof Player)) {
                    LivingEntity target = (LivingEntity) entity;
                    target.damage(6.0, player);
                    target.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 60, 2)); // 3 seconds stun
                    target.getWorld().spawnParticle(Particle.EXPLOSION, target.getLocation(), 1);
                    target.getWorld().playSound(target.getLocation(), Sound.ENTITY_ZOMBIE_ATTACK_IRON_DOOR, 1.0f, 0.8f);
                }
            }
            
            player.getWorld().spawnParticle(Particle.SWEEP_ATTACK, player.getLocation().add(0, 1, 0), 5);
            player.playSound(player.getLocation(), Sound.ITEM_SHIELD_BLOCK, 1.0f, 0.8f);
            
            return true;
        }));
        
        // Battle Cry - Boost damage
        skills.put("battle_cry", new SkillDefinition("battle_cry", 20000, 30, (player, data) -> {
            player.addPotionEffect(new PotionEffect(PotionEffectType.STRENGTH, 200, 1)); // 10 seconds
            player.addPotionEffect(new PotionEffect(PotionEffectType.RESISTANCE, 200, 0)); // 10 seconds
            
            player.getWorld().spawnParticle(Particle.FLAME, player.getLocation().add(0, 1, 0), 30, 1, 1, 1, 0.1);
            player.playSound(player.getLocation(), Sound.ENTITY_ENDER_DRAGON_GROWL, 1.0f, 1.2f);
            
            // Broadcast effect to nearby players
            for (Entity entity : player.getNearbyEntities(10, 10, 10)) {
                if (entity instanceof Player) {
                    Player ally = (Player) entity;
                    ally.sendMessage(ChatColor.GOLD + player.getName() + " lets out a mighty battle cry!");
                }
            }
            
            return true;
        }));
        
        // Whirlwind - Spin attack
        skills.put("whirlwind", new SkillDefinition("whirlwind", 15000, 40, (player, data) -> {
            Location center = player.getLocation();
            
            // Create spinning effect
            new BukkitRunnable() {
                int ticks = 0;
                
                @Override
                public void run() {
                    if (ticks >= 20) { // 1 second duration
                        cancel();
                        return;
                    }
                    
                    // Damage nearby enemies
                    for (Entity entity : player.getNearbyEntities(4, 4, 4)) {
                        if (entity instanceof LivingEntity && !(entity instanceof Player)) {
                            LivingEntity target = (LivingEntity) entity;
                            target.damage(4.0, player);
                            
                            // Knockback effect
                            Vector knockback = target.getLocation().subtract(center).toVector().normalize();
                            knockback.setY(0.3);
                            target.setVelocity(knockback);
                        }
                    }
                    
                    // Visual effects
                    for (int i = 0; i < 8; i++) {
                        double angle = (ticks * 18 + i * 45) * Math.PI / 180;
                        double x = center.getX() + Math.cos(angle) * 3;
                        double z = center.getZ() + Math.sin(angle) * 3;
                        Location particleLoc = new Location(center.getWorld(), x, center.getY() + 1, z);
                        center.getWorld().spawnParticle(Particle.SWEEP_ATTACK, particleLoc, 1);
                    }
                    
                    if (ticks % 5 == 0) {
                        player.playSound(center, Sound.ENTITY_PLAYER_ATTACK_SWEEP, 1.0f, 1.0f + (ticks * 0.1f));
                    }
                    
                    ticks++;
                }
            }.runTaskTimer(plugin, 0L, 1L);
            
            return true;
        }));
    }
    
    private void registerMageSkills() {
        // Fireball - Launch burning projectile
        skills.put("fireball", new SkillDefinition("fireball", 3000, 15, (player, data) -> {
            Fireball fireball = player.launchProjectile(Fireball.class);
            fireball.setYield(2.0f); // Explosion power
            fireball.setIsIncendiary(true);
            
            player.playSound(player.getLocation(), Sound.ENTITY_GHAST_SHOOT, 1.0f, 1.2f);
            player.getWorld().spawnParticle(Particle.FLAME, player.getLocation().add(0, 1.5, 0), 10, 0.3, 0.3, 0.3, 0.1);
            
            return true;
        }));
        
        // Ice Blast - Freeze enemies
        skills.put("ice_blast", new SkillDefinition("ice_blast", 5000, 20, (player, data) -> {
            Vector direction = player.getLocation().getDirection();
            Location start = player.getEyeLocation();
            
            // Create ice projectile effect
            new BukkitRunnable() {
                int distance = 0;
                
                @Override
                public void run() {
                    if (distance >= 20) {
                        cancel();
                        return;
                    }
                    
                    Location current = start.clone().add(direction.clone().multiply(distance));
                    current.getWorld().spawnParticle(Particle.SNOWFLAKE, current, 5, 0.2, 0.2, 0.2, 0.01);
                    
                    // Check for entities to freeze
                    for (Entity entity : current.getWorld().getNearbyEntities(current, 2, 2, 2)) {
                        if (entity instanceof LivingEntity && !(entity instanceof Player) && entity.getLocation().distance(current) <= 2) {
                            LivingEntity target = (LivingEntity) entity;
                            target.damage(6.0, player);
                            target.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 100, 3)); // 5 seconds freeze
                            target.getWorld().spawnParticle(Particle.BLOCK, target.getLocation().add(0, 1, 0), 20, 0.5, 1, 0.5, 0.1, Material.ICE.createBlockData());
                            target.getWorld().playSound(target.getLocation(), Sound.BLOCK_GLASS_BREAK, 1.0f, 0.5f);
                            cancel();
                            return;
                        }
                    }
                    
                    distance++;
                }
            }.runTaskTimer(plugin, 0L, 1L);
            
            player.playSound(player.getLocation(), Sound.BLOCK_SNOW_BREAK, 1.0f, 0.8f);
            
            return true;
        }));
        
        // Lightning Bolt - Strike with lightning
        skills.put("lightning_bolt", new SkillDefinition("lightning_bolt", 8000, 35, (player, data) -> {
            Location target = player.getTargetBlock(null, 20).getLocation().add(0, 1, 0);
            
            // Strike lightning
            target.getWorld().strikeLightningEffect(target);
            
            // Damage nearby entities
            for (Entity entity : target.getWorld().getNearbyEntities(target, 3, 3, 3)) {
                if (entity instanceof LivingEntity && !(entity instanceof Player)) {
                    LivingEntity mob = (LivingEntity) entity;
                    mob.damage(12.0, player);
                    mob.getWorld().spawnParticle(Particle.ELECTRIC_SPARK, mob.getLocation().add(0, 1, 0), 20);
                }
            }
            
            player.playSound(player.getLocation(), Sound.ENTITY_LIGHTNING_BOLT_THUNDER, 1.0f, 1.0f);
            
            return true;
        }));
        
        // Teleport - Instant movement
        skills.put("teleport", new SkillDefinition("teleport", 10000, 40, (player, data) -> {
            Location target = player.getTargetBlock(null, 15).getLocation().add(0, 1, 0);
            
            // Check if location is safe
            if (target.getBlock().getType().isSolid() || target.clone().add(0, 1, 0).getBlock().getType().isSolid()) {
                player.sendMessage(ChatColor.RED + "Cannot teleport to that location!");
                return false;
            }
            
            Location oldLocation = player.getLocation().clone();
            
            // Teleport effects
            oldLocation.getWorld().spawnParticle(Particle.PORTAL, oldLocation.add(0, 1, 0), 50, 0.5, 1, 0.5, 0.1);
            target.getWorld().spawnParticle(Particle.PORTAL, target, 50, 0.5, 1, 0.5, 0.1);
            
            player.teleport(target);
            player.playSound(oldLocation, Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.0f);
            player.playSound(target, Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.0f);
            
            return true;
        }));
    }
    
    private void registerArcherSkills() {
        // Power Shot - Powerful arrow
        skills.put("power_shot", new SkillDefinition("power_shot", 4000, 15, (player, data) -> {
            Arrow arrow = player.launchProjectile(Arrow.class);
            arrow.setVelocity(arrow.getVelocity().multiply(2.0));
            arrow.setDamage(arrow.getDamage() * 2.5);
            arrow.setCritical(true);
            
            player.playSound(player.getLocation(), Sound.ENTITY_ARROW_SHOOT, 1.0f, 0.8f);
            player.getWorld().spawnParticle(Particle.CRIT, player.getLocation().add(0, 1.5, 0), 10, 0.3, 0.3, 0.3, 0.1);
            
            return true;
        }));
        
        // Multi Shot - Fire multiple arrows
        skills.put("multi_shot", new SkillDefinition("multi_shot", 6000, 25, (player, data) -> {
            Vector baseDirection = player.getLocation().getDirection();
            
            // Fire 5 arrows in a spread
            for (int i = -2; i <= 2; i++) {
                Vector direction = baseDirection.clone();
                direction.rotateAroundY(Math.toRadians(i * 10)); // 10 degree spread
                
                Arrow arrow = player.launchProjectile(Arrow.class);
                arrow.setVelocity(direction.multiply(1.5));
                arrow.setDamage(arrow.getDamage() * 1.2);
            }
            
            player.playSound(player.getLocation(), Sound.ENTITY_ARROW_SHOOT, 1.0f, 1.2f);
            player.getWorld().spawnParticle(Particle.CRIT, player.getLocation().add(0, 1.5, 0), 20, 0.5, 0.5, 0.5, 0.1);
            
            return true;
        }));
        
        // Explosive Arrow - Arrow that explodes
        skills.put("explosive_arrow", new SkillDefinition("explosive_arrow", 10000, 40, (player, data) -> {
            Arrow arrow = player.launchProjectile(Arrow.class);
            arrow.setVelocity(arrow.getVelocity().multiply(1.5));
            
            // Mark arrow as explosive
            arrow.setMetadata("explosive", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
            
            player.playSound(player.getLocation(), Sound.ENTITY_TNT_PRIMED, 1.0f, 1.5f);
            player.getWorld().spawnParticle(Particle.FLAME, player.getLocation().add(0, 1.5, 0), 15, 0.3, 0.3, 0.3, 0.1);
            
            return true;
        }));
        
        // Eagle Eye - Increase accuracy and range
        skills.put("eagle_eye", new SkillDefinition("eagle_eye", 15000, 20, (player, data) -> {
            player.addPotionEffect(new PotionEffect(PotionEffectType.NIGHT_VISION, 400, 0)); // 20 seconds
            player.addPotionEffect(new PotionEffect(PotionEffectType.SLOW_FALLING, 400, 0)); // 20 seconds
            
            player.sendMessage(ChatColor.YELLOW + "Your vision sharpens! Accuracy and range increased!");
            player.playSound(player.getLocation(), Sound.ENTITY_PHANTOM_AMBIENT, 1.0f, 1.5f);
            player.getWorld().spawnParticle(Particle.END_ROD, player.getLocation().add(0, 2, 0), 10, 0.5, 0.5, 0.5, 0.1);
            
            return true;
        }));
    }

    private void registerRogueSkills() {
        // Stealth - Become invisible
        skills.put("stealth", new SkillDefinition("stealth", 8000, 15, (player, data) -> {
            player.addPotionEffect(new PotionEffect(PotionEffectType.INVISIBILITY, 100, 0)); // 5 seconds
            player.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, 100, 1)); // 5 seconds speed boost

            player.sendMessage(ChatColor.DARK_PURPLE + "You fade into the shadows...");
            player.playSound(player.getLocation(), Sound.ENTITY_ENDERMAN_TELEPORT, 0.5f, 1.5f);
            player.getWorld().spawnParticle(Particle.SMOKE, player.getLocation().add(0, 1, 0), 20, 0.5, 1, 0.5, 0.1);

            return true;
        }));

        // Backstab - Deal extra damage from behind
        skills.put("backstab", new SkillDefinition("backstab", 6000, 20, (player, data) -> {
            // Find target in front of player
            for (Entity entity : player.getNearbyEntities(3, 3, 3)) {
                if (entity instanceof LivingEntity && !(entity instanceof Player)) {
                    LivingEntity target = (LivingEntity) entity;

                    // Check if behind target
                    Vector playerToTarget = target.getLocation().toVector().subtract(player.getLocation().toVector()).normalize();
                    Vector targetDirection = target.getLocation().getDirection().normalize();

                    if (playerToTarget.dot(targetDirection) > 0.5) { // Behind target
                        target.damage(12.0, player); // High damage
                        target.getWorld().spawnParticle(Particle.CRIT, target.getLocation().add(0, 1, 0), 15);
                        player.sendMessage(ChatColor.RED + "Critical backstab!");
                    } else {
                        target.damage(6.0, player); // Normal damage
                    }

                    player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_ATTACK_CRIT, 1.0f, 1.2f);
                    return true;
                }
            }

            player.sendMessage(ChatColor.RED + "No target found!");
            return false;
        }));

        // Poison Blade - Coat weapon with poison
        skills.put("poison_blade", new SkillDefinition("poison_blade", 30000, 25, (player, data) -> {
            // Add temporary poison effect to weapon attacks
            player.sendMessage(ChatColor.GREEN + "Your blade drips with poison...");
            player.playSound(player.getLocation(), Sound.ENTITY_SPIDER_AMBIENT, 1.0f, 0.8f);
            player.getWorld().spawnParticle(Particle.DRIPPING_WATER, player.getLocation().add(0, 1, 0), 10, 0.3, 0.3, 0.3, 0.1);

            // Give player a temporary effect to track poison blade
            player.addPotionEffect(new PotionEffect(PotionEffectType.LUCK, 600, 0)); // 30 seconds marker

            return true;
        }));

        // Shadow Step - Teleport behind target
        skills.put("shadow_step", new SkillDefinition("shadow_step", 12000, 35, (player, data) -> {
            Entity target = null;
            double minDistance = Double.MAX_VALUE;

            // Find nearest enemy
            for (Entity entity : player.getNearbyEntities(8, 8, 8)) {
                if (entity instanceof LivingEntity && !(entity instanceof Player)) {
                    double distance = entity.getLocation().distance(player.getLocation());
                    if (distance < minDistance) {
                        minDistance = distance;
                        target = entity;
                    }
                }
            }

            if (target != null) {
                // Teleport behind target
                Vector direction = target.getLocation().getDirection().normalize();
                Location teleportLoc = target.getLocation().subtract(direction.multiply(2));
                teleportLoc.setY(target.getLocation().getY());

                player.teleport(teleportLoc);
                player.sendMessage(ChatColor.DARK_PURPLE + "You step through the shadows!");
                player.playSound(player.getLocation(), Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.0f);
                player.getWorld().spawnParticle(Particle.PORTAL, player.getLocation(), 20, 0.5, 1, 0.5, 0.1);

                return true;
            } else {
                player.sendMessage(ChatColor.RED + "No target found within range!");
                return false;
            }
        }));

        // Smoke Bomb - Create concealing smoke
        skills.put("smoke_bomb", new SkillDefinition("smoke_bomb", 20000, 40, (player, data) -> {
            Location center = player.getLocation();

            // Create smoke effect
            for (int i = 0; i < 50; i++) {
                Location smokeLoc = center.clone().add(
                    (Math.random() - 0.5) * 6,
                    Math.random() * 3,
                    (Math.random() - 0.5) * 6
                );
                player.getWorld().spawnParticle(Particle.SMOKE, smokeLoc, 1);
            }

            // Blind nearby enemies
            for (Entity entity : player.getNearbyEntities(5, 5, 5)) {
                if (entity instanceof LivingEntity && !(entity instanceof Player)) {
                    LivingEntity target = (LivingEntity) entity;
                    target.addPotionEffect(new PotionEffect(PotionEffectType.BLINDNESS, 100, 1)); // 5 seconds
                }
            }

            player.sendMessage(ChatColor.GRAY + "Smoke fills the area!");
            player.playSound(player.getLocation(), Sound.ENTITY_TNT_PRIMED, 0.5f, 0.8f);

            return true;
        }));

        // Assassinate - Instant kill low health enemies
        skills.put("assassinate", new SkillDefinition("assassinate", 60000, 80, (player, data) -> {
            for (Entity entity : player.getNearbyEntities(3, 3, 3)) {
                if (entity instanceof LivingEntity && !(entity instanceof Player)) {
                    LivingEntity target = (LivingEntity) entity;

                    double maxHealth = target.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).getValue();
                    if (target.getHealth() <= maxHealth * 0.25) { // 25% health or less
                        target.damage(maxHealth, player); // Instant kill
                        player.sendMessage(ChatColor.DARK_RED + "ASSASSINATION!");
                        player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_ATTACK_CRIT, 1.0f, 0.5f);
                        target.getWorld().spawnParticle(Particle.BLOCK, target.getLocation(), 20, 0.5, 1, 0.5, 0.1, Material.REDSTONE_BLOCK.createBlockData());
                        return true;
                    }
                }
            }

            player.sendMessage(ChatColor.RED + "No suitable target for assassination!");
            return false;
        }));

        // Shadow Clone - Create a duplicate
        skills.put("shadow_clone", new SkillDefinition("shadow_clone", 120000, 120, (player, data) -> {
            // Spawn armor stand as shadow clone
            Location cloneLoc = player.getLocation().add(1, 0, 1);
            org.bukkit.entity.ArmorStand clone = player.getWorld().spawn(cloneLoc, org.bukkit.entity.ArmorStand.class);

            clone.setCustomName(ChatColor.DARK_GRAY + player.getName() + "'s Shadow");
            clone.setCustomNameVisible(true);
            clone.setGravity(false);
            clone.setVisible(false);
            clone.setMarker(false);

            // Remove after 20 seconds
            new BukkitRunnable() {
                @Override
                public void run() {
                    if (clone.isValid()) {
                        clone.getWorld().spawnParticle(Particle.SMOKE, clone.getLocation(), 10);
                        clone.remove();
                    }
                }
            }.runTaskLater(plugin, 400L);

            player.sendMessage(ChatColor.DARK_PURPLE + "A shadow clone appears!");
            player.playSound(player.getLocation(), Sound.ENTITY_ENDERMAN_SCREAM, 0.5f, 1.5f);
            player.getWorld().spawnParticle(Particle.PORTAL, cloneLoc, 30, 0.5, 1, 0.5, 0.1);

            return true;
        }));
    }

    private void registerPaladinSkills() {
        // Holy Strike - Divine damage attack
        skills.put("holy_strike", new SkillDefinition("holy_strike", 5000, 20, (player, data) -> {
            for (Entity entity : player.getNearbyEntities(4, 4, 4)) {
                if (entity instanceof LivingEntity && !(entity instanceof Player)) {
                    LivingEntity target = (LivingEntity) entity;
                    target.damage(8.0, player);
                    target.getWorld().spawnParticle(Particle.END_ROD, target.getLocation().add(0, 1, 0), 10);
                    target.getWorld().playSound(target.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.5f);
                }
            }

            player.sendMessage(ChatColor.GOLD + "Divine power flows through your weapon!");
            player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_ATTACK_SWEEP, 1.0f, 1.2f);
            player.getWorld().spawnParticle(Particle.END_ROD, player.getLocation().add(0, 1, 0), 15, 0.5, 1, 0.5, 0.1);

            return true;
        }));

        // Divine Protection - Reduce incoming damage
        skills.put("divine_protection", new SkillDefinition("divine_protection", 15000, 25, (player, data) -> {
            player.addPotionEffect(new PotionEffect(PotionEffectType.RESISTANCE, 300, 1)); // 15 seconds
            player.addPotionEffect(new PotionEffect(PotionEffectType.GLOWING, 300, 0)); // Visual effect

            player.sendMessage(ChatColor.GOLD + "Divine protection surrounds you!");
            player.playSound(player.getLocation(), Sound.BLOCK_ENCHANTMENT_TABLE_USE, 1.0f, 1.2f);
            player.getWorld().spawnParticle(Particle.ENCHANT, player.getLocation().add(0, 1, 0), 20, 0.5, 1, 0.5, 0.1);

            return true;
        }));

        // Heal - Restore health
        skills.put("heal", new SkillDefinition("heal", 8000, 30, (player, data) -> {
            double healAmount = 6.0; // 3 hearts
            double currentHealth = player.getHealth();
            double maxHealth = player.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).getValue();

            player.setHealth(Math.min(maxHealth, currentHealth + healAmount));

            player.sendMessage(ChatColor.GREEN + "Divine light heals your wounds!");
            player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.5f);
            player.getWorld().spawnParticle(Particle.HEART, player.getLocation().add(0, 2, 0), 10, 0.5, 0.5, 0.5, 0.1);

            return true;
        }));

        // Consecration - Sanctify ground
        skills.put("consecration", new SkillDefinition("consecration", 20000, 40, (player, data) -> {
            Location center = player.getLocation();

            // Create consecrated ground effect
            for (int x = -3; x <= 3; x++) {
                for (int z = -3; z <= 3; z++) {
                    Location loc = center.clone().add(x, 0, z);
                    if (loc.distance(center) <= 3) {
                        loc.getWorld().spawnParticle(Particle.END_ROD, loc.add(0, 0.1, 0), 1);
                    }
                }
            }

            // Damage undead in area
            for (Entity entity : player.getNearbyEntities(5, 5, 5)) {
                if (entity instanceof LivingEntity && !(entity instanceof Player)) {
                    LivingEntity target = (LivingEntity) entity;
                    if (target.getType().name().contains("ZOMBIE") || target.getType().name().contains("SKELETON") ||
                        target.getType().name().contains("WITHER")) {
                        target.damage(10.0, player);
                        target.getWorld().spawnParticle(Particle.FLAME, target.getLocation(), 5);
                    }
                }
            }

            player.sendMessage(ChatColor.GOLD + "The ground is consecrated!");
            player.playSound(player.getLocation(), Sound.BLOCK_BEACON_ACTIVATE, 1.0f, 1.2f);

            return true;
        }));

        // Divine Shield - Temporary immunity
        skills.put("divine_shield", new SkillDefinition("divine_shield", 60000, 50, (player, data) -> {
            player.addPotionEffect(new PotionEffect(PotionEffectType.RESISTANCE, 60, 4)); // 3 seconds near-immunity
            player.addPotionEffect(new PotionEffect(PotionEffectType.GLOWING, 60, 0)); // Visual effect
            player.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 60, 2)); // Movement penalty

            player.sendMessage(ChatColor.GOLD + "Divine shield protects you!");
            player.playSound(player.getLocation(), Sound.ITEM_TOTEM_USE, 1.0f, 1.0f);
            player.getWorld().spawnParticle(Particle.TOTEM_OF_UNDYING, player.getLocation().add(0, 1, 0), 30, 0.5, 1, 0.5, 0.1);

            return true;
        }));

        // Smite - Divine lightning
        skills.put("smite", new SkillDefinition("smite", 30000, 80, (player, data) -> {
            Location target = player.getTargetBlock(null, 20).getLocation();

            // Strike lightning
            target.getWorld().strikeLightningEffect(target);

            // Damage enemies in area
            for (Entity entity : target.getWorld().getNearbyEntities(target, 3, 3, 3)) {
                if (entity instanceof LivingEntity && !(entity instanceof Player)) {
                    LivingEntity livingTarget = (LivingEntity) entity;
                    livingTarget.damage(15.0, player);
                    livingTarget.getWorld().spawnParticle(Particle.ELECTRIC_SPARK, livingTarget.getLocation(), 10);
                }
            }

            player.sendMessage(ChatColor.YELLOW + "Divine lightning strikes your enemies!");
            player.playSound(player.getLocation(), Sound.ENTITY_LIGHTNING_BOLT_THUNDER, 1.0f, 1.0f);

            return true;
        }));

        // Resurrection - Revive fallen allies (placeholder - would need more complex implementation)
        skills.put("resurrection", new SkillDefinition("resurrection", 300000, 150, (player, data) -> {
            // For now, just heal nearby players significantly
            boolean healed = false;
            for (Entity entity : player.getNearbyEntities(5, 5, 5)) {
                if (entity instanceof Player) {
                    Player target = (Player) entity;
                    if (target.getHealth() < target.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).getValue() * 0.5) {
                        double maxHealth = target.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).getValue();
                        target.setHealth(maxHealth);
                        target.sendMessage(ChatColor.GOLD + player.getName() + " has blessed you with divine healing!");
                        target.getWorld().spawnParticle(Particle.TOTEM_OF_UNDYING, target.getLocation(), 20);
                        healed = true;
                    }
                }
            }

            if (healed) {
                player.sendMessage(ChatColor.GOLD + "Divine power flows through your allies!");
                player.playSound(player.getLocation(), Sound.ITEM_TOTEM_USE, 1.0f, 0.8f);
                return true;
            } else {
                player.sendMessage(ChatColor.RED + "No allies in need of divine intervention nearby!");
                return false;
            }
        }));
    }

    private void registerDruidSkills() {
        // Nature Bolt - Launch nature energy
        skills.put("nature_bolt", new SkillDefinition("nature_bolt", 2000, 12, (player, data) -> {
            Vector direction = player.getLocation().getDirection();
            Location start = player.getEyeLocation();

            new BukkitRunnable() {
                int distance = 0;
                Location current = start.clone();

                @Override
                public void run() {
                    if (distance >= 20) {
                        cancel();
                        return;
                    }

                    current.add(direction);
                    current.getWorld().spawnParticle(Particle.HAPPY_VILLAGER, current, 3, 0.1, 0.1, 0.1, 0.1);

                    // Check for entities to damage
                    for (Entity entity : current.getWorld().getNearbyEntities(current, 1, 1, 1)) {
                        if (entity instanceof LivingEntity && !(entity instanceof Player) && entity.getLocation().distance(current) <= 1) {
                            LivingEntity target = (LivingEntity) entity;
                            target.damage(5.0, player);
                            target.getWorld().spawnParticle(Particle.HAPPY_VILLAGER, target.getLocation().add(0, 1, 0), 10);
                            target.getWorld().playSound(target.getLocation(), Sound.BLOCK_GRASS_BREAK, 1.0f, 1.2f);
                            cancel();
                            return;
                        }
                    }

                    distance++;
                }
            }.runTaskTimer(plugin, 0L, 1L);

            player.playSound(player.getLocation(), Sound.BLOCK_GRASS_BREAK, 1.0f, 1.5f);
            player.getWorld().spawnParticle(Particle.HAPPY_VILLAGER, player.getLocation().add(0, 1.5, 0), 5, 0.3, 0.3, 0.3, 0.1);

            return true;
        }));

        // Entangle - Root enemies
        skills.put("entangle", new SkillDefinition("entangle", 8000, 20, (player, data) -> {
            for (Entity entity : player.getNearbyEntities(6, 6, 6)) {
                if (entity instanceof LivingEntity && !(entity instanceof Player)) {
                    LivingEntity target = (LivingEntity) entity;
                    target.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 100, 4)); // 5 seconds immobilize
                    target.getWorld().spawnParticle(Particle.HAPPY_VILLAGER, target.getLocation(), 15, 0.5, 1, 0.5, 0.1);

                    // Create vine effect around target
                    Location targetLoc = target.getLocation();
                    for (int i = 0; i < 8; i++) {
                        double angle = i * Math.PI / 4;
                        Location vineLoc = targetLoc.clone().add(Math.cos(angle), 0, Math.sin(angle));
                        vineLoc.getWorld().spawnParticle(Particle.HAPPY_VILLAGER, vineLoc, 1);
                    }
                }
            }

            player.sendMessage(ChatColor.GREEN + "Vines entangle your enemies!");
            player.playSound(player.getLocation(), Sound.BLOCK_GRASS_BREAK, 1.0f, 0.8f);

            return true;
        }));

        // Bark Skin - Increase natural armor
        skills.put("bark_skin", new SkillDefinition("bark_skin", 30000, 25, (player, data) -> {
            player.addPotionEffect(new PotionEffect(PotionEffectType.RESISTANCE, 600, 1)); // 30 seconds
            player.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 600, 0)); // Slight movement penalty

            player.sendMessage(ChatColor.DARK_GREEN + "Your skin hardens like bark!");
            player.playSound(player.getLocation(), Sound.BLOCK_WOOD_BREAK, 1.0f, 0.8f);
            player.getWorld().spawnParticle(Particle.BLOCK, player.getLocation().add(0, 1, 0), 15, 0.5, 1, 0.5, 0.1, Material.OAK_LOG.createBlockData());

            return true;
        }));

        // Wolf Form - Transform into wolf
        skills.put("wolf_form", new SkillDefinition("wolf_form", 60000, 50, (player, data) -> {
            player.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, 1200, 2)); // 60 seconds speed
            player.addPotionEffect(new PotionEffect(PotionEffectType.JUMP_BOOST, 1200, 1)); // 60 seconds jump
            player.addPotionEffect(new PotionEffect(PotionEffectType.NIGHT_VISION, 1200, 0)); // 60 seconds night vision

            player.sendMessage(ChatColor.GRAY + "You take the form of a wolf!");
            player.playSound(player.getLocation(), Sound.ENTITY_WOLF_AMBIENT, 1.0f, 0.8f);
            player.getWorld().spawnParticle(Particle.SMOKE, player.getLocation().add(0, 1, 0), 20, 0.5, 1, 0.5, 0.1);

            return true;
        }));

        // Nature Heal - Healing using nature's power
        skills.put("nature_heal", new SkillDefinition("nature_heal", 12000, 40, (player, data) -> {
            double healAmount = 8.0; // 4 hearts
            double currentHealth = player.getHealth();
            double maxHealth = player.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).getValue();

            player.setHealth(Math.min(maxHealth, currentHealth + healAmount));

            // Also heal nearby players
            for (Entity entity : player.getNearbyEntities(5, 5, 5)) {
                if (entity instanceof Player) {
                    Player target = (Player) entity;
                    double targetCurrent = target.getHealth();
                    double targetMax = target.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).getValue();
                    target.setHealth(Math.min(targetMax, targetCurrent + healAmount / 2));
                    target.sendMessage(ChatColor.GREEN + "Nature's healing flows through you!");
                }
            }

            player.sendMessage(ChatColor.GREEN + "Nature restores your vitality!");
            player.playSound(player.getLocation(), Sound.BLOCK_GRASS_BREAK, 1.0f, 1.2f);
            player.getWorld().spawnParticle(Particle.HAPPY_VILLAGER, player.getLocation().add(0, 1, 0), 20, 1, 1, 1, 0.1);

            return true;
        }));

        // Earthquake - Shake the earth
        skills.put("earthquake", new SkillDefinition("earthquake", 45000, 100, (player, data) -> {
            Location center = player.getLocation();

            // Create earthquake effect
            for (Entity entity : player.getNearbyEntities(15, 15, 15)) {
                if (entity instanceof LivingEntity && !(entity instanceof Player)) {
                    LivingEntity target = (LivingEntity) entity;
                    target.damage(10.0, player);
                    target.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 100, 2)); // 5 seconds slow

                    // Knockback effect
                    Vector direction = target.getLocation().toVector().subtract(center.toVector()).normalize();
                    direction.setY(0.5);
                    target.setVelocity(direction.multiply(1.5));
                }
            }

            // Visual effects
            for (int i = 0; i < 50; i++) {
                Location effectLoc = center.clone().add(
                    (Math.random() - 0.5) * 30,
                    0,
                    (Math.random() - 0.5) * 30
                );
                effectLoc.getWorld().spawnParticle(Particle.BLOCK, effectLoc, 3, 0.1, 0.1, 0.1, 0.1, Material.DIRT.createBlockData());
            }

            player.sendMessage(ChatColor.DARK_GREEN + "The earth trembles beneath your power!");
            player.playSound(player.getLocation(), Sound.ENTITY_ENDER_DRAGON_GROWL, 1.0f, 0.5f);

            return true;
        }));

        // Storm Call - Summon lightning storm
        skills.put("storm_call", new SkillDefinition("storm_call", 120000, 120, (player, data) -> {
            Location center = player.getLocation();

            // Create multiple lightning strikes over time
            new BukkitRunnable() {
                int strikes = 0;

                @Override
                public void run() {
                    if (strikes >= 10) {
                        cancel();
                        return;
                    }

                    // Random location within 20 blocks
                    Location strikeLoc = center.clone().add(
                        (Math.random() - 0.5) * 40,
                        0,
                        (Math.random() - 0.5) * 40
                    );

                    strikeLoc.getWorld().strikeLightningEffect(strikeLoc);

                    // Damage enemies near strike
                    for (Entity entity : strikeLoc.getWorld().getNearbyEntities(strikeLoc, 3, 3, 3)) {
                        if (entity instanceof LivingEntity && !(entity instanceof Player)) {
                            LivingEntity target = (LivingEntity) entity;
                            target.damage(8.0, player);
                        }
                    }

                    strikes++;
                }
            }.runTaskTimer(plugin, 0L, 20L); // Strike every second

            player.sendMessage(ChatColor.YELLOW + "You call forth the fury of the storm!");
            player.playSound(player.getLocation(), Sound.ENTITY_LIGHTNING_BOLT_THUNDER, 1.0f, 1.0f);

            return true;
        }));
    }

    public boolean executeSkill(Player player, String skillId) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player.getUniqueId());
        if (data == null) {
            return false;
        }

        // Check if skill is unlocked
        if (!data.getUnlockedSkills().contains(skillId)) {
            player.sendMessage(ChatColor.RED + "You haven't unlocked this skill!");
            return false;
        }

        SkillDefinition skill = skills.get(skillId);
        if (skill == null) {
            player.sendMessage(ChatColor.RED + "Unknown skill: " + skillId);
            return false;
        }
        
        // Check cooldown
        if (data.isSkillOnCooldown(skillId)) {
            long remaining = data.getSkillCooldownRemaining(skillId);
            player.sendMessage(ChatColor.RED + "Skill on cooldown! " + (remaining / 1000) + " seconds remaining.");
            return false;
        }
        
        // Check mana cost
        if (!data.canAffordManaCost(skill.manaCost)) {
            player.sendMessage(ChatColor.RED + "Not enough mana! Need " + skill.manaCost + " mana.");
            return false;
        }
        
        // Execute skill
        boolean success = skill.executor.execute(player, data);
        
        if (success) {
            // Consume mana and set cooldown
            data.consumeMana(skill.manaCost);
            data.setSkillCooldown(skillId, skill.cooldown);
            
            player.sendMessage(ChatColor.GREEN + "Used skill: " + ChatColor.YELLOW + skillId);
        }
        
        return success;
    }
    
    // Removed duplicate PlayerInteractEvent handler - SkillListener handles item-based skill triggers
    
    // Skill definition class
    private static class SkillDefinition {
        final String id;
        final long cooldown;
        final int manaCost;
        final SkillExecutorFunction executor;
        
        SkillDefinition(String id, long cooldown, int manaCost, SkillExecutorFunction executor) {
            this.id = id;
            this.cooldown = cooldown;
            this.manaCost = manaCost;
            this.executor = executor;
        }
    }
    
    @FunctionalInterface
    private interface SkillExecutorFunction {
        boolean execute(Player player, PlayerData data);
    }
    
    public Set<String> getAvailableSkills() {
        return skills.keySet();
    }
    
    public boolean hasSkill(String skillId) {
        return skills.containsKey(skillId);
    }
}
