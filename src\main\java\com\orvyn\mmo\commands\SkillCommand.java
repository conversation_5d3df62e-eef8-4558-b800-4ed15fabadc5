package com.orvyn.mmo.commands;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class SkillCommand implements CommandExecutor, TabCompleter {
    
    private final OrvynMMOPlugin plugin;
    
    public SkillCommand(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "Only players can use this command!");
            return true;
        }
        
        Player player = (Player) sender;
        
        if (args.length == 0) {
            showSkillHelp(player);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "use":
                return handleUseSkill(player, args);
            case "list":
                return handleListSkills(player);
            case "info":
                return handleSkillInfo(player, args);
            case "cooldowns":
                return handleCooldowns(player);
            default:
                showSkillHelp(player);
                return true;
        }
    }
    
    private void showSkillHelp(Player player) {
        player.sendMessage("");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");
        player.sendMessage(ChatColor.YELLOW + "    Skill Commands");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");
        player.sendMessage(ChatColor.AQUA + "/skill use <skill_name>" + ChatColor.GRAY + " - Use a skill");
        player.sendMessage(ChatColor.AQUA + "/skill list" + ChatColor.GRAY + " - List unlocked skills");
        player.sendMessage(ChatColor.AQUA + "/skill info <skill_name>" + ChatColor.GRAY + " - Get skill information");
        player.sendMessage(ChatColor.AQUA + "/skill cooldowns" + ChatColor.GRAY + " - View active cooldowns");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");
        player.sendMessage("");
    }
    
    private boolean handleUseSkill(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage(ChatColor.RED + "Usage: /skill use <skill_name>");
            return true;
        }

        String skillName = args[1].toLowerCase().replace(" ", "_");

        // Use SkillManager for consistent skill execution (handles cooldowns, mana, etc.)
        boolean success = plugin.getSkillManager().useSkill(player, skillName);

        if (!success) {
            player.sendMessage(ChatColor.RED + "Failed to use skill: " + skillName);
        }

        return true;
    }
    
    private boolean handleListSkills(Player player) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player.getUniqueId());
        if (data == null) return true;
        
        player.sendMessage("");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");
        player.sendMessage(ChatColor.YELLOW + "    Unlocked Skills");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");
        
        if (data.getUnlockedSkills().isEmpty()) {
            player.sendMessage(ChatColor.GRAY + "No skills unlocked yet!");
            player.sendMessage(ChatColor.YELLOW + "Use " + ChatColor.AQUA + "/player skills" + ChatColor.YELLOW + " to unlock skills");
        } else {
            for (String skillId : data.getUnlockedSkills()) {
                String displayName = formatSkillName(skillId);
                boolean onCooldown = data.isSkillOnCooldown(skillId);
                
                if (onCooldown) {
                    long remaining = data.getSkillCooldownRemaining(skillId);
                    player.sendMessage(ChatColor.RED + "• " + displayName + ChatColor.GRAY + " (Cooldown: " + (remaining / 1000) + "s)");
                } else {
                    player.sendMessage(ChatColor.GREEN + "• " + displayName + ChatColor.GRAY + " (Ready)");
                }
            }
        }
        
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");
        player.sendMessage("");
        return true;
    }
    
    private boolean handleSkillInfo(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage(ChatColor.RED + "Usage: /skill info <skill_name>");
            return true;
        }
        
        String skillName = args[1].toLowerCase().replace(" ", "_");
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player.getUniqueId());
        
        if (data == null || !data.getUnlockedSkills().contains(skillName)) {
            player.sendMessage(ChatColor.RED + "You haven't unlocked this skill: " + formatSkillName(skillName));
            return true;
        }
        
        // Show skill information
        showSkillInfo(player, skillName);
        return true;
    }
    
    private boolean handleCooldowns(Player player) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player.getUniqueId());
        if (data == null) return true;
        
        player.sendMessage("");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");
        player.sendMessage(ChatColor.YELLOW + "    Active Cooldowns");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");
        
        boolean hasCooldowns = false;
        for (String skillId : data.getUnlockedSkills()) {
            if (data.isSkillOnCooldown(skillId)) {
                long remaining = data.getSkillCooldownRemaining(skillId);
                String displayName = formatSkillName(skillId);
                player.sendMessage(ChatColor.RED + "• " + displayName + ChatColor.GRAY + " - " + (remaining / 1000) + " seconds");
                hasCooldowns = true;
            }
        }
        
        if (!hasCooldowns) {
            player.sendMessage(ChatColor.GREEN + "All skills are ready to use!");
        }
        
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");
        player.sendMessage("");
        return true;
    }
    
    private void showSkillInfo(Player player, String skillId) {
        String displayName = formatSkillName(skillId);
        
        player.sendMessage("");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");
        player.sendMessage(ChatColor.YELLOW + "    " + displayName);
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");
        
        // Show skill-specific information
        switch (skillId) {
            case "charge":
                player.sendMessage(ChatColor.AQUA + "Type: " + ChatColor.WHITE + "Warrior Skill");
                player.sendMessage(ChatColor.AQUA + "Cooldown: " + ChatColor.WHITE + "8 seconds");
                player.sendMessage(ChatColor.AQUA + "Mana Cost: " + ChatColor.WHITE + "20");
                player.sendMessage(ChatColor.YELLOW + "Dash forward and deal damage to nearby enemies");
                break;
            case "shield_bash":
                player.sendMessage(ChatColor.AQUA + "Type: " + ChatColor.WHITE + "Warrior Skill");
                player.sendMessage(ChatColor.AQUA + "Cooldown: " + ChatColor.WHITE + "12 seconds");
                player.sendMessage(ChatColor.AQUA + "Mana Cost: " + ChatColor.WHITE + "25");
                player.sendMessage(ChatColor.YELLOW + "Stun nearby enemies with your shield");
                break;
            case "battle_cry":
                player.sendMessage(ChatColor.AQUA + "Type: " + ChatColor.WHITE + "Warrior Skill");
                player.sendMessage(ChatColor.AQUA + "Cooldown: " + ChatColor.WHITE + "20 seconds");
                player.sendMessage(ChatColor.AQUA + "Mana Cost: " + ChatColor.WHITE + "30");
                player.sendMessage(ChatColor.YELLOW + "Boost damage and resistance for 10 seconds");
                break;
            case "whirlwind":
                player.sendMessage(ChatColor.AQUA + "Type: " + ChatColor.WHITE + "Warrior Skill");
                player.sendMessage(ChatColor.AQUA + "Cooldown: " + ChatColor.WHITE + "15 seconds");
                player.sendMessage(ChatColor.AQUA + "Mana Cost: " + ChatColor.WHITE + "40");
                player.sendMessage(ChatColor.YELLOW + "Spin attack hitting all nearby enemies");
                break;
            case "fireball":
                player.sendMessage(ChatColor.AQUA + "Type: " + ChatColor.WHITE + "Mage Skill");
                player.sendMessage(ChatColor.AQUA + "Cooldown: " + ChatColor.WHITE + "3 seconds");
                player.sendMessage(ChatColor.AQUA + "Mana Cost: " + ChatColor.WHITE + "15");
                player.sendMessage(ChatColor.YELLOW + "Launch a burning projectile");
                break;
            case "ice_blast":
                player.sendMessage(ChatColor.AQUA + "Type: " + ChatColor.WHITE + "Mage Skill");
                player.sendMessage(ChatColor.AQUA + "Cooldown: " + ChatColor.WHITE + "5 seconds");
                player.sendMessage(ChatColor.AQUA + "Mana Cost: " + ChatColor.WHITE + "20");
                player.sendMessage(ChatColor.YELLOW + "Freeze enemies in place");
                break;
            case "lightning_bolt":
                player.sendMessage(ChatColor.AQUA + "Type: " + ChatColor.WHITE + "Mage Skill");
                player.sendMessage(ChatColor.AQUA + "Cooldown: " + ChatColor.WHITE + "8 seconds");
                player.sendMessage(ChatColor.AQUA + "Mana Cost: " + ChatColor.WHITE + "35");
                player.sendMessage(ChatColor.YELLOW + "Strike enemies with lightning");
                break;
            case "teleport":
                player.sendMessage(ChatColor.AQUA + "Type: " + ChatColor.WHITE + "Mage Skill");
                player.sendMessage(ChatColor.AQUA + "Cooldown: " + ChatColor.WHITE + "10 seconds");
                player.sendMessage(ChatColor.AQUA + "Mana Cost: " + ChatColor.WHITE + "40");
                player.sendMessage(ChatColor.YELLOW + "Instantly move to target location");
                break;
            case "power_shot":
                player.sendMessage(ChatColor.AQUA + "Type: " + ChatColor.WHITE + "Archer Skill");
                player.sendMessage(ChatColor.AQUA + "Cooldown: " + ChatColor.WHITE + "4 seconds");
                player.sendMessage(ChatColor.AQUA + "Mana Cost: " + ChatColor.WHITE + "15");
                player.sendMessage(ChatColor.YELLOW + "Fire a powerful arrow");
                break;
            case "multi_shot":
                player.sendMessage(ChatColor.AQUA + "Type: " + ChatColor.WHITE + "Archer Skill");
                player.sendMessage(ChatColor.AQUA + "Cooldown: " + ChatColor.WHITE + "6 seconds");
                player.sendMessage(ChatColor.AQUA + "Mana Cost: " + ChatColor.WHITE + "25");
                player.sendMessage(ChatColor.YELLOW + "Fire multiple arrows at once");
                break;
            case "explosive_arrow":
                player.sendMessage(ChatColor.AQUA + "Type: " + ChatColor.WHITE + "Archer Skill");
                player.sendMessage(ChatColor.AQUA + "Cooldown: " + ChatColor.WHITE + "10 seconds");
                player.sendMessage(ChatColor.AQUA + "Mana Cost: " + ChatColor.WHITE + "40");
                player.sendMessage(ChatColor.YELLOW + "Arrow explodes on impact");
                break;
            case "eagle_eye":
                player.sendMessage(ChatColor.AQUA + "Type: " + ChatColor.WHITE + "Archer Skill");
                player.sendMessage(ChatColor.AQUA + "Cooldown: " + ChatColor.WHITE + "15 seconds");
                player.sendMessage(ChatColor.AQUA + "Mana Cost: " + ChatColor.WHITE + "20");
                player.sendMessage(ChatColor.YELLOW + "Increase accuracy and range for 20 seconds");
                break;
            default:
                player.sendMessage(ChatColor.GRAY + "No information available for this skill.");
                break;
        }
        
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");
        player.sendMessage("");
    }
    
    private String formatSkillName(String skillId) {
        return Arrays.stream(skillId.split("_"))
                .map(word -> word.substring(0, 1).toUpperCase() + word.substring(1).toLowerCase())
                .reduce((a, b) -> a + " " + b)
                .orElse(skillId);
    }
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();

        if (args.length == 1) {
            String input = args[0].toLowerCase();
            for (String subCommand : Arrays.asList("use", "list", "info", "cooldowns")) {
                if (subCommand.startsWith(input)) {
                    completions.add(subCommand);
                }
            }
        } else if (args.length == 2 && (args[0].equalsIgnoreCase("use") || args[0].equalsIgnoreCase("info"))) {
            if (sender instanceof Player) {
                Player player = (Player) sender;
                PlayerData data = plugin.getPlayerDataManager().getPlayerData(player.getUniqueId());
                if (data != null) {
                    String input = args[1].toLowerCase();
                    for (String skillId : data.getUnlockedSkills()) {
                        if (skillId.startsWith(input)) {
                            completions.add(skillId);
                        }
                    }

                    // Also add formatted skill names for better UX
                    for (String skillId : data.getUnlockedSkills()) {
                        String formattedName = formatSkillName(skillId).toLowerCase().replace(" ", "_");
                        if (formattedName.startsWith(input) && !completions.contains(skillId)) {
                            completions.add(skillId);
                        }
                    }
                }
            }
        }

        return completions;
    }
}
