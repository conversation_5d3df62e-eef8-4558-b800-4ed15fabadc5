package com.orvyn.mmo.managers;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.gui.MagicWorkbenchGUI;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class MagicWorkbenchManager implements Listener {
    
    private final OrvynMMOPlugin plugin;
    private final Map<Location, String> magicWorkbenches = new HashMap<>();
    private final NamespacedKey magicWorkbenchKey;
    
    public MagicWorkbenchManager(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
        this.magicWorkbenchKey = new NamespacedKey(plugin, "magic_workbench");
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }
    
    public ItemStack createMagicWorkbench() {
        ItemStack workbench = new ItemStack(Material.ENCHANTING_TABLE);
        ItemMeta meta = workbench.getItemMeta();
        
        meta.setDisplayName(ChatColor.DARK_PURPLE + "Magic Bench");
        meta.setLore(Arrays.asList(
            ChatColor.GRAY + "A mystical crafting station",
            ChatColor.GRAY + "Used to create powerful magical items",
            "",
            ChatColor.YELLOW + "Right-click to open crafting interface",
            ChatColor.BLUE + "Place in world to use"
        ));
        
        // Mark as magic workbench
        meta.getPersistentDataContainer().set(magicWorkbenchKey, PersistentDataType.STRING, "magic_workbench");
        
        workbench.setItemMeta(meta);
        return workbench;
    }
    
    @EventHandler
    public void onBlockPlace(BlockPlaceEvent event) {
        ItemStack item = event.getItemInHand();
        if (item.hasItemMeta() && item.getItemMeta().getPersistentDataContainer().has(magicWorkbenchKey, PersistentDataType.STRING)) {
            // Register magic workbench location
            Location loc = event.getBlock().getLocation();
            magicWorkbenches.put(loc, event.getPlayer().getUniqueId().toString());
            
            event.getPlayer().sendMessage(ChatColor.DARK_PURPLE + "✨ Magic Bench placed! Right-click to use.");
            event.getPlayer().playSound(loc, org.bukkit.Sound.BLOCK_ENCHANTMENT_TABLE_USE, 1.0f, 1.0f);
        }
    }
    
    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        Location loc = event.getBlock().getLocation();
        if (magicWorkbenches.containsKey(loc)) {
            // Drop magic workbench item
            ItemStack workbench = createMagicWorkbench();
            loc.getWorld().dropItemNaturally(loc, workbench);
            
            magicWorkbenches.remove(loc);
            event.getPlayer().sendMessage(ChatColor.YELLOW + "Magic Bench retrieved!");
        }
    }
    
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        if (event.getAction() != Action.RIGHT_CLICK_BLOCK) return;
        
        Block block = event.getClickedBlock();
        if (block == null || block.getType() != Material.ENCHANTING_TABLE) return;
        
        Location loc = block.getLocation();
        if (magicWorkbenches.containsKey(loc)) {
            event.setCancelled(true);
            
            Player player = event.getPlayer();
            player.sendMessage(ChatColor.DARK_PURPLE + "✨ Opening Magic Bench...");
            player.playSound(loc, org.bukkit.Sound.BLOCK_ENCHANTMENT_TABLE_USE, 1.0f, 1.2f);
            
            // Open magic workbench GUI
            MagicWorkbenchGUI gui = new MagicWorkbenchGUI(plugin);
            gui.openWorkbench(player);
        }
    }
    
    public boolean isMagicWorkbench(Location location) {
        return magicWorkbenches.containsKey(location);
    }
    
    public Map<Location, String> getMagicWorkbenches() {
        return new HashMap<>(magicWorkbenches);
    }
    
    public void shutdown() {
        magicWorkbenches.clear();
    }
}
