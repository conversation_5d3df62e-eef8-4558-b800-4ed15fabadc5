package com.orvyn.mmo.config;

import com.orvyn.mmo.OrvynMMOPlugin;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;

public class ConfigHub {
    
    private final OrvynMMOPlugin plugin;
    private final Map<String, FileConfiguration> configs = new HashMap<>();
    private final Map<String, File> configFiles = new HashMap<>();
    
    private static final String[] CONFIG_FILES = {
        "config.yml",
        "classes.yml",
        "skills.yml",
        "items.yml",
        "tiers.yml",
        "professions.yml",
        "waypoints.yml",
        "quests.yml",
        "drops.yml",
        "spawners.yml",
        "effects.yml",
        "npcs.yml",
        "ui.yml"
    };
    
    public ConfigHub(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
    }
    
    public void loadAllConfigs() {
        // Create configs directory if it doesn't exist
        File configsDir = new File(plugin.getDataFolder(), "configs");
        if (!configsDir.exists()) {
            configsDir.mkdirs();
        }
        
        // Load each config file
        for (String fileName : CONFIG_FILES) {
            loadConfig(fileName);
        }
        
        // Validate configurations
        validateConfigs();
    }
    
    private void loadConfig(String fileName) {
        try {
            File configFile;
            
            if (fileName.equals("config.yml")) {
                // Main config goes in plugin data folder
                configFile = new File(plugin.getDataFolder(), fileName);
            } else {
                // Other configs go in configs subdirectory
                configFile = new File(plugin.getDataFolder(), "configs/" + fileName);
            }
            
            // Create default file if it doesn't exist
            if (!configFile.exists()) {
                createDefaultConfig(fileName, configFile);
            }
            
            // Load the configuration
            FileConfiguration config = YamlConfiguration.loadConfiguration(configFile);
            
            // Store references
            configs.put(fileName, config);
            configFiles.put(fileName, configFile);
            
            plugin.getLogger().info("Loaded config: " + fileName);
            
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to load config: " + fileName, e);
        }
    }
    
    private void createDefaultConfig(String fileName, File configFile) throws IOException {
        // Try to copy from resources first
        InputStream resourceStream = plugin.getResource("configs/" + fileName);
        if (resourceStream == null && fileName.equals("config.yml")) {
            resourceStream = plugin.getResource(fileName);
        }
        
        if (resourceStream != null) {
            Files.copy(resourceStream, configFile.toPath());
            resourceStream.close();
        } else {
            // Create empty file with basic structure
            configFile.getParentFile().mkdirs();
            configFile.createNewFile();
            
            // Write default content based on file type
            FileConfiguration defaultConfig = YamlConfiguration.loadConfiguration(configFile);
            createDefaultContent(fileName, defaultConfig);
            defaultConfig.save(configFile);
        }
    }
    
    private void createDefaultContent(String fileName, FileConfiguration config) {
        switch (fileName) {
            case "config.yml":
                config.set("database.type", "sqlite");
                config.set("database.file", "orvynmmo.db");
                config.set("performance.tick-budget-ms", 5);
                config.set("performance.async-saves", true);
                break;
            case "classes.yml":
                config.set("warrior.base.HP", 24);
                config.set("warrior.base.MANA", 60);
                config.set("warrior.base.STR", 7);
                config.set("warrior.skills.arc_slash.level", 1);
                config.set("mage.base.HP", 18);
                config.set("mage.base.MANA", 120);
                config.set("mage.base.INT", 8);
                config.set("mage.skills.fire_bolt.level", 1);
                break;
            case "skills.yml":
                config.set("arc_slash.type", "builtin:arc_slash");
                config.set("arc_slash.cooldown", "4s");
                config.set("arc_slash.cost.mana", 10);
                config.set("fire_bolt.type", "projectile");
                config.set("fire_bolt.range", 24);
                config.set("fire_bolt.onHit.0.damage.amount", "6+INT*0.6");
                config.set("fire_bolt.onHit.1.ignite.seconds", 3);
                break;
            case "items.yml":
                config.set("iron_training_blade.material", "IRON_SWORD");
                config.set("iron_training_blade.tier", "common");
                config.set("iron_training_blade.attributes.STR", 2);
                config.set("iron_training_blade.triggers.0.onRightClick.skill", "arc_slash");
                break;
            case "tiers.yml":
                config.set("common.roll.min", 0.9);
                config.set("common.roll.max", 1.1);
                config.set("epic.roll.min", 1.25);
                config.set("epic.roll.max", 1.45);
                break;
            case "professions.yml":
                config.set("mining.expTable.0", 50);
                config.set("mining.expTable.1", 125);
                config.set("mining.expTable.2", 250);
                config.set("mining.bonuses.2.HASTE", 0.05);
                break;
            case "waypoints.yml":
                config.set("spawn.world", "world");
                config.set("spawn.x", 0);
                config.set("spawn.y", 64);
                config.set("spawn.z", 0);
                break;
            case "drops.yml":
                config.set("zombie.table.0.item", "iron_training_blade");
                config.set("zombie.table.0.chance", 0.02);
                break;
        }
    }
    
    private void validateConfigs() {
        // Basic validation - check required sections exist
        for (Map.Entry<String, FileConfiguration> entry : configs.entrySet()) {
            String fileName = entry.getKey();
            FileConfiguration config = entry.getValue();
            
            try {
                validateConfig(fileName, config);
            } catch (Exception e) {
                plugin.getLogger().log(Level.WARNING, "Validation warning for " + fileName + ": " + e.getMessage());
            }
        }
    }
    
    private void validateConfig(String fileName, FileConfiguration config) {
        switch (fileName) {
            case "config.yml":
                if (!config.contains("database.type")) {
                    throw new IllegalStateException("Missing database.type");
                }
                break;
            case "classes.yml":
                if (config.getKeys(false).isEmpty()) {
                    throw new IllegalStateException("No classes defined");
                }
                break;
        }
    }
    
    public FileConfiguration getConfig(String fileName) {
        return configs.get(fileName);
    }
    
    public void saveConfig(String fileName) {
        try {
            FileConfiguration config = configs.get(fileName);
            File file = configFiles.get(fileName);
            if (config != null && file != null) {
                config.save(file);
            }
        } catch (IOException e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to save config: " + fileName, e);
        }
    }
    
    public void reloadConfig(String fileName) {
        loadConfig(fileName);
    }
}
