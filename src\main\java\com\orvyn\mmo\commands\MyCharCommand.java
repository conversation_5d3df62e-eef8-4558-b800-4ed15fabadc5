package com.orvyn.mmo.commands;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.gui.CharacterHubGUI;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class MyCharCommand implements CommandExecutor, TabCompleter {
    
    private final OrvynMMOPlugin plugin;
    
    public MyCharCommand(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "Only players can use this command!");
            return true;
        }
        
        Player player = (Player) sender;
        
        if (args.length == 0) {
            // Open main character hub GUI
            openCharacterHub(player);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "stats":
            case "attributes":
                openCharacterHub(player, "stats");
                break;
                
            case "skills":
            case "skill":
                openCharacterHub(player, "skills");
                break;
                
            case "skillbar":
            case "hotkeys":
                openCharacterHub(player, "skillbar");
                break;
                
            case "professions":
            case "prof":
                openCharacterHub(player, "professions");
                break;
                
            case "quests":
            case "quest":
                openCharacterHub(player, "quests");
                break;
                
            case "equipment":
            case "gear":
                openCharacterHub(player, "equipment");
                break;
                
            case "party":
                openCharacterHub(player, "party");
                break;

            case "effects":
                com.orvyn.mmo.gui.EffectsGUI effectsGUI = new com.orvyn.mmo.gui.EffectsGUI(plugin);
                effectsGUI.openEffectsGUI(player);
                break;

            case "help":
                showHelp(player);
                break;
                
            default:
                openCharacterHub(player);
                break;
        }
        
        return true;
    }
    
    private void openCharacterHub(Player player) {
        openCharacterHub(player, null);
    }
    
    private void openCharacterHub(Player player, String section) {
        CharacterHubGUI gui = new CharacterHubGUI(plugin, player);
        if (section != null) {
            gui.openSection(section);
        } else {
            gui.open();
        }
    }
    
    private void showHelp(Player player) {
        player.sendMessage("");
        player.sendMessage(ChatColor.GOLD + "╔═══════════════════════════════════════════════════════════╗");
        player.sendMessage(ChatColor.GOLD + "║" + ChatColor.YELLOW + "                    🎮 OrvynMMO Commands 🎮                   " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
        player.sendMessage(ChatColor.GOLD + "║" + ChatColor.AQUA + "                   Character Management                    " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
        player.sendMessage(ChatColor.GOLD + "║ " + ChatColor.GREEN + "/mychar" + ChatColor.GRAY + "                - Open main character hub        " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "║ " + ChatColor.GREEN + "/mychar stats" + ChatColor.GRAY + "           - Manage your attributes & levels     " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "║ " + ChatColor.GREEN + "/mychar skills" + ChatColor.GRAY + "          - View & unlock new skills            " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "║ " + ChatColor.GREEN + "/mychar skillbar" + ChatColor.GRAY + "        - Set up skill hotkeys (Sneak + 1-5)     " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "║ " + ChatColor.GREEN + "/mychar professions" + ChatColor.GRAY + "     - View mining, farming, combat levels      " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "║ " + ChatColor.GREEN + "/mychar quests" + ChatColor.GRAY + "          - Accept & track quests               " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "║ " + ChatColor.GREEN + "/mychar equipment" + ChatColor.GRAY + "       - Enhance gear & socket gems             " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "║ " + ChatColor.GREEN + "/mychar party" + ChatColor.GRAY + "           - Create & manage parties             " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
        player.sendMessage(ChatColor.GOLD + "║" + ChatColor.YELLOW + "                      Skill Usage                         " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
        player.sendMessage(ChatColor.GOLD + "║ " + ChatColor.LIGHT_PURPLE + "Hold Sneak + Press 1-5" + ChatColor.GRAY + "   - Use skills from your skill bar      " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "║ " + ChatColor.GREEN + "/skill use <name>" + ChatColor.GRAY + "        - Use a specific skill by name          " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "║ " + ChatColor.GREEN + "/skill list" + ChatColor.GRAY + "             - Show all your unlocked skills       " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "║ " + ChatColor.GREEN + "/skill cooldowns" + ChatColor.GRAY + "        - Check skill cooldown timers            " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
        player.sendMessage(ChatColor.GOLD + "║" + ChatColor.YELLOW + "                    Getting Started                        " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
        player.sendMessage(ChatColor.GOLD + "║ " + ChatColor.WHITE + "1. Choose your class:" + ChatColor.AQUA + " /ommo class" + ChatColor.GRAY + "                        " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "║ " + ChatColor.WHITE + "2. Allocate stats:" + ChatColor.AQUA + " /mychar stats" + ChatColor.GRAY + "                       " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "║ " + ChatColor.WHITE + "3. Set up skills:" + ChatColor.AQUA + " /mychar skillbar" + ChatColor.GRAY + "                      " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "║ " + ChatColor.WHITE + "4. Accept quests:" + ChatColor.AQUA + " /mychar quests" + ChatColor.GRAY + "                       " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "║ " + ChatColor.WHITE + "5. Enhance gear:" + ChatColor.AQUA + " /mychar equipment" + ChatColor.GRAY + "                     " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
        player.sendMessage(ChatColor.GOLD + "║" + ChatColor.YELLOW + "                       Aliases                             " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
        player.sendMessage(ChatColor.GOLD + "║ " + ChatColor.GRAY + "You can also use: " + ChatColor.WHITE + "/character, /char, /me" + ChatColor.GRAY + "                " + ChatColor.GOLD + "║");
        player.sendMessage(ChatColor.GOLD + "╚═══════════════════════════════════════════════════════════╝");
        player.sendMessage("");
    }
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        if (!(sender instanceof Player)) return new ArrayList<>();

        List<String> completions = new ArrayList<>();

        if (args.length == 1) {
            String input = args[0].toLowerCase();
            for (String subCommand : Arrays.asList("stats", "skills", "skillbar", "professions", "quests", "equipment", "party", "effects", "help")) {
                if (subCommand.startsWith(input)) {
                    completions.add(subCommand);
                }
            }
        }

        return completions;
    }
}
