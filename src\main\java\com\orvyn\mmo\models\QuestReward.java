package com.orvyn.mmo.models;

public class QuestReward {

    public enum RewardType {
        EXPERIENCE, PROFESSION_XP, ITEM, CURRENCY
    }

    private final String id;
    private final RewardType rewardType;
    private final String type; // Keep for backward compatibility
    private final String target;
    private final int amount;
    private final String description;

    // Backward compatibility constructor
    public QuestReward(String id, String type, String target, int amount) {
        this.id = id;
        this.type = type;
        this.target = target;
        this.amount = amount;
        this.rewardType = parseRewardType(type);
        this.description = generateDescription();
    }

    // New constructor with RewardType
    public QuestReward(RewardType rewardType, String target, int amount, String description) {
        this.id = "reward_" + System.currentTimeMillis();
        this.rewardType = rewardType;
        this.type = rewardType.name().toLowerCase();
        this.target = target;
        this.amount = amount;
        this.description = description;
    }

    private RewardType parseRewardType(String type) {
        try {
            return RewardType.valueOf(type.toUpperCase());
        } catch (IllegalArgumentException e) {
            return RewardType.EXPERIENCE; // Default fallback
        }
    }

    private String generateDescription() {
        switch (rewardType) {
            case EXPERIENCE:
                return amount + " Experience";
            case PROFESSION_XP:
                return amount + " " + target + " XP";
            case ITEM:
                return amount + "x " + formatTarget(target);
            case CURRENCY:
                return amount + " " + target;
            default:
                return "Reward";
        }
    }

    private String formatTarget(String target) {
        if (target == null) return "items";
        return target.toLowerCase().replace("_", " ");
    }

    public String getId() { return id; }
    public String getType() { return type; }
    public RewardType getRewardType() { return rewardType; }
    public String getTarget() { return target; }
    public int getAmount() { return amount; }
    public String getDescription() { return description; }
}
