package com.orvyn.mmo.listeners;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Arrow;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Fireball;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.entity.Projectile;
import org.bukkit.entity.Snowball;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityShootBowEvent;
import org.bukkit.event.entity.ProjectileHitEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class ClassWeaponListener implements Listener {
    
    private final OrvynMMOPlugin plugin;
    private final Map<UUID, Long> cooldowns = new HashMap<>();
    private final Map<UUID, Integer> archerShotCount = new HashMap<>();
    
    public ClassWeaponListener(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        if (event.getAction() != Action.RIGHT_CLICK_AIR && event.getAction() != Action.RIGHT_CLICK_BLOCK) {
            return;
        }
        
        Player player = event.getPlayer();
        ItemStack item = player.getInventory().getItemInMainHand();
        
        if (item == null || item.getType() == Material.AIR) {
            return;
        }
        
        ItemMeta meta = item.getItemMeta();
        if (meta == null) {
            return;
        }
        
        NamespacedKey key = new NamespacedKey(plugin, "class_weapon");
        if (!meta.getPersistentDataContainer().has(key, PersistentDataType.STRING)) {
            return;
        }
        
        String weaponType = meta.getPersistentDataContainer().get(key, PersistentDataType.STRING);
        if (weaponType == null) {
            return;
        }
        
        event.setCancelled(true);
        
        switch (weaponType) {
            case "warrior_blade":
                handleWarriorBlade(player);
                break;
            case "mage_staff":
                handleMageStaff(player);
                break;
            // Hunter bow is handled in EntityShootBowEvent
        }
    }
    
    private void handleWarriorBlade(Player player) {
        // Check cooldown
        long currentTime = System.currentTimeMillis();
        long lastUse = cooldowns.getOrDefault(player.getUniqueId(), 0L);
        if (currentTime - lastUse < 5000) { // 5 second cooldown
            long remaining = (5000 - (currentTime - lastUse)) / 1000;
            player.sendMessage(ChatColor.RED + "Sword beam on cooldown for " + remaining + " seconds!");
            return;
        }
        
        // Check mana
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        if (data.getCurrentMana() < 10) {
            player.sendMessage(ChatColor.RED + "Not enough mana! Need 10 mana.");
            return;
        }
        
        // Consume mana
        data.setCurrentMana(data.getCurrentMana() - 10);
        cooldowns.put(player.getUniqueId(), currentTime);
        
        // Fire sword beam (using snowball as projectile)
        Snowball beam = player.launchProjectile(Snowball.class);
        beam.setVelocity(player.getLocation().getDirection().multiply(2.0));
        
        // Mark as sword beam
        NamespacedKey key = new NamespacedKey(plugin, "sword_beam");
        beam.getPersistentDataContainer().set(key, PersistentDataType.BOOLEAN, true);
        
        // Visual and audio effects
        player.getWorld().spawnParticle(Particle.CRIT, player.getLocation().add(0, 1, 0), 10, 0.5, 0.5, 0.5, 0.1);
        player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_ATTACK_CRIT, 1.0f, 1.5f);
        
        player.sendMessage(ChatColor.YELLOW + "Sword beam fired!");
    }
    
    private void handleMageStaff(Player player) {
        // Check cooldown
        long currentTime = System.currentTimeMillis();
        long lastUse = cooldowns.getOrDefault(player.getUniqueId(), 0L);
        if (currentTime - lastUse < 6000) { // 6 second cooldown
            long remaining = (6000 - (currentTime - lastUse)) / 1000;
            player.sendMessage(ChatColor.RED + "Fireball on cooldown for " + remaining + " seconds!");
            return;
        }
        
        // Check mana
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        if (data.getCurrentMana() < 15) {
            player.sendMessage(ChatColor.RED + "Not enough mana! Need 15 mana.");
            return;
        }
        
        // Consume mana
        data.setCurrentMana(data.getCurrentMana() - 15);
        cooldowns.put(player.getUniqueId(), currentTime);
        
        // Fire fireball
        Fireball fireball = player.launchProjectile(Fireball.class);
        fireball.setVelocity(player.getLocation().getDirection().multiply(1.5));
        fireball.setYield(0); // No terrain damage
        
        // Mark as mage fireball
        NamespacedKey key = new NamespacedKey(plugin, "mage_fireball");
        fireball.getPersistentDataContainer().set(key, PersistentDataType.BOOLEAN, true);
        
        // Visual and audio effects
        player.getWorld().spawnParticle(Particle.FLAME, player.getLocation().add(0, 1, 0), 15, 0.5, 0.5, 0.5, 0.1);
        player.playSound(player.getLocation(), Sound.ENTITY_BLAZE_SHOOT, 1.0f, 1.0f);
        
        player.sendMessage(ChatColor.YELLOW + "Fireball cast!");
    }
    
    @EventHandler
    public void onEntityShootBow(EntityShootBowEvent event) {
        if (!(event.getEntity() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getEntity();
        ItemStack bow = event.getBow();
        
        if (bow == null) {
            return;
        }
        
        ItemMeta meta = bow.getItemMeta();
        if (meta == null) {
            return;
        }
        
        NamespacedKey key = new NamespacedKey(plugin, "class_weapon");
        if (!meta.getPersistentDataContainer().has(key, PersistentDataType.STRING)) {
            return;
        }
        
        String weaponType = meta.getPersistentDataContainer().get(key, PersistentDataType.STRING);
        if (!"hunter_bow".equals(weaponType)) {
            return;
        }
        
        // Check mana
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        if (data.getCurrentMana() < 5) {
            player.sendMessage(ChatColor.RED + "Not enough mana! Arrows cost 5 mana each.");
            event.setCancelled(true);
            return;
        }
        
        // Consume mana
        data.setCurrentMana(data.getCurrentMana() - 5);
        
        // Track shot count for power shots
        int shotCount = archerShotCount.getOrDefault(player.getUniqueId(), 0) + 1;
        archerShotCount.put(player.getUniqueId(), shotCount);
        
        Entity projectile = event.getProjectile();
        
        // Every 5th shot is a power shot
        if (shotCount % 5 == 0) {
            // Mark as power shot
            NamespacedKey powerKey = new NamespacedKey(plugin, "power_shot");
            projectile.getPersistentDataContainer().set(powerKey, PersistentDataType.BOOLEAN, true);
            
            // Visual effects for power shot
            player.getWorld().spawnParticle(Particle.CRIT, player.getLocation().add(0, 1, 0), 20, 0.5, 0.5, 0.5, 0.2);
            player.playSound(player.getLocation(), Sound.ENTITY_ARROW_SHOOT, 1.0f, 0.5f);
            player.sendMessage(ChatColor.GOLD + "Power Shot!");
            
            // Add homing effect
            if (projectile instanceof Arrow) {
                addHomingEffect((Arrow) projectile, player);
            }
        } else {
            // Regular shot
            player.playSound(player.getLocation(), Sound.ENTITY_ARROW_SHOOT, 1.0f, 1.0f);
        }
    }
    
    private void addHomingEffect(Arrow arrow, Player shooter) {
        new BukkitRunnable() {
            int ticks = 0;
            
            @Override
            public void run() {
                if (arrow.isDead() || arrow.isOnGround() || ticks > 100) { // 5 seconds max
                    cancel();
                    return;
                }
                
                // Find nearest enemy within 10 blocks
                LivingEntity target = null;
                double closestDistance = 10.0;
                
                for (Entity entity : arrow.getNearbyEntities(10, 10, 10)) {
                    if (entity instanceof LivingEntity && entity != shooter && !(entity instanceof Player)) {
                        double distance = arrow.getLocation().distance(entity.getLocation());
                        if (distance < closestDistance) {
                            target = (LivingEntity) entity;
                            closestDistance = distance;
                        }
                    }
                }
                
                // Apply homing
                if (target != null) {
                    Vector direction = target.getLocation().add(0, 1, 0).subtract(arrow.getLocation()).toVector().normalize();
                    Vector currentVelocity = arrow.getVelocity();
                    Vector newVelocity = currentVelocity.add(direction.multiply(0.1)).normalize().multiply(currentVelocity.length());
                    arrow.setVelocity(newVelocity);
                    
                    // Particle trail
                    arrow.getWorld().spawnParticle(Particle.ENCHANT, arrow.getLocation(), 2);
                }
                
                ticks++;
            }
        }.runTaskTimer(plugin, 0L, 1L);
    }
    
    @EventHandler
    public void onProjectileHit(ProjectileHitEvent event) {
        Projectile projectile = event.getEntity();
        
        // Handle sword beam hits
        NamespacedKey beamKey = new NamespacedKey(plugin, "sword_beam");
        if (projectile.getPersistentDataContainer().has(beamKey, PersistentDataType.BOOLEAN)) {
            handleSwordBeamHit(event);
            return;
        }
        
        // Handle mage fireball hits
        NamespacedKey fireballKey = new NamespacedKey(plugin, "mage_fireball");
        if (projectile.getPersistentDataContainer().has(fireballKey, PersistentDataType.BOOLEAN)) {
            handleMageFireballHit(event);
            return;
        }
    }
    
    private void handleSwordBeamHit(ProjectileHitEvent event) {
        Location hitLocation = event.getEntity().getLocation();
        
        // Damage nearby entities
        for (Entity entity : event.getEntity().getNearbyEntities(2, 2, 2)) {
            if (entity instanceof LivingEntity && !(entity instanceof Player)) {
                LivingEntity target = (LivingEntity) entity;
                target.damage(8.0);
                
                // Visual effect
                target.getWorld().spawnParticle(Particle.CRIT, target.getLocation().add(0, 1, 0), 5);
            }
        }
        
        // Explosion effect
        hitLocation.getWorld().spawnParticle(Particle.EXPLOSION, hitLocation, 1);
        hitLocation.getWorld().playSound(hitLocation, Sound.ENTITY_GENERIC_EXPLODE, 1.0f, 1.5f);
    }
    
    private void handleMageFireballHit(ProjectileHitEvent event) {
        Location hitLocation = event.getEntity().getLocation();
        
        // AoE damage
        for (Entity entity : event.getEntity().getNearbyEntities(3, 3, 3)) {
            if (entity instanceof LivingEntity && !(entity instanceof Player)) {
                LivingEntity target = (LivingEntity) entity;
                double distance = target.getLocation().distance(hitLocation);
                double damage = 10.0 - (distance * 2); // Damage falls off with distance
                if (damage > 0) {
                    target.damage(Math.max(3.0, damage)); // Minimum 3 damage
                    target.setFireTicks(60); // 3 seconds of fire
                }
            }
        }
        
        // Fire explosion effect
        hitLocation.getWorld().spawnParticle(Particle.EXPLOSION, hitLocation, 2);
        hitLocation.getWorld().spawnParticle(Particle.FLAME, hitLocation, 20, 2, 2, 2, 0.1);
        hitLocation.getWorld().playSound(hitLocation, Sound.ENTITY_GENERIC_EXPLODE, 1.0f, 0.8f);
    }
    
    @EventHandler
    public void onEntityDamageByEntity(EntityDamageByEntityEvent event) {
        if (!(event.getDamager() instanceof Arrow)) {
            return;
        }
        
        Arrow arrow = (Arrow) event.getDamager();
        NamespacedKey powerKey = new NamespacedKey(plugin, "power_shot");
        
        if (arrow.getPersistentDataContainer().has(powerKey, PersistentDataType.BOOLEAN)) {
            // Double damage for power shots
            event.setDamage(event.getDamage() * 2);
            
            // Visual effect
            if (event.getEntity() instanceof LivingEntity) {
                LivingEntity target = (LivingEntity) event.getEntity();
                target.getWorld().spawnParticle(Particle.CRIT, target.getLocation().add(0, 1, 0), 10);
                target.getWorld().playSound(target.getLocation(), Sound.ENTITY_PLAYER_ATTACK_CRIT, 1.0f, 0.8f);
            }
        }
    }
}
