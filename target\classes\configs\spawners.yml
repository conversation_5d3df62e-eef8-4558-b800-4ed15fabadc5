# ========================================
# OrvynMMO Spawners Configuration
# ========================================
#
# This file defines region-based custom mob spawning areas.
# Spawners create challenging encounters with custom mobs and rewards.
#
# Configuration Guide:
# - region: Defines the spawning area using two corner positions
#   - world: World name where spawner is located
#   - pos1/pos2: Corner coordinates (x, y, z) defining cuboid region
# - mobs: List of mob types that can spawn in this region
#   - type: Bukkit EntityType (ZOMBIE, SKELETON, SPIDER, etc.)
#   - name: Custom display name with color codes (§c = red, §a = green)
#   - level: Mob level (affects health and damage)
#   - spawn_chance: Probability this mob spawns (0.0 to 1.0)
#   - max_count: Maximum of this mob type in the region
#   - custom_drops: Items dropped when mob dies
#     - item: Bukkit Material name (IRON_SWORD, EMERALD, etc.)
#     - chance: Drop probability (0.0 to 1.0)
#     - amount: Number of items to drop
#     - name: Custom item name with color codes (optional)
# - spawn_interval: Ticks between spawn checks (200 = 10 seconds)
# - enabled: Whether this spawner is active
#
# Level Effects:
# - Health: Base health + (level × level_health_multiplier)
# - Damage: Base damage + (level × level_damage_multiplier)
#
# Commands: /ommo spawner list
# ========================================

dungeon_entrance_spawner:
  region:
    world: "world"
    pos1: {x: 100, y: 64, z: 200}
    pos2: {x: 150, y: 80, z: 250}
  mobs:
    - type: ZOMBIE
      name: "§cUndead Warrior"
      level: 5
      spawn_chance: 0.6
      max_count: 10
      custom_drops:
        - item: IRON_SWORD
          chance: 0.1
          name: "§7Rusty Sword"
        - item: ROTTEN_FLESH
          chance: 0.8
          amount: 2
    - type: SKELETON
      name: "§7Skeleton Archer"
      level: 4
      spawn_chance: 0.4
      max_count: 5
      custom_drops:
        - item: BOW
          chance: 0.15
          name: "§7Cracked Bow"
        - item: ARROW
          chance: 0.6
          amount: 5
  spawn_interval: 200  # Check every 10 seconds (200 ticks)
  enabled: true

goblin_camp:
  region:
    world: "world"
    pos1: {x: -100, y: 60, z: -100}
    pos2: {x: -50, y: 75, z: -50}
  mobs:
    - type: ZOMBIE
      name: "§2Goblin Warrior"
      level: 3
      spawn_chance: 0.7
      max_count: 8
      custom_drops:
        - item: EMERALD
          chance: 0.05
          amount: 1
        - item: LEATHER
          chance: 0.3
          amount: 2
    - type: SPIDER
      name: "§8Giant Spider"
      level: 4
      spawn_chance: 0.3
      max_count: 3
      custom_drops:
        - item: STRING
          chance: 0.9
          amount: 3
        - item: SPIDER_EYE
          chance: 0.4
          amount: 1
  spawn_interval: 300  # Check every 15 seconds
  enabled: true

ancient_ruins:
  region:
    world: "world"
    pos1: {x: 300, y: 50, z: 300}
    pos2: {x: 350, y: 70, z: 350}
  mobs:
    - type: SKELETON
      name: "§6Ancient Guardian"
      level: 8
      spawn_chance: 0.4
      max_count: 3
      custom_drops:
        - item: GOLD_INGOT
          chance: 0.2
          amount: 1
        - item: BONE
          chance: 0.7
          amount: 3
    - type: ZOMBIE
      name: "§5Cursed Warrior"
      level: 7
      spawn_chance: 0.6
      max_count: 5
      custom_drops:
        - item: DIAMOND
          chance: 0.05
          amount: 1
        - item: IRON_INGOT
          chance: 0.3
          amount: 2
  spawn_interval: 400  # Check every 20 seconds
  enabled: true

# Global spawner settings
global_settings:
  max_spawners_active: 10
  default_spawn_interval: 200
  default_max_count: 5
  level_health_multiplier: 20  # Each level adds 20 HP
  level_damage_multiplier: 1.5 # Each level adds 1.5 damage
  despawn_distance: 64         # Despawn mobs if no players within 64 blocks
