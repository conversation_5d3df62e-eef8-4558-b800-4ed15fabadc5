package com.orvyn.mmo.gui;

import com.orvyn.mmo.OrvynMMOPlugin;

import com.orvyn.mmo.models.EnhancedItem;
import com.orvyn.mmo.models.Gem;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import java.util.ArrayList;
import java.util.List;

public class EquipmentGUI implements Listener {

    private final OrvynMMOPlugin plugin;
    private final Player player;
    private static final Map<UUID, EquipmentGUI> activeGUIs = new HashMap<>();

    public EquipmentGUI(OrvynMMOPlugin plugin, Player player) {
        this.plugin = plugin;
        this.player = player;

        // Unregister any existing GUI for this player
        EquipmentGUI existing = activeGUIs.get(player.getUniqueId());
        if (existing != null) {
            HandlerList.unregisterAll(existing);
        }

        // Register this as a listener and track it
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
        activeGUIs.put(player.getUniqueId(), this);
    }
    
    public void openEquipmentHub() {
        Inventory gui = Bukkit.createInventory(null, 54, ChatColor.DARK_PURPLE + "Equipment Hub - " + player.getName());
        
        // Equipment Viewer
        ItemStack viewer = createItem(Material.DIAMOND_CHESTPLATE,
            ChatColor.AQUA + "Equipment Viewer",
            ChatColor.GRAY + "View your currently equipped items",
            "",
            ChatColor.YELLOW + "Helmet: " + getEquippedItemName(player.getInventory().getHelmet()),
            ChatColor.YELLOW + "Chestplate: " + getEquippedItemName(player.getInventory().getChestplate()),
            ChatColor.YELLOW + "Leggings: " + getEquippedItemName(player.getInventory().getLeggings()),
            ChatColor.YELLOW + "Boots: " + getEquippedItemName(player.getInventory().getBoots()),
            ChatColor.YELLOW + "Main Hand: " + getEquippedItemName(player.getInventory().getItemInMainHand()),
            "",
            ChatColor.GREEN + "Click to view equipment details"
        );
        gui.setItem(10, viewer);
        
        // Item Enhancement
        ItemStack enhancement = createItem(Material.ANVIL,
            ChatColor.GOLD + "Item Enhancement",
            ChatColor.GRAY + "Upgrade your equipment",
            "",
            ChatColor.YELLOW + "Enhance items to increase their power",
            ChatColor.YELLOW + "Higher enhancement levels provide better stats",
            "",
            ChatColor.GREEN + "Click to enhance equipment"
        );
        gui.setItem(12, enhancement);
        
        // Gem Socketing
        ItemStack socketing = createItem(Material.EMERALD,
            ChatColor.GREEN + "Gem Socketing",
            ChatColor.GRAY + "Socket gems into your equipment",
            "",
            ChatColor.YELLOW + "Add gems to equipment for bonus stats",
            ChatColor.YELLOW + "Different gems provide different bonuses",
            "",
            ChatColor.GREEN + "Click to socket gems"
        );
        gui.setItem(14, socketing);
        
        // Gem Collection
        ItemStack gemCollection = createItem(Material.DIAMOND,
            ChatColor.LIGHT_PURPLE + "Gem Collection",
            ChatColor.GRAY + "View and manage your gems",
            "",
            ChatColor.YELLOW + "Browse your collected gems",
            ChatColor.YELLOW + "See gem stats and effects",
            "",
            ChatColor.GREEN + "Click to view gem collection"
        );
        gui.setItem(16, gemCollection);
        
        // Enhanced Items Showcase
        ItemStack showcase = createItem(Material.ENCHANTED_BOOK,
            ChatColor.DARK_PURPLE + "Enhanced Items",
            ChatColor.GRAY + "Browse available enhanced items",
            "",
            ChatColor.YELLOW + "View example enhanced equipment",
            ChatColor.YELLOW + "See what's possible with enhancement",
            "",
            ChatColor.GREEN + "Click to browse enhanced items"
        );
        gui.setItem(28, showcase);
        
        // Crafting Station (Future)
        ItemStack crafting = createItem(Material.CRAFTING_TABLE,
            ChatColor.YELLOW + "Enhancement Crafting",
            ChatColor.GRAY + "Craft enhancement materials",
            "",
            ChatColor.RED + "Coming Soon!",
            ChatColor.GRAY + "Craft gems and enhancement stones",
            "",
            ChatColor.GRAY + "Feature in development"
        );
        gui.setItem(30, crafting);
        
        // Statistics
        ItemStack stats = createItem(Material.BOOK,
            ChatColor.AQUA + "Equipment Statistics",
            ChatColor.GRAY + "View your equipment stats",
            "",
            ChatColor.YELLOW + "Total Attack Damage: " + calculateTotalAttackDamage(),
            ChatColor.YELLOW + "Total Armor: " + calculateTotalArmor(),
            ChatColor.YELLOW + "Enhanced Items: " + countEnhancedItems(),
            ChatColor.YELLOW + "Socketed Gems: " + countSocketedGems(),
            "",
            ChatColor.GREEN + "Click for detailed statistics"
        );
        gui.setItem(32, stats);
        
        // Back button
        ItemStack back = createItem(Material.ARROW,
            ChatColor.YELLOW + "Back",
            ChatColor.GRAY + "Return to character hub"
        );
        gui.setItem(49, back);
        
        player.openInventory(gui);
    }
    
    public void openEquipmentViewer() {
        Inventory gui = Bukkit.createInventory(null, 54, ChatColor.AQUA + "Equipment Viewer");
        
        // Player's current equipment in armor slots
        ItemStack helmet = player.getInventory().getHelmet();
        ItemStack chestplate = player.getInventory().getChestplate();
        ItemStack leggings = player.getInventory().getLeggings();
        ItemStack boots = player.getInventory().getBoots();
        ItemStack mainHand = player.getInventory().getItemInMainHand();
        ItemStack offHand = player.getInventory().getItemInOffHand();
        
        // Display equipment with enhanced info if available
        if (helmet != null && helmet.getType() != Material.AIR) {
            gui.setItem(13, createEquipmentDisplayItem(helmet, "Helmet"));
        } else {
            gui.setItem(13, createEmptySlotItem("Helmet"));
        }
        
        if (chestplate != null && chestplate.getType() != Material.AIR) {
            gui.setItem(22, createEquipmentDisplayItem(chestplate, "Chestplate"));
        } else {
            gui.setItem(22, createEmptySlotItem("Chestplate"));
        }
        
        if (leggings != null && leggings.getType() != Material.AIR) {
            gui.setItem(31, createEquipmentDisplayItem(leggings, "Leggings"));
        } else {
            gui.setItem(31, createEmptySlotItem("Leggings"));
        }
        
        if (boots != null && boots.getType() != Material.AIR) {
            gui.setItem(40, createEquipmentDisplayItem(boots, "Boots"));
        } else {
            gui.setItem(40, createEmptySlotItem("Boots"));
        }
        
        if (mainHand != null && mainHand.getType() != Material.AIR) {
            gui.setItem(20, createEquipmentDisplayItem(mainHand, "Main Hand"));
        } else {
            gui.setItem(20, createEmptySlotItem("Main Hand"));
        }
        
        if (offHand != null && offHand.getType() != Material.AIR) {
            gui.setItem(24, createEquipmentDisplayItem(offHand, "Off Hand"));
        } else {
            gui.setItem(24, createEmptySlotItem("Off Hand"));
        }
        
        // Back button
        ItemStack back = createItem(Material.ARROW,
            ChatColor.YELLOW + "Back",
            ChatColor.GRAY + "Return to equipment hub"
        );
        gui.setItem(49, back);
        
        player.openInventory(gui);
    }
    
    public void openEnhancementStation() {
        Inventory gui = Bukkit.createInventory(null, 54, ChatColor.GOLD + "Enhancement Station");
        
        // Instructions
        ItemStack instructions = createItem(Material.BOOK,
            ChatColor.YELLOW + "Enhancement Guide",
            ChatColor.GRAY + "How to enhance your equipment:",
            "",
            ChatColor.WHITE + "1. Place item in the enhancement slot",
            ChatColor.WHITE + "2. Add enhancement materials",
            ChatColor.WHITE + "3. Click enhance to upgrade",
            "",
            ChatColor.YELLOW + "Higher levels require more materials",
            ChatColor.YELLOW + "Enhancement increases all base stats"
        );
        gui.setItem(4, instructions);
        
        // Enhancement slot (where player places item)
        ItemStack enhanceSlot = createItem(Material.GRAY_STAINED_GLASS_PANE,
            ChatColor.AQUA + "Enhancement Slot",
            ChatColor.GRAY + "Place item here to enhance",
            "",
            ChatColor.YELLOW + "Drag equipment here"
        );
        gui.setItem(22, enhanceSlot);
        
        // Enhancement button
        ItemStack enhanceButton = createItem(Material.EMERALD,
            ChatColor.GREEN + "Enhance Item",
            ChatColor.GRAY + "Click to enhance the placed item",
            "",
            ChatColor.YELLOW + "Cost: Varies by level",
            ChatColor.RED + "Place an item first!"
        );
        gui.setItem(31, enhanceButton);
        
        // Show some example enhanced items
        int slot = 45;
        for (EnhancedItem item : plugin.getEquipmentManager().getAllEnhancedItems()) {
            if (slot >= 53) break;
            gui.setItem(slot, item.createItemStack());
            slot++;
        }
        
        // Back button
        ItemStack back = createItem(Material.ARROW,
            ChatColor.YELLOW + "Back",
            ChatColor.GRAY + "Return to equipment hub"
        );
        gui.setItem(49, back);
        
        player.openInventory(gui);
    }
    
    public void openGemCollection() {
        Inventory gui = Bukkit.createInventory(null, 54, ChatColor.LIGHT_PURPLE + "Gem Collection");
        
        // Display available gems
        int slot = 9;
        for (Gem gem : plugin.getEquipmentManager().getAllGems()) {
            if (slot >= 45) break;
            gui.setItem(slot, gem.createItemStack());
            slot++;
        }
        
        // Give player some random gems for testing
        ItemStack giveGems = createItem(Material.CHEST,
            ChatColor.GREEN + "Get Random Gems",
            ChatColor.GRAY + "Get some gems for testing",
            "",
            ChatColor.YELLOW + "Click to receive random gems",
            ChatColor.GRAY + "(For testing purposes)"
        );
        gui.setItem(4, giveGems);
        
        // Back button
        ItemStack back = createItem(Material.ARROW,
            ChatColor.YELLOW + "Back",
            ChatColor.GRAY + "Return to equipment hub"
        );
        gui.setItem(49, back);
        
        player.openInventory(gui);
    }

    public void openGemSocketingStation() {
        Inventory gui = Bukkit.createInventory(null, 54, ChatColor.AQUA + "Gem Socketing Station");

        // Instructions
        ItemStack instructions = createItem(Material.BOOK,
            ChatColor.YELLOW + "How to Socket Gems",
            ChatColor.GRAY + "1. Place equipment in the center slot",
            ChatColor.GRAY + "2. Place gems in the gem slots",
            ChatColor.GRAY + "3. Click 'Socket Gem' to apply",
            "",
            ChatColor.RED + "Warning: Socketing replaces existing gems!"
        );
        gui.setItem(4, instructions);

        // Equipment slot (center)
        ItemStack equipmentSlot = createItem(Material.GRAY_STAINED_GLASS_PANE,
            ChatColor.WHITE + "Equipment Slot",
            ChatColor.GRAY + "Place equipment here to socket gems"
        );
        gui.setItem(22, equipmentSlot);

        // Gem slots (around equipment)
        ItemStack gemSlot1 = createItem(Material.LIGHT_BLUE_STAINED_GLASS_PANE,
            ChatColor.AQUA + "Gem Slot 1",
            ChatColor.GRAY + "Place gem here"
        );
        gui.setItem(21, gemSlot1);

        ItemStack gemSlot2 = createItem(Material.LIGHT_BLUE_STAINED_GLASS_PANE,
            ChatColor.AQUA + "Gem Slot 2",
            ChatColor.GRAY + "Place gem here"
        );
        gui.setItem(23, gemSlot2);

        ItemStack gemSlot3 = createItem(Material.LIGHT_BLUE_STAINED_GLASS_PANE,
            ChatColor.AQUA + "Gem Slot 3",
            ChatColor.GRAY + "Place gem here"
        );
        gui.setItem(31, gemSlot3);

        // Socket button
        ItemStack socketButton = createItem(Material.ANVIL,
            ChatColor.GREEN + "Socket Gem",
            ChatColor.GRAY + "Click to socket the gem into equipment",
            "",
            ChatColor.YELLOW + "Cost: 100 coins per socket"
        );
        gui.setItem(40, socketButton);

        // Back button
        ItemStack back = createItem(Material.ARROW,
            ChatColor.YELLOW + "Back",
            ChatColor.GRAY + "Return to equipment hub"
        );
        gui.setItem(49, back);

        player.openInventory(gui);
    }

    public void openEnhancedItemsShowcase() {
        Inventory gui = Bukkit.createInventory(null, 54, ChatColor.GOLD + "Enhanced Items Showcase");

        // Display all available enhanced items from the manager
        int slot = 9;
        for (EnhancedItem enhancedItem : plugin.getEquipmentManager().getAllEnhancedItems()) {
            if (slot >= 45) break;

            ItemStack displayItem = enhancedItem.createItemStack();
            gui.setItem(slot, displayItem);
            slot++;
        }

        if (slot == 9) { // No enhanced items found
            ItemStack noItems = createItem(Material.BARRIER,
                ChatColor.RED + "No Enhanced Items",
                ChatColor.GRAY + "No enhanced items are available.",
                "",
                ChatColor.YELLOW + "Create some enhanced items first!"
            );
            gui.setItem(22, noItems);
        }

        // Back button
        ItemStack back = createItem(Material.ARROW,
            ChatColor.YELLOW + "Back",
            ChatColor.GRAY + "Return to equipment hub"
        );
        gui.setItem(49, back);

        player.openInventory(gui);
    }

    public void openEnhancementCrafting() {
        Inventory gui = Bukkit.createInventory(null, 54, ChatColor.YELLOW + "Enhancement Crafting");

        // Instructions
        ItemStack instructions = createItem(Material.BOOK,
            ChatColor.YELLOW + "Enhancement Crafting",
            ChatColor.GRAY + "Craft materials for enhancing equipment:",
            "",
            ChatColor.WHITE + "• Enhancement Stones",
            ChatColor.WHITE + "• Gem Fragments",
            ChatColor.WHITE + "• Socket Crystals",
            "",
            ChatColor.GRAY + "Use materials from mining and combat"
        );
        gui.setItem(4, instructions);

        // Craft Enhancement Stone
        ItemStack enhancementStone = createItem(Material.EMERALD,
            ChatColor.GREEN + "Craft Enhancement Stone",
            ChatColor.GRAY + "Required materials:",
            ChatColor.WHITE + "• 10 Iron Ingots",
            ChatColor.WHITE + "• 5 Diamonds",
            ChatColor.WHITE + "• 100 Experience",
            "",
            ChatColor.YELLOW + "Click to craft"
        );
        gui.setItem(20, enhancementStone);

        // Craft Gem Fragment
        ItemStack gemFragment = createItem(Material.PRISMARINE_SHARD,
            ChatColor.AQUA + "Craft Gem Fragment",
            ChatColor.GRAY + "Required materials:",
            ChatColor.WHITE + "• 5 Quartz",
            ChatColor.WHITE + "• 3 Lapis Lazuli",
            ChatColor.WHITE + "• 50 Experience",
            "",
            ChatColor.YELLOW + "Click to craft"
        );
        gui.setItem(22, gemFragment);

        // Craft Socket Crystal
        ItemStack socketCrystal = createItem(Material.AMETHYST_SHARD,
            ChatColor.LIGHT_PURPLE + "Craft Socket Crystal",
            ChatColor.GRAY + "Required materials:",
            ChatColor.WHITE + "• 1 Diamond",
            ChatColor.WHITE + "• 8 Redstone",
            ChatColor.WHITE + "• 75 Experience",
            "",
            ChatColor.YELLOW + "Click to craft"
        );
        gui.setItem(24, socketCrystal);

        // Back button
        ItemStack back = createItem(Material.ARROW,
            ChatColor.YELLOW + "Back",
            ChatColor.GRAY + "Return to equipment hub"
        );
        gui.setItem(49, back);

        player.openInventory(gui);
    }

    public void openDetailedStatistics() {
        Inventory gui = Bukkit.createInventory(null, 54, ChatColor.AQUA + "Detailed Equipment Statistics");

        // Player's total stats
        ItemStack totalStats = createItem(Material.BOOK,
            ChatColor.GOLD + "Total Equipment Stats",
            ChatColor.GRAY + "Your combined equipment statistics:",
            "",
            ChatColor.RED + "⚔ Total Attack Damage: " + calculateTotalAttackDamage(),
            ChatColor.BLUE + "🛡 Total Armor: " + calculateTotalArmor(),
            ChatColor.GREEN + "✨ Enhanced Items: " + countEnhancedItems(),
            ChatColor.LIGHT_PURPLE + "💎 Socketed Gems: " + countSocketedGems(),
            "",
            ChatColor.YELLOW + "Equipment Breakdown:"
        );
        gui.setItem(4, totalStats);

        // Individual equipment stats
        int slot = 19;
        String[] slotNames = {"Helmet", "Chestplate", "Leggings", "Boots", "Main Hand", "Off Hand"};
        ItemStack[] equipment = {
            player.getInventory().getHelmet(),
            player.getInventory().getChestplate(),
            player.getInventory().getLeggings(),
            player.getInventory().getBoots(),
            player.getInventory().getItemInMainHand(),
            player.getInventory().getItemInOffHand()
        };

        for (int i = 0; i < equipment.length; i++) {
            ItemStack item = equipment[i];
            String slotName = slotNames[i];

            if (item != null && item.getType() != Material.AIR) {
                ItemStack statItem = createEquipmentStatItem(item, slotName);
                gui.setItem(slot, statItem);
            } else {
                ItemStack emptySlot = createItem(Material.GRAY_STAINED_GLASS_PANE,
                    ChatColor.GRAY + slotName + " (Empty)",
                    ChatColor.DARK_GRAY + "No item equipped"
                );
                gui.setItem(slot, emptySlot);
            }
            slot++;
        }

        // Back button
        ItemStack back = createItem(Material.ARROW,
            ChatColor.YELLOW + "Back",
            ChatColor.GRAY + "Return to equipment hub"
        );
        gui.setItem(49, back);

        player.openInventory(gui);
    }

    private ItemStack createEquipmentDisplayItem(ItemStack equipment, String slotName) {
        ItemStack display = equipment.clone();
        ItemMeta meta = display.getItemMeta();
        
        List<String> lore = meta.hasLore() ? new ArrayList<>(meta.getLore()) : new ArrayList<>();
        lore.add("");
        lore.add(ChatColor.GRAY + "Equipped in: " + ChatColor.WHITE + slotName);
        lore.add(ChatColor.YELLOW + "Click to enhance or socket gems");
        
        meta.setLore(lore);
        display.setItemMeta(meta);
        
        return display;
    }
    
    private ItemStack createEmptySlotItem(String slotName) {
        return createItem(Material.GRAY_STAINED_GLASS_PANE,
            ChatColor.GRAY + "Empty " + slotName + " Slot",
            ChatColor.DARK_GRAY + "No item equipped",
            "",
            ChatColor.YELLOW + "Equip an item in this slot"
        );
    }
    
    private String getEquippedItemName(ItemStack item) {
        if (item == null || item.getType() == Material.AIR) {
            return ChatColor.GRAY + "None";
        }
        
        if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
            return item.getItemMeta().getDisplayName();
        }
        
        return ChatColor.WHITE + formatMaterialName(item.getType());
    }
    
    private String formatMaterialName(Material material) {
        String name = material.name().toLowerCase().replace("_", " ");
        String[] words = name.split(" ");
        StringBuilder formatted = new StringBuilder();
        
        for (String word : words) {
            if (formatted.length() > 0) formatted.append(" ");
            formatted.append(word.substring(0, 1).toUpperCase()).append(word.substring(1));
        }
        
        return formatted.toString();
    }

    private ItemStack createEquipmentStatItem(ItemStack equipment, String slotName) {
        List<String> lore = new ArrayList<>();
        lore.add(ChatColor.GRAY + "Slot: " + slotName);
        lore.add("");

        // Basic item info
        lore.add(ChatColor.YELLOW + "Item: " + ChatColor.WHITE + equipment.getType().name().toLowerCase().replace("_", " "));

        // Check if enhanced
        if (isEnhancedItem(equipment)) {
            lore.add(ChatColor.GREEN + "✨ Enhanced Item");

            // Try to extract enhancement level from lore
            if (equipment.hasItemMeta() && equipment.getItemMeta().hasLore()) {
                for (String loreLine : equipment.getItemMeta().getLore()) {
                    if (loreLine.contains("Enhancement:") || loreLine.contains("+")) {
                        lore.add(ChatColor.GREEN + loreLine);
                        break;
                    }
                }
            }
        }

        // Stats
        lore.add("");
        lore.add(ChatColor.YELLOW + "Stats:");

        if (slotName.equals("Main Hand") || slotName.equals("Off Hand")) {
            int damage = getItemAttackDamage(equipment);
            lore.add(ChatColor.RED + "⚔ Attack Damage: " + damage);
        } else {
            int armor = getItemArmorValue(equipment);
            lore.add(ChatColor.BLUE + "🛡 Armor: " + armor);
        }

        // Socketed gems
        int gemCount = getSocketedGemCount(equipment);
        if (gemCount > 0) {
            lore.add("");
            lore.add(ChatColor.LIGHT_PURPLE + "💎 Socketed Gems: " + gemCount);
        }

        return createItem(equipment.getType(), ChatColor.WHITE + slotName, lore.toArray(new String[0]));
    }

    private String calculateTotalAttackDamage() {
        int totalDamage = 0;

        // Check main hand weapon
        ItemStack mainHand = player.getInventory().getItemInMainHand();
        if (mainHand != null && mainHand.getType() != Material.AIR) {
            totalDamage += getItemAttackDamage(mainHand);
        }

        // Check off hand weapon
        ItemStack offHand = player.getInventory().getItemInOffHand();
        if (offHand != null && offHand.getType() != Material.AIR) {
            totalDamage += getItemAttackDamage(offHand);
        }

        return String.valueOf(totalDamage);
    }

    private String calculateTotalArmor() {
        int totalArmor = 0;

        // Check all armor pieces
        ItemStack[] armor = player.getInventory().getArmorContents();
        for (ItemStack piece : armor) {
            if (piece != null && piece.getType() != Material.AIR) {
                totalArmor += getItemArmorValue(piece);
            }
        }

        return String.valueOf(totalArmor);
    }

    private String countEnhancedItems() {
        int count = 0;

        // Check all equipment slots
        ItemStack[] equipment = {
            player.getInventory().getHelmet(),
            player.getInventory().getChestplate(),
            player.getInventory().getLeggings(),
            player.getInventory().getBoots(),
            player.getInventory().getItemInMainHand(),
            player.getInventory().getItemInOffHand()
        };

        for (ItemStack item : equipment) {
            if (item != null && item.getType() != Material.AIR && isEnhancedItem(item)) {
                count++;
            }
        }

        return String.valueOf(count);
    }

    private String countSocketedGems() {
        int count = 0;

        // Check all equipment slots for socketed gems
        ItemStack[] equipment = {
            player.getInventory().getHelmet(),
            player.getInventory().getChestplate(),
            player.getInventory().getLeggings(),
            player.getInventory().getBoots(),
            player.getInventory().getItemInMainHand(),
            player.getInventory().getItemInOffHand()
        };

        for (ItemStack item : equipment) {
            if (item != null && item.getType() != Material.AIR) {
                count += getSocketedGemCount(item);
            }
        }

        return String.valueOf(count);
    }
    
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        Player clickedPlayer = (Player) event.getWhoClicked();
        
        if (!clickedPlayer.equals(player)) return;
        
        String title = event.getView().getTitle();
        if (!title.startsWith(ChatColor.DARK_PURPLE + "Equipment Hub") &&
            !title.equals(ChatColor.AQUA + "Equipment Viewer") &&
            !title.equals(ChatColor.GOLD + "Enhancement Station") &&
            !title.equals(ChatColor.LIGHT_PURPLE + "Gem Collection") &&
            !title.equals(ChatColor.AQUA + "Gem Socketing Station") &&
            !title.equals(ChatColor.GOLD + "Enhanced Items Showcase") &&
            !title.equals(ChatColor.YELLOW + "Enhancement Crafting") &&
            !title.equals(ChatColor.AQUA + "Detailed Equipment Statistics")) return;
        
        int slot = event.getSlot();

        // Handle different GUI clicks with selective event cancellation
        if (title.startsWith(ChatColor.DARK_PURPLE + "Equipment Hub")) {
            event.setCancelled(true);
            handleEquipmentHubClick(slot);
        } else if (title.equals(ChatColor.AQUA + "Equipment Viewer")) {
            event.setCancelled(true);
            handleEquipmentViewerClick(slot);
        } else if (title.equals(ChatColor.GOLD + "Enhancement Station")) {
            handleEnhancementStationClick(event, slot);
        } else if (title.equals(ChatColor.LIGHT_PURPLE + "Gem Collection")) {
            event.setCancelled(true);
            handleGemCollectionClick(slot);
        } else if (title.equals(ChatColor.AQUA + "Gem Socketing Station")) {
            handleGemSocketingStationClick(event, slot);
        } else if (title.equals(ChatColor.GOLD + "Enhanced Items Showcase")) {
            event.setCancelled(true);
            handleEnhancedItemsShowcaseClick(slot);
        } else if (title.equals(ChatColor.YELLOW + "Enhancement Crafting")) {
            event.setCancelled(true);
            handleEnhancementCraftingClick(slot);
        } else if (title.equals(ChatColor.AQUA + "Detailed Equipment Statistics")) {
            event.setCancelled(true);
            handleDetailedStatisticsClick(slot);
        }
        
        player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
    }

    @EventHandler
    public void onInventoryDrag(org.bukkit.event.inventory.InventoryDragEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        Player dragPlayer = (Player) event.getWhoClicked();

        if (!dragPlayer.equals(player)) return;

        String title = event.getView().getTitle();
        if (!title.equals(ChatColor.GOLD + "Enhancement Station") &&
            !title.equals(ChatColor.AQUA + "Gem Socketing Station")) return;

        // Check if dragging to valid slots
        for (int slot : event.getRawSlots()) {
            if (slot >= 54) continue; // Allow dragging in player inventory

            if (title.equals(ChatColor.GOLD + "Enhancement Station")) {
                // Enhancement Station: Only allow slot 22 (enhancement slot)
                if (slot != 22) {
                    event.setCancelled(true);
                    return;
                }
            } else if (title.equals(ChatColor.AQUA + "Gem Socketing Station")) {
                // Gem Socketing Station: Allow interactive slots only
                if (!isGemSocketingInteractiveSlot(slot)) {
                    event.setCancelled(true);
                    return;
                }
            }
        }
    }

    private void handleEquipmentHubClick(int slot) {
        switch (slot) {
            case 10: // Equipment Viewer
                openEquipmentViewer();
                break;
            case 12: // Item Enhancement
                openEnhancementStation();
                break;
            case 14: // Gem Socketing
                openGemSocketingStation();
                break;
            case 16: // Gem Collection
                openGemCollection();
                break;
            case 28: // Enhanced Items Showcase
                openEnhancedItemsShowcase();
                break;
            case 30: // Crafting Station
                openEnhancementCrafting();
                break;
            case 32: // Statistics
                openDetailedStatistics();
                break;
            case 49: // Back
                CharacterHubGUI hubGUI = new CharacterHubGUI(plugin, player);
                hubGUI.open();
                break;
        }
    }
    
    private void handleEquipmentViewerClick(int slot) {
        if (slot == 49) { // Back button
            openEquipmentHub();
        } else {
            // TODO: Handle equipment slot clicks for enhancement/socketing
            player.sendMessage(ChatColor.YELLOW + "Equipment enhancement interface coming soon!");
        }
    }
    
    private void handleEnhancementStationClick(org.bukkit.event.inventory.InventoryClickEvent event, int slot) {
        // Interactive slots: 22 (enhancement slot)
        // Button slots: 31 (enhancement button), 49 (back button)

        if (slot == 22) {
            // Enhancement slot - allow item placement/removal
            return; // Don't cancel event, allow normal inventory interaction
        } else if (slot == 49) { // Back button
            event.setCancelled(true);
            openEquipmentHub();
        } else if (slot == 31) { // Enhancement button
            event.setCancelled(true);
            performEnhancement();
        } else {
            // Cancel clicks on decoration items
            event.setCancelled(true);
        }
    }

    private void performEnhancement() {
        // Get the item from the enhancement slot (slot 22)
        org.bukkit.inventory.Inventory gui = player.getOpenInventory().getTopInventory();
        ItemStack itemToEnhance = gui.getItem(22);

        if (itemToEnhance == null || itemToEnhance.getType() == Material.AIR) {
            player.sendMessage(ChatColor.RED + "Place an item in the enhancement slot first!");
            return;
        }

        // Check if it's a placeholder item (gray glass pane)
        if (itemToEnhance.getType() == Material.GRAY_STAINED_GLASS_PANE) {
            player.sendMessage(ChatColor.RED + "Place an item in the enhancement slot first!");
            return;
        }

        // Try to enhance the item using the EquipmentManager
        if (plugin.getEquipmentManager().enhanceItemStack(player, itemToEnhance)) {
            player.sendMessage(ChatColor.GREEN + "Item enhanced successfully!");

            // Update the item in the GUI slot
            gui.setItem(22, itemToEnhance);

            // Play success effects
            player.playSound(player.getLocation(), org.bukkit.Sound.BLOCK_ENCHANTMENT_TABLE_USE, 1.0f, 1.5f);
            player.getWorld().spawnParticle(org.bukkit.Particle.ENCHANT, player.getLocation().add(0, 1, 0), 20);

        } else {
            player.sendMessage(ChatColor.RED + "Failed to enhance item. Check if it can be enhanced and you have enough materials.");
        }
    }
    
    private void handleGemCollectionClick(int slot) {
        if (slot == 49) { // Back button
            openEquipmentHub();
        } else if (slot == 4) { // Give random gems
            giveRandomGems();
        }
        // Handle gem selection for socketing
    }

    private void handleGemSocketingStationClick(org.bukkit.event.inventory.InventoryClickEvent event, int slot) {
        // Interactive slots for gem socketing station
        if (isGemSocketingInteractiveSlot(slot)) {
            // Allow item placement/removal in interactive slots
            return; // Don't cancel event
        } else if (slot == 49) { // Back button
            event.setCancelled(true);
            openEquipmentHub();
        } else {
            // Cancel clicks on decoration items
            event.setCancelled(true);
        }
    }

    @EventHandler
    public void onInventoryClose(org.bukkit.event.inventory.InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) return;
        Player closingPlayer = (Player) event.getPlayer();

        if (closingPlayer.equals(player)) {
            // Unregister this GUI when the player closes any inventory
            HandlerList.unregisterAll(this);
            activeGUIs.remove(player.getUniqueId());
        }
    }

    public static void cleanupAllGUIs() {
        for (EquipmentGUI gui : activeGUIs.values()) {
            HandlerList.unregisterAll(gui);
        }
        activeGUIs.clear();
    }

    private boolean isGemSocketingInteractiveSlot(int slot) {
        // Define which slots are interactive in the gem socketing station
        // Based on the GUI layout, these would be equipment slot and gem slots
        // You may need to adjust these slot numbers based on your actual GUI layout
        return slot == 22 || // Equipment slot (center)
               (slot >= 19 && slot <= 25) || // Gem slots row
               (slot >= 28 && slot <= 34);   // Additional gem slots if needed
    }

    private void handleEnhancedItemsShowcaseClick(int slot) {
        if (slot == 49) { // Back button
            openEquipmentHub();
        }
        // Handle enhanced item viewing
    }

    private void handleEnhancementCraftingClick(int slot) {
        if (slot == 49) { // Back button
            openEquipmentHub();
        } else if (slot == 20) { // Craft Enhancement Stone
            craftEnhancementStone();
        } else if (slot == 22) { // Craft Gem Fragment
            craftGemFragment();
        } else if (slot == 24) { // Craft Socket Crystal
            craftSocketCrystal();
        }
    }

    private void handleDetailedStatisticsClick(int slot) {
        if (slot == 49) { // Back button
            openEquipmentHub();
        }
        // Handle statistics viewing
    }
    
    private void giveRandomGems() {
        for (int i = 0; i < 5; i++) {
            Gem randomGem = plugin.getEquipmentManager().getRandomGem();
            ItemStack gemItem = randomGem.createItemStack();
            
            if (player.getInventory().firstEmpty() != -1) {
                player.getInventory().addItem(gemItem);
            } else {
                player.getWorld().dropItem(player.getLocation(), gemItem);
            }
        }
        
        player.sendMessage(ChatColor.GREEN + "Received 5 random gems!");
        player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.5f);
    }

    private void craftEnhancementStone() {
        // Check if player has required materials
        if (hasRequiredMaterials(Material.IRON_INGOT, 10) &&
            hasRequiredMaterials(Material.DIAMOND, 5) &&
            player.getLevel() >= 100) {

            // Remove materials
            removeItems(Material.IRON_INGOT, 10);
            removeItems(Material.DIAMOND, 5);
            player.setLevel(player.getLevel() - 100);

            // Give enhancement stone
            ItemStack stone = createItem(Material.EMERALD,
                ChatColor.GREEN + "Enhancement Stone",
                ChatColor.GRAY + "Used to enhance equipment"
            );
            player.getInventory().addItem(stone);
            player.sendMessage(ChatColor.GREEN + "Crafted Enhancement Stone!");
            player.playSound(player.getLocation(), Sound.BLOCK_ANVIL_USE, 1.0f, 1.0f);
        } else {
            player.sendMessage(ChatColor.RED + "Insufficient materials!");
            player.playSound(player.getLocation(), Sound.BLOCK_NOTE_BLOCK_BASS, 1.0f, 0.5f);
        }
    }

    private void craftGemFragment() {
        if (hasRequiredMaterials(Material.QUARTZ, 5) &&
            hasRequiredMaterials(Material.LAPIS_LAZULI, 3) &&
            player.getLevel() >= 50) {

            removeItems(Material.QUARTZ, 5);
            removeItems(Material.LAPIS_LAZULI, 3);
            player.setLevel(player.getLevel() - 50);

            ItemStack fragment = createItem(Material.PRISMARINE_SHARD,
                ChatColor.AQUA + "Gem Fragment",
                ChatColor.GRAY + "Used to create gems"
            );
            player.getInventory().addItem(fragment);
            player.sendMessage(ChatColor.GREEN + "Crafted Gem Fragment!");
            player.playSound(player.getLocation(), Sound.BLOCK_ANVIL_USE, 1.0f, 1.2f);
        } else {
            player.sendMessage(ChatColor.RED + "Insufficient materials!");
            player.playSound(player.getLocation(), Sound.BLOCK_NOTE_BLOCK_BASS, 1.0f, 0.5f);
        }
    }

    private void craftSocketCrystal() {
        if (hasRequiredMaterials(Material.DIAMOND, 1) &&
            hasRequiredMaterials(Material.REDSTONE, 8) &&
            player.getLevel() >= 75) {

            removeItems(Material.DIAMOND, 1);
            removeItems(Material.REDSTONE, 8);
            player.setLevel(player.getLevel() - 75);

            ItemStack crystal = createItem(Material.AMETHYST_SHARD,
                ChatColor.LIGHT_PURPLE + "Socket Crystal",
                ChatColor.GRAY + "Used to add sockets to equipment"
            );
            player.getInventory().addItem(crystal);
            player.sendMessage(ChatColor.GREEN + "Crafted Socket Crystal!");
            player.playSound(player.getLocation(), Sound.BLOCK_ANVIL_USE, 1.0f, 1.4f);
        } else {
            player.sendMessage(ChatColor.RED + "Insufficient materials!");
            player.playSound(player.getLocation(), Sound.BLOCK_NOTE_BLOCK_BASS, 1.0f, 0.5f);
        }
    }

    private boolean hasRequiredMaterials(Material material, int amount) {
        int count = 0;
        for (ItemStack item : player.getInventory().getContents()) {
            if (item != null && item.getType() == material) {
                count += item.getAmount();
            }
        }
        return count >= amount;
    }

    private void removeItems(Material material, int amount) {
        int remaining = amount;
        for (ItemStack item : player.getInventory().getContents()) {
            if (item != null && item.getType() == material && remaining > 0) {
                int toRemove = Math.min(item.getAmount(), remaining);
                item.setAmount(item.getAmount() - toRemove);
                remaining -= toRemove;
                if (item.getAmount() <= 0) {
                    item.setType(Material.AIR);
                }
            }
        }
    }

    private ItemStack createItem(Material material, String name, String... lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(name);
        
        List<String> loreList = new ArrayList<>();
        for (String line : lore) {
            loreList.add(line);
        }
        meta.setLore(loreList);
        
        item.setItemMeta(meta);
        return item;
    }

    private int getItemAttackDamage(ItemStack item) {
        if (item == null || item.getType() == Material.AIR) return 0;

        // Base damage values for different weapon types
        switch (item.getType()) {
            case WOODEN_SWORD: return 4;
            case STONE_SWORD: return 5;
            case IRON_SWORD: return 6;
            case GOLDEN_SWORD: return 4;
            case DIAMOND_SWORD: return 7;
            case NETHERITE_SWORD: return 8;
            case WOODEN_AXE: return 7;
            case STONE_AXE: return 9;
            case IRON_AXE: return 9;
            case GOLDEN_AXE: return 7;
            case DIAMOND_AXE: return 9;
            case NETHERITE_AXE: return 10;
            case BOW: return 6;
            case CROSSBOW: return 8;
            case TRIDENT: return 9;
            default: return 1; // Default punch damage
        }
    }

    private int getItemArmorValue(ItemStack item) {
        if (item == null || item.getType() == Material.AIR) return 0;

        // Base armor values for different armor types
        switch (item.getType()) {
            // Leather armor
            case LEATHER_HELMET: return 1;
            case LEATHER_CHESTPLATE: return 3;
            case LEATHER_LEGGINGS: return 2;
            case LEATHER_BOOTS: return 1;
            // Chainmail armor
            case CHAINMAIL_HELMET: return 2;
            case CHAINMAIL_CHESTPLATE: return 5;
            case CHAINMAIL_LEGGINGS: return 4;
            case CHAINMAIL_BOOTS: return 1;
            // Iron armor
            case IRON_HELMET: return 2;
            case IRON_CHESTPLATE: return 6;
            case IRON_LEGGINGS: return 5;
            case IRON_BOOTS: return 2;
            // Golden armor
            case GOLDEN_HELMET: return 2;
            case GOLDEN_CHESTPLATE: return 5;
            case GOLDEN_LEGGINGS: return 3;
            case GOLDEN_BOOTS: return 1;
            // Diamond armor
            case DIAMOND_HELMET: return 3;
            case DIAMOND_CHESTPLATE: return 8;
            case DIAMOND_LEGGINGS: return 6;
            case DIAMOND_BOOTS: return 3;
            // Netherite armor
            case NETHERITE_HELMET: return 3;
            case NETHERITE_CHESTPLATE: return 8;
            case NETHERITE_LEGGINGS: return 6;
            case NETHERITE_BOOTS: return 3;
            default: return 0;
        }
    }

    private boolean isEnhancedItem(ItemStack item) {
        if (item == null || item.getType() == Material.AIR || !item.hasItemMeta()) return false;

        // Check if item has enhancement lore
        if (item.getItemMeta().hasLore()) {
            for (String lore : item.getItemMeta().getLore()) {
                if (lore.contains("Enhancement:") || lore.contains("+")) {
                    return true;
                }
            }
        }

        return false;
    }

    private int getSocketedGemCount(ItemStack item) {
        if (item == null || item.getType() == Material.AIR || !item.hasItemMeta()) return 0;

        int count = 0;
        if (item.getItemMeta().hasLore()) {
            for (String lore : item.getItemMeta().getLore()) {
                if (lore.contains("Socketed:") || lore.contains("Gem:")) {
                    count++;
                }
            }
        }

        return count;
    }
}
