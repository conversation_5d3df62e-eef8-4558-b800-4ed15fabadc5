# OrvynMMO Main Configuration

# Database settings
database:
  type: sqlite
  file: orvynmmo.db
  
# Performance settings
performance:
  tick-budget-ms: 5
  async-saves: true
  
# General settings
general:
  debug: false
  auto-save-interval: 60 # seconds

# Experience and Leveling System
experience:
  # Base XP required for level 2 (scales exponentially)
  base_xp_per_level: 100

  # XP multiplier for each level (1.5 = 50% more XP needed each level)
  xp_multiplier: 1.5

  # Maximum level players can reach
  max_level: 100

  # XP Sources
  sources:
    fishing: 5
    quest_base: 50  # Base XP for quests (modified by quest level)
    profession_multiplier: 2  # Multiplier for profession activities

  # Level-up Rewards
  rewards:
    skill_points_per_level: 1
    attribute_points_per_level: 2

  # Mob XP Rewards
  mob_rewards:
    zombie: 10
    skeleton: 12
    creeper: 15
    spider: 8
    enderman: 25
    witch: 20
    blaze: 30
    wither_skeleton: 40
    ender_dragon: 1000
    wither: 500

  # Block XP Rewards (Mining)
  block_rewards:
    coal_ore: 2
    iron_ore: 5
    gold_ore: 8
    diamond_ore: 15
    emerald_ore: 20
    ancient_debris: 50

# Health and Mana Regeneration System
regeneration:
  # Health regeneration settings
  health:
    enabled: true
    rate: 2.0  # Flat health per regeneration tick
    percent_per_tick: 0.02  # 2% of max health per tick

  # Mana regeneration settings
  mana:
    enabled: true
    rate: 5.0  # Flat mana per regeneration tick
    percent_per_tick: 0.05  # 5% of max mana per tick

  # Combat settings
  combat_timeout: 8000  # Time in ms before regeneration starts after combat
  interval: 2000  # Regeneration tick interval in ms

  # Visual effects
  show_effects: true  # Show particle and sound effects during regeneration

# Mob Health Bar and Level System
mob_system:
  # Holographic health bar settings
  health_bars:
    enabled: true
    view_distance: 20.0  # Maximum distance to show health bars
    field_of_view_angle: 60.0  # Field of view angle in degrees
    update_interval: 10  # Update interval in ticks (0.5 seconds)

  # Mob level progression system
  level_system:
    enabled: true
    blocks_per_level: 50.0  # Distance from spawn for each level increase
    health_scaling_per_level: 0.2  # Health multiplier per level (20% increase)
    max_level: 50  # Maximum mob level cap
