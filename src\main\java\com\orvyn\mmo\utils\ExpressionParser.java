package com.orvyn.mmo.utils;

import com.orvyn.mmo.data.PlayerData;
import com.orvyn.mmo.enums.Attribute;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ExpressionParser {
    
    private static final Pattern ATTRIBUTE_PATTERN = Pattern.compile("(STR|AGI|INT|HP|MANA|CRIT|HASTE)");
    
    public static double evaluate(String expression, PlayerData playerData) {
        if (expression == null || expression.trim().isEmpty()) {
            return 0;
        }
        
        String processed = expression.trim();
        
        // Replace attribute names with their values
        Matcher matcher = ATTRIBUTE_PATTERN.matcher(processed);
        StringBuffer sb = new StringBuffer();
        
        while (matcher.find()) {
            String attrName = matcher.group(1);
            Attribute attr = Attribute.fromString(attrName);
            int value = attr != null ? playerData.getTotalAttribute(attr) : 0;
            matcher.appendReplacement(sb, String.valueOf(value));
        }
        matcher.appendTail(sb);
        processed = sb.toString();
        
        // Simple expression evaluation
        return evaluateSimpleExpression(processed);
    }
    
    private static double evaluateSimpleExpression(String expression) {
        try {
            // Remove spaces
            expression = expression.replaceAll("\\s+", "");
            
            // Handle simple addition and multiplication
            if (expression.contains("+")) {
                String[] parts = expression.split("\\+");
                double sum = 0;
                for (String part : parts) {
                    sum += evaluateSimpleExpression(part);
                }
                return sum;
            }
            
            if (expression.contains("*")) {
                String[] parts = expression.split("\\*");
                double product = 1;
                for (String part : parts) {
                    product *= evaluateSimpleExpression(part);
                }
                return product;
            }
            
            // Just a number
            return Double.parseDouble(expression);
            
        } catch (Exception e) {
            // If parsing fails, return 0
            return 0;
        }
    }
}
