package com.orvyn.mmo.models;

import java.util.ArrayList;
import java.util.List;

public class Skill {
    
    private final String id;
    private String type;
    private double range;
    private long cooldown;
    private int manaCost;
    private String description;
    private List<String> effects = new ArrayList<>();
    
    public Skill(String id) {
        this.id = id;
    }
    
    public String getId() { return id; }
    public String getType() { return type; }
    public void setType(String type) { this.type = type; }
    public double getRange() { return range; }
    public void setRange(double range) { this.range = range; }
    public long getCooldown() { return cooldown; }
    public void setCooldown(long cooldown) { this.cooldown = cooldown; }
    public int getManaCost() { return manaCost; }
    public void setManaCost(int manaCost) { this.manaCost = manaCost; }
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    public List<String> getEffects() { return effects; }
    public void setEffects(List<String> effects) { this.effects = effects; }
}
