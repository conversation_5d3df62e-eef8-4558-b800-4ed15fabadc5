package com.orvyn.mmo.managers;

import com.orvyn.mmo.OrvynMMOPlugin;

import com.orvyn.mmo.models.EnhancedItem;
import com.orvyn.mmo.models.Gem;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

public class EquipmentManager implements Listener {
    
    private final OrvynMMOPlugin plugin;
    private final Map<String, EnhancedItem> enhancedItems = new HashMap<>();
    private final Map<String, Gem> availableGems = new HashMap<>();
    
    public EquipmentManager(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
        initializeGems();
        initializeEnhancedItems();
    }
    
    private void initializeGems() {
        // Create various gems of different rarities
        for (Gem.GemRarity rarity : Gem.GemRarity.values()) {
            // Damage gems
            Gem damageGem = Gem.createDamageGem(rarity);
            availableGems.put(damageGem.getGemId(), damageGem);
            
            // Defense gems
            Gem defenseGem = Gem.createDefenseGem(rarity);
            availableGems.put(defenseGem.getGemId(), defenseGem);
            
            // Speed gems
            Gem speedGem = Gem.createSpeedGem(rarity);
            availableGems.put(speedGem.getGemId(), speedGem);
            
            // Critical gems
            Gem criticalGem = Gem.createCriticalGem(rarity);
            availableGems.put(criticalGem.getGemId(), criticalGem);
            
            // Elemental gems
            String[] elements = {"Fire", "Ice", "Lightning", "Earth", "Wind"};
            for (String element : elements) {
                Gem elementalGem = Gem.createElementalGem(rarity, element);
                availableGems.put(elementalGem.getGemId(), elementalGem);
            }
        }
        
        // Skill gems
        createSkillGems();
    }
    
    private void createSkillGems() {
        // Warrior skill gems
        availableGems.put("skill_gem_charge", Gem.createSkillGem(Gem.GemRarity.RARE, "charge", "Charge"));
        availableGems.put("skill_gem_shield_bash", Gem.createSkillGem(Gem.GemRarity.RARE, "shield_bash", "Shield Bash"));
        availableGems.put("skill_gem_battle_cry", Gem.createSkillGem(Gem.GemRarity.EPIC, "battle_cry", "Battle Cry"));
        availableGems.put("skill_gem_whirlwind", Gem.createSkillGem(Gem.GemRarity.EPIC, "whirlwind", "Whirlwind"));
        
        // Mage skill gems
        availableGems.put("skill_gem_fireball", Gem.createSkillGem(Gem.GemRarity.UNCOMMON, "fireball", "Fireball"));
        availableGems.put("skill_gem_ice_blast", Gem.createSkillGem(Gem.GemRarity.RARE, "ice_blast", "Ice Blast"));
        availableGems.put("skill_gem_lightning_bolt", Gem.createSkillGem(Gem.GemRarity.RARE, "lightning_bolt", "Lightning Bolt"));
        availableGems.put("skill_gem_teleport", Gem.createSkillGem(Gem.GemRarity.EPIC, "teleport", "Teleport"));
        
        // Archer skill gems
        availableGems.put("skill_gem_power_shot", Gem.createSkillGem(Gem.GemRarity.UNCOMMON, "power_shot", "Power Shot"));
        availableGems.put("skill_gem_multi_shot", Gem.createSkillGem(Gem.GemRarity.RARE, "multi_shot", "Multi Shot"));
        availableGems.put("skill_gem_explosive_arrow", Gem.createSkillGem(Gem.GemRarity.EPIC, "explosive_arrow", "Explosive Arrow"));
        availableGems.put("skill_gem_eagle_eye", Gem.createSkillGem(Gem.GemRarity.RARE, "eagle_eye", "Eagle Eye"));
    }
    
    private void initializeEnhancedItems() {
        // Create some example enhanced items
        
        // Weapons
        Map<String, Integer> swordStats = new HashMap<>();
        swordStats.put("attack_damage", 8);
        swordStats.put("attack_speed", 5);
        EnhancedItem enhancedSword = new EnhancedItem(
            "enhanced_iron_sword",
            EnhancedItem.ItemType.WEAPON,
            Material.IRON_SWORD,
            "Enhanced Iron Sword",
            10,
            swordStats,
            2
        );
        enhancedItems.put(enhancedSword.getItemId(), enhancedSword);
        
        // Armor
        Map<String, Integer> chestplateStats = new HashMap<>();
        chestplateStats.put("armor", 6);
        chestplateStats.put("armor_toughness", 2);
        EnhancedItem enhancedChestplate = new EnhancedItem(
            "enhanced_iron_chestplate",
            EnhancedItem.ItemType.ARMOR,
            Material.IRON_CHESTPLATE,
            "Enhanced Iron Chestplate",
            10,
            chestplateStats,
            3
        );
        enhancedItems.put(enhancedChestplate.getItemId(), enhancedChestplate);
        
        // Tools
        Map<String, Integer> pickaxeStats = new HashMap<>();
        pickaxeStats.put("mining_speed", 10);
        pickaxeStats.put("durability", 250);
        EnhancedItem enhancedPickaxe = new EnhancedItem(
            "enhanced_iron_pickaxe",
            EnhancedItem.ItemType.TOOL,
            Material.IRON_PICKAXE,
            "Enhanced Iron Pickaxe",
            8,
            pickaxeStats,
            1
        );
        enhancedItems.put(enhancedPickaxe.getItemId(), enhancedPickaxe);
    }
    
    public EnhancedItem createEnhancedItem(ItemStack baseItem) {
        if (baseItem == null || baseItem.getType() == Material.AIR) return null;
        
        String itemId = "enhanced_" + baseItem.getType().name().toLowerCase() + "_" + System.currentTimeMillis();
        EnhancedItem.ItemType type = determineItemType(baseItem.getType());
        String name = "Enhanced " + formatMaterialName(baseItem.getType());
        
        Map<String, Integer> baseStats = generateBaseStats(baseItem.getType(), type);
        int maxSockets = determineMaxSockets(baseItem.getType(), type);
        int baseLevel = determineBaseLevel(baseItem.getType());
        
        return new EnhancedItem(itemId, type, baseItem.getType(), name, baseLevel, baseStats, maxSockets);
    }
    
    private EnhancedItem.ItemType determineItemType(Material material) {
        String name = material.name();
        if (name.contains("SWORD") || name.contains("AXE") || name.contains("BOW") || name.contains("TRIDENT")) {
            return EnhancedItem.ItemType.WEAPON;
        } else if (name.contains("HELMET") || name.contains("CHESTPLATE") || name.contains("LEGGINGS") || name.contains("BOOTS")) {
            return EnhancedItem.ItemType.ARMOR;
        } else if (name.contains("PICKAXE") || name.contains("SHOVEL") || name.contains("HOE") || name.contains("SHEARS")) {
            return EnhancedItem.ItemType.TOOL;
        } else {
            return EnhancedItem.ItemType.ACCESSORY;
        }
    }
    
    private Map<String, Integer> generateBaseStats(Material material, EnhancedItem.ItemType type) {
        Map<String, Integer> stats = new HashMap<>();
        
        switch (type) {
            case WEAPON:
                stats.put("attack_damage", getBaseDamage(material));
                stats.put("attack_speed", getBaseAttackSpeed(material));
                break;
            case ARMOR:
                stats.put("armor", getBaseArmor(material));
                stats.put("armor_toughness", getBaseToughness(material));
                break;
            case TOOL:
                stats.put("mining_speed", getBaseMiningSpeed(material));
                stats.put("durability", getBaseDurability(material));
                break;
            case ACCESSORY:
                stats.put("health", 20);
                stats.put("mana", 50);
                break;
        }
        
        return stats;
    }
    
    private int determineMaxSockets(Material material, EnhancedItem.ItemType type) {
        switch (type) {
            case WEAPON:
                return material.name().contains("DIAMOND") || material.name().contains("NETHERITE") ? 3 : 2;
            case ARMOR:
                return material.name().contains("CHESTPLATE") ? 3 : 2;
            case TOOL:
                return 1;
            case ACCESSORY:
                return 2;
            default:
                return 1;
        }
    }
    
    private int determineBaseLevel(Material material) {
        String name = material.name();
        if (name.contains("WOOD") || name.contains("LEATHER")) return 1;
        if (name.contains("STONE")) return 3;
        if (name.contains("IRON")) return 10;
        if (name.contains("GOLD")) return 8;
        if (name.contains("DIAMOND")) return 20;
        if (name.contains("NETHERITE")) return 30;
        return 5; // Default
    }

    private int getBaseAttackSpeed(Material material) {
        String name = material.name();
        if (name.contains("SWORD")) return 6;
        if (name.contains("AXE")) return 4;
        if (name.contains("BOW")) return 3;
        return 5;
    }



    private int getBaseToughness(Material material) {
        String name = material.name();
        if (name.contains("DIAMOND")) return 2;
        if (name.contains("NETHERITE")) return 3;
        return 0;
    }
    
    private int getBaseMiningSpeed(Material material) {
        String name = material.name();
        if (name.contains("WOOD")) return 2;
        if (name.contains("STONE")) return 4;
        if (name.contains("IRON")) return 6;
        if (name.contains("GOLD")) return 12;
        if (name.contains("DIAMOND")) return 8;
        if (name.contains("NETHERITE")) return 9;
        return 1;
    }
    
    private int getBaseDurability(Material material) {
        String name = material.name();
        if (name.contains("WOOD")) return 59;
        if (name.contains("STONE")) return 131;
        if (name.contains("IRON")) return 250;
        if (name.contains("GOLD")) return 32;
        if (name.contains("DIAMOND")) return 1561;
        if (name.contains("NETHERITE")) return 2031;
        return 100;
    }
    
    private String formatMaterialName(Material material) {
        String name = material.name().toLowerCase().replace("_", " ");
        String[] words = name.split(" ");
        StringBuilder formatted = new StringBuilder();
        
        for (String word : words) {
            if (formatted.length() > 0) formatted.append(" ");
            formatted.append(word.substring(0, 1).toUpperCase()).append(word.substring(1));
        }
        
        return formatted.toString();
    }
    
    public boolean enhanceItem(Player player, EnhancedItem item) {
        if (!item.canBeEnhanced()) {
            player.sendMessage(ChatColor.RED + "This item cannot be enhanced further!");
            return false;
        }
        
        // TODO: Check if player has enough currency/materials
        // PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        // int cost = item.getEnhancementCost();
        
        if (item.enhance()) {
            player.sendMessage(ChatColor.GREEN + "Successfully enhanced " + item.getName() + " to +" + item.getEnhancementLevel() + "!");
            player.playSound(player.getLocation(), Sound.BLOCK_ANVIL_USE, 1.0f, 1.2f);
            
            // Visual effects
            player.getWorld().spawnParticle(org.bukkit.Particle.ENCHANT, player.getLocation().add(0, 1, 0), 20);
            
            return true;
        }
        
        return false;
    }
    
    public boolean socketGem(Player player, EnhancedItem item, Gem gem, int socketIndex) {
        if (!item.canSocketGem(gem, socketIndex)) {
            player.sendMessage(ChatColor.RED + "Cannot socket gem in this slot!");
            return false;
        }
        
        if (item.socketGem(gem, socketIndex)) {
            player.sendMessage(ChatColor.GREEN + "Successfully socketed " + gem.getDisplayName() + " Gem!");
            player.playSound(player.getLocation(), Sound.BLOCK_ENCHANTMENT_TABLE_USE, 1.0f, 1.5f);
            
            // Visual effects
            player.getWorld().spawnParticle(org.bukkit.Particle.HAPPY_VILLAGER, player.getLocation().add(0, 1, 0), 15);
            
            return true;
        }
        
        return false;
    }
    
    public Gem removeGem(Player player, EnhancedItem item, int socketIndex) {
        Gem removedGem = item.removeGem(socketIndex);
        if (removedGem != null) {
            player.sendMessage(ChatColor.YELLOW + "Removed " + removedGem.getDisplayName() + " Gem from " + item.getName());
            player.playSound(player.getLocation(), Sound.BLOCK_STONE_BUTTON_CLICK_OFF, 1.0f, 1.0f);
            return removedGem;
        }
        return null;
    }
    
    public Gem getRandomGem() {
        List<Gem> gems = new ArrayList<>(availableGems.values());
        return gems.get(ThreadLocalRandom.current().nextInt(gems.size()));
    }
    
    public Gem getGem(String gemId) {
        return availableGems.get(gemId);
    }
    
    public EnhancedItem getEnhancedItem(String itemId) {
        return enhancedItems.get(itemId);
    }
    
    public Collection<EnhancedItem> getAllEnhancedItems() {
        return enhancedItems.values();
    }
    
    public Collection<Gem> getAllGems() {
        return availableGems.values();
    }
    
    public boolean enhanceItemStack(Player player, ItemStack itemStack) {
        if (itemStack == null || itemStack.getType() == Material.AIR) {
            return false;
        }

        // Check if this is already an enhanced item
        if (isEnhancedItemStack(itemStack)) {
            player.sendMessage(ChatColor.YELLOW + "This item is already enhanced! Use the enhanced item system for further upgrades.");
            return false;
        }

        // Create a basic enhanced version of the item
        EnhancedItem enhancedItem = createEnhancedFromItemStack(itemStack);
        if (enhancedItem == null) {
            player.sendMessage(ChatColor.RED + "This item cannot be enhanced!");
            return false;
        }

        // Enhance it once
        if (enhancedItem.enhance()) {
            // Replace the item in player's hand
            ItemStack enhancedItemStack = enhancedItem.createItemStack();
            player.getInventory().setItemInMainHand(enhancedItemStack);

            // Visual and audio effects
            player.getWorld().spawnParticle(Particle.ENCHANT, player.getLocation().add(0, 1, 0), 20);
            player.playSound(player.getLocation(), Sound.BLOCK_ANVIL_USE, 1.0f, 1.0f);

            return true;
        }

        return false;
    }

    private boolean isEnhancedItemStack(ItemStack item) {
        if (!item.hasItemMeta() || !item.getItemMeta().hasLore()) return false;

        for (String lore : item.getItemMeta().getLore()) {
            if (lore.contains("Enhancement:") || lore.contains("+")) {
                return true;
            }
        }
        return false;
    }

    private EnhancedItem createEnhancedFromItemStack(ItemStack item) {
        // Determine item type
        EnhancedItem.ItemType itemType = getItemType(item.getType());
        if (itemType == null) return null;

        // Create enhanced item
        String itemId = "enhanced_" + item.getType().name().toLowerCase();
        String name = item.getType().name().toLowerCase().replace("_", " ");

        // Base stats based on item type and material
        Map<String, Integer> baseStats = calculateBaseStats(item.getType());

        // Determine sockets based on item rarity (1-3 sockets)
        int sockets = 1; // Default to 1 socket for regular items
        if (item.getType().name().contains("DIAMOND")) sockets = 2;
        if (item.getType().name().contains("NETHERITE")) sockets = 3;

        EnhancedItem enhancedItem = new EnhancedItem(
            itemId, itemType, item.getType(), name, 1, baseStats, sockets
        );

        return enhancedItem;
    }

    private EnhancedItem.ItemType getItemType(Material material) {
        String name = material.name();
        if (name.contains("SWORD") || name.contains("AXE") || name.contains("BOW") ||
            name.contains("CROSSBOW") || name.contains("TRIDENT")) {
            return EnhancedItem.ItemType.WEAPON;
        } else if (name.contains("HELMET") || name.contains("CHESTPLATE") ||
                   name.contains("LEGGINGS") || name.contains("BOOTS")) {
            return EnhancedItem.ItemType.ARMOR;
        } else if (name.contains("PICKAXE") || name.contains("SHOVEL") ||
                   name.contains("HOE") || name.contains("SHEARS")) {
            return EnhancedItem.ItemType.TOOL;
        }
        return null; // Not enhanceable
    }

    private Map<String, Integer> calculateBaseStats(Material material) {
        Map<String, Integer> stats = new HashMap<>();

        String name = material.name();
        if (name.contains("SWORD") || name.contains("AXE") || name.contains("BOW") ||
            name.contains("CROSSBOW") || name.contains("TRIDENT")) {
            // Weapon stats
            stats.put("damage", getBaseDamage(material));
            stats.put("critical_chance", 5);
        } else if (name.contains("HELMET") || name.contains("CHESTPLATE") ||
                   name.contains("LEGGINGS") || name.contains("BOOTS")) {
            // Armor stats
            stats.put("armor", getBaseArmor(material));
            stats.put("health", 10);
        } else if (name.contains("PICKAXE") || name.contains("SHOVEL") ||
                   name.contains("HOE")) {
            // Tool stats
            stats.put("efficiency", 1);
            stats.put("durability", 50);
        }

        return stats;
    }

    private int getBaseDamage(Material material) {
        switch (material) {
            case WOODEN_SWORD: return 4;
            case STONE_SWORD: return 5;
            case IRON_SWORD: return 6;
            case GOLDEN_SWORD: return 4;
            case DIAMOND_SWORD: return 7;
            case NETHERITE_SWORD: return 8;
            case WOODEN_AXE: return 7;
            case STONE_AXE: return 9;
            case IRON_AXE: return 9;
            case GOLDEN_AXE: return 7;
            case DIAMOND_AXE: return 9;
            case NETHERITE_AXE: return 10;
            case BOW: return 6;
            case CROSSBOW: return 8;
            case TRIDENT: return 9;
            default: return 1;
        }
    }

    private int getBaseArmor(Material material) {
        switch (material) {
            case LEATHER_HELMET: return 1;
            case LEATHER_CHESTPLATE: return 3;
            case LEATHER_LEGGINGS: return 2;
            case LEATHER_BOOTS: return 1;
            case CHAINMAIL_HELMET: return 2;
            case CHAINMAIL_CHESTPLATE: return 5;
            case CHAINMAIL_LEGGINGS: return 4;
            case CHAINMAIL_BOOTS: return 1;
            case IRON_HELMET: return 2;
            case IRON_CHESTPLATE: return 6;
            case IRON_LEGGINGS: return 5;
            case IRON_BOOTS: return 2;
            case GOLDEN_HELMET: return 2;
            case GOLDEN_CHESTPLATE: return 5;
            case GOLDEN_LEGGINGS: return 3;
            case GOLDEN_BOOTS: return 1;
            case DIAMOND_HELMET: return 3;
            case DIAMOND_CHESTPLATE: return 8;
            case DIAMOND_LEGGINGS: return 6;
            case DIAMOND_BOOTS: return 3;
            case NETHERITE_HELMET: return 3;
            case NETHERITE_CHESTPLATE: return 8;
            case NETHERITE_LEGGINGS: return 6;
            case NETHERITE_BOOTS: return 3;
            default: return 0;
        }
    }

    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        // TODO: Handle gem socketing interactions
    }
}
