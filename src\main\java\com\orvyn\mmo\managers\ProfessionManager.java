package com.orvyn.mmo.managers;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.player.PlayerFishEvent;
import org.bukkit.inventory.ItemStack;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

public class ProfessionManager implements Listener {

    private final OrvynMMOPlugin plugin;
    private final Map<Material, Integer> miningExp = new HashMap<>();
    private final Map<Material, ItemStack[]> miningDrops = new HashMap<>();
    private final Map<Material, Integer> woodcuttingExp = new HashMap<>();
    private final Map<Material, ItemStack[]> woodcuttingDrops = new HashMap<>();
    private final Map<Material, Integer> farmingExp = new HashMap<>();
    private final Map<Material, ItemStack[]> farmingDrops = new HashMap<>();
    private final Random random = new Random();

    public ProfessionManager(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
        setupProfessions();
    }

    private void setupProfessions() {
        // Mining experience values
        miningExp.put(Material.STONE, 1);
        miningExp.put(Material.COAL_ORE, 5);
        miningExp.put(Material.COPPER_ORE, 8);
        miningExp.put(Material.IRON_ORE, 12);
        miningExp.put(Material.GOLD_ORE, 20);
        miningExp.put(Material.DIAMOND_ORE, 50);
        miningExp.put(Material.EMERALD_ORE, 75);
        miningExp.put(Material.DEEPSLATE_COAL_ORE, 6);
        miningExp.put(Material.DEEPSLATE_COPPER_ORE, 10);
        miningExp.put(Material.DEEPSLATE_IRON_ORE, 15);
        miningExp.put(Material.DEEPSLATE_GOLD_ORE, 25);
        miningExp.put(Material.DEEPSLATE_DIAMOND_ORE, 60);
        miningExp.put(Material.DEEPSLATE_EMERALD_ORE, 90);

        // Mining bonus drops (chance for extra items)
        miningDrops.put(Material.COAL_ORE, new ItemStack[]{new ItemStack(Material.COAL, 1)});
        miningDrops.put(Material.COPPER_ORE, new ItemStack[]{new ItemStack(Material.RAW_COPPER, 1)});
        miningDrops.put(Material.IRON_ORE, new ItemStack[]{new ItemStack(Material.RAW_IRON, 1)});
        miningDrops.put(Material.GOLD_ORE, new ItemStack[]{new ItemStack(Material.RAW_GOLD, 1)});
        miningDrops.put(Material.DIAMOND_ORE, new ItemStack[]{new ItemStack(Material.DIAMOND, 1)});
        miningDrops.put(Material.EMERALD_ORE, new ItemStack[]{new ItemStack(Material.EMERALD, 1)});

        // Woodcutting experience values
        woodcuttingExp.put(Material.OAK_LOG, 2);
        woodcuttingExp.put(Material.BIRCH_LOG, 2);
        woodcuttingExp.put(Material.SPRUCE_LOG, 2);
        woodcuttingExp.put(Material.JUNGLE_LOG, 3);
        woodcuttingExp.put(Material.ACACIA_LOG, 3);
        woodcuttingExp.put(Material.DARK_OAK_LOG, 3);
        woodcuttingExp.put(Material.MANGROVE_LOG, 4);
        woodcuttingExp.put(Material.CHERRY_LOG, 4);

        // Woodcutting bonus drops
        woodcuttingDrops.put(Material.OAK_LOG, new ItemStack[]{new ItemStack(Material.STICK, 2)});
        woodcuttingDrops.put(Material.BIRCH_LOG, new ItemStack[]{new ItemStack(Material.STICK, 2)});
        woodcuttingDrops.put(Material.SPRUCE_LOG, new ItemStack[]{new ItemStack(Material.STICK, 2)});

        // Farming experience values
        farmingExp.put(Material.WHEAT, 3);
        farmingExp.put(Material.CARROTS, 3);
        farmingExp.put(Material.POTATOES, 3);
        farmingExp.put(Material.BEETROOTS, 4);
        farmingExp.put(Material.PUMPKIN, 5);
        farmingExp.put(Material.MELON, 5);
        farmingExp.put(Material.SUGAR_CANE, 2);

        // Farming bonus drops
        farmingDrops.put(Material.WHEAT, new ItemStack[]{new ItemStack(Material.WHEAT_SEEDS, 1)});
        farmingDrops.put(Material.CARROTS, new ItemStack[]{new ItemStack(Material.CARROT, 1)});
        farmingDrops.put(Material.POTATOES, new ItemStack[]{new ItemStack(Material.POTATO, 1)});
    }

    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        Player player = event.getPlayer();
        Material blockType = event.getBlock().getType();

        // Check if it's a mining profession block
        if (miningExp.containsKey(blockType)) {
            handleMining(player, blockType, event);
        }
        // Check if it's a woodcutting profession block
        else if (woodcuttingExp.containsKey(blockType)) {
            handleWoodcutting(player, blockType, event);
        }
        // Check if it's a farming profession block
        else if (farmingExp.containsKey(blockType)) {
            handleFarming(player, blockType, event);
        }
    }

    private void handleMining(Player player, Material blockType, BlockBreakEvent event) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);

        // Get experience for this block
        int expGain = miningExp.get(blockType);

        // Apply mining level bonus
        int miningLevel = data.getProfessionLevel("mining");
        expGain += miningLevel / 10; // +1 exp per 10 levels

        // Add experience (PlayerData will handle level up automatically)
        int oldLevel = miningLevel;
        data.addProfessionExp("mining", expGain);
        int newLevel = data.getProfessionLevel("mining");

        // Check if level increased
        if (newLevel > oldLevel) {
            player.sendMessage(ChatColor.GOLD + "Mining level up! You are now level " + newLevel);
            player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.5f);
        }

        // Chance for bonus drops based on mining level
        if (miningDrops.containsKey(blockType)) {
            double bonusChance = Math.min(0.5, miningLevel * 0.02); // Max 50% at level 25
            if (random.nextDouble() < bonusChance) {
                ItemStack[] bonusItems = miningDrops.get(blockType);
                for (ItemStack item : bonusItems) {
                    event.getBlock().getWorld().dropItemNaturally(event.getBlock().getLocation(), item.clone());
                }
                player.sendMessage(ChatColor.GREEN + "Bonus drop!");
            }
        }

        // Show experience gain
        if (expGain > 0) {
            player.sendMessage(ChatColor.AQUA + "+" + expGain + " Mining XP");
        }
    }

    private void handleWoodcutting(Player player, Material blockType, BlockBreakEvent event) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);

        // Get experience for this block
        int expGain = woodcuttingExp.get(blockType);

        // Apply woodcutting level bonus
        int woodcuttingLevel = data.getProfessionLevel("woodcutting");
        expGain += woodcuttingLevel / 10; // +1 exp per 10 levels

        // Add experience (PlayerData will handle level up automatically)
        int oldLevel = woodcuttingLevel;
        data.addProfessionExp("woodcutting", expGain);
        int newLevel = data.getProfessionLevel("woodcutting");

        // Check if level increased
        if (newLevel > oldLevel) {
            player.sendMessage(ChatColor.GOLD + "Woodcutting level up! You are now level " + newLevel);
            player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.5f);
        }

        // Chance for bonus drops based on woodcutting level
        if (woodcuttingDrops.containsKey(blockType)) {
            double bonusChance = Math.min(0.4, woodcuttingLevel * 0.015); // Max 40% at level 27
            if (random.nextDouble() < bonusChance) {
                ItemStack[] bonusItems = woodcuttingDrops.get(blockType);
                for (ItemStack item : bonusItems) {
                    event.getBlock().getWorld().dropItemNaturally(event.getBlock().getLocation(), item.clone());
                }
                player.sendMessage(ChatColor.GREEN + "Bonus drop!");
            }
        }

        // Show experience gain
        if (expGain > 0) {
            player.sendMessage(ChatColor.AQUA + "+" + expGain + " Woodcutting XP");
        }
    }

    private void handleFarming(Player player, Material blockType, BlockBreakEvent event) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);

        // Get experience for this block
        int expGain = farmingExp.get(blockType);

        // Apply farming level bonus
        int farmingLevel = data.getProfessionLevel("farming");
        expGain += farmingLevel / 8; // +1 exp per 8 levels

        // Add experience (PlayerData will handle level up automatically)
        int oldLevel = farmingLevel;
        data.addProfessionExp("farming", expGain);
        int newLevel = data.getProfessionLevel("farming");

        // Check if level increased
        if (newLevel > oldLevel) {
            player.sendMessage(ChatColor.GOLD + "Farming level up! You are now level " + newLevel);
            player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.5f);
        }

        // Chance for bonus drops based on farming level
        if (farmingDrops.containsKey(blockType)) {
            double bonusChance = Math.min(0.6, farmingLevel * 0.025); // Max 60% at level 24
            if (random.nextDouble() < bonusChance) {
                ItemStack[] bonusItems = farmingDrops.get(blockType);
                for (ItemStack item : bonusItems) {
                    event.getBlock().getWorld().dropItemNaturally(event.getBlock().getLocation(), item.clone());
                }
                player.sendMessage(ChatColor.GREEN + "Bonus drop!");
            }
        }

        // Show experience gain
        if (expGain > 0) {
            player.sendMessage(ChatColor.AQUA + "+" + expGain + " Farming XP");
        }
    }

    @EventHandler
    public void onPlayerFish(PlayerFishEvent event) {
        if (event.getState() == PlayerFishEvent.State.CAUGHT_FISH) {
            Player player = event.getPlayer();
            PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);

            // Base fishing experience
            int expGain = 3;

            // Apply fishing level bonus
            int fishingLevel = data.getProfessionLevel("fishing");
            expGain += fishingLevel / 15; // +1 exp per 15 levels

            // Add experience (PlayerData will handle level up automatically)
            int oldLevel = fishingLevel;
            data.addProfessionExp("fishing", expGain);
            int newLevel = data.getProfessionLevel("fishing");

            // Check if level increased
            if (newLevel > oldLevel) {
                player.sendMessage(ChatColor.GOLD + "Fishing level up! You are now level " + newLevel);
                player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.5f);
            }

            // Chance for rare catches based on fishing level
            double rareChance = Math.min(0.15, fishingLevel * 0.005); // Max 15% at level 30
            if (random.nextDouble() < rareChance) {
                ItemStack rareCatch = getRareFishingDrop(fishingLevel);
                if (rareCatch != null) {
                    player.getWorld().dropItemNaturally(player.getLocation(), rareCatch);
                    player.sendMessage(ChatColor.YELLOW + "Rare catch!");
                }
            }

            // Show experience gain
            player.sendMessage(ChatColor.AQUA + "+" + expGain + " Fishing XP");
        }
    }

    private ItemStack getRareFishingDrop(int fishingLevel) {
        if (fishingLevel >= 30 && random.nextDouble() < 0.01) {
            return new ItemStack(Material.DIAMOND, 1);
        } else if (fishingLevel >= 20 && random.nextDouble() < 0.05) {
            return new ItemStack(Material.EMERALD, 1);
        } else if (fishingLevel >= 10 && random.nextDouble() < 0.1) {
            return new ItemStack(Material.GOLD_INGOT, 1);
        } else if (random.nextDouble() < 0.2) {
            return new ItemStack(Material.IRON_INGOT, 1);
        }
        return null;
    }

    public long getRequiredExpForLevel(int level) {
        // Use the same formula as PlayerData: level * 50
        return level * 50L;
    }

    public void reload() {
        setupProfessions();
    }

    public void shutdown() {
        // Cleanup if needed
    }
}
