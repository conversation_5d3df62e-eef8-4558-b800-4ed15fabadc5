package com.orvyn.mmo.models;

public class QuestObjective {

    private final String id;
    private final Quest.ObjectiveType objectiveType;
    private final String type; // Keep for backward compatibility
    private final String target;
    private final int amount;
    private final String description;

    // Backward compatibility constructor
    public QuestObjective(String id, String type, String target, int amount, String description) {
        this.id = id;
        this.type = type;
        this.target = target;
        this.amount = amount;
        this.description = description;
        this.objectiveType = parseObjectiveType(type);
    }

    // New constructor with ObjectiveType
    public QuestObjective(String id, Quest.ObjectiveType objectiveType, String target, int amount, String description) {
        this.id = id;
        this.objectiveType = objectiveType;
        this.type = objectiveType.name().toLowerCase();
        this.target = target;
        this.amount = amount;
        this.description = description;
    }

    private Quest.ObjectiveType parseObjectiveType(String type) {
        try {
            return Quest.ObjectiveType.valueOf(type.toUpperCase());
        } catch (IllegalArgumentException e) {
            // Default fallback for unknown types
            return Quest.ObjectiveType.COLLECT_ITEMS;
        }
    }

    public String getId() { return id; }
    public String getType() { return type; }
    public Quest.ObjectiveType getObjectiveType() { return objectiveType; }
    public String getTarget() { return target; }
    public int getAmount() { return amount; }
    public String getDescription() { return description; }

    public String getFormattedDescription() {
        switch (objectiveType) {
            case KILL_MOBS:
                return "Kill " + amount + " " + formatTarget(target);
            case COLLECT_ITEMS:
                return "Collect " + amount + " " + formatTarget(target);
            case MINE_BLOCKS:
                return "Mine " + amount + " " + formatTarget(target);
            case FISH_ITEMS:
                return "Fish " + amount + " items";
            case CRAFT_ITEMS:
                return "Craft " + amount + " " + formatTarget(target);
            case GAIN_PROFESSION_XP:
                return "Gain " + amount + " " + target + " XP";
            case DEAL_DAMAGE:
                return "Deal " + amount + " damage to mobs";
            case SURVIVE_TIME:
                return "Survive for " + (amount / 60) + " minutes";
            case REACH_LOCATION:
                return "Reach location: " + target;
            default:
                return description != null ? description : "Complete objective";
        }
    }

    private String formatTarget(String target) {
        if (target == null) return "items";
        return target.toLowerCase().replace("_", " ");
    }
}
