package com.orvyn.mmo.managers;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import com.orvyn.mmo.data.QuestProgress;
import com.orvyn.mmo.events.OrvynQuestCompleteEvent;
import com.orvyn.mmo.models.Quest;
import com.orvyn.mmo.models.QuestObjective;
import com.orvyn.mmo.models.QuestReward;
import com.orvyn.mmo.models.QuestTemplate;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.event.player.PlayerFishEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

public class QuestManager implements Listener {

    private final OrvynMMOPlugin plugin;
    private final Map<String, Quest> sharedQuests = new HashMap<>();
    private final Map<UUID, List<Quest>> personalQuests = new HashMap<>();
    private Quest currentDailyQuest;
    private long nextDailyQuestTime;

    // Quest generation templates
    private final List<QuestTemplate> questTemplates = new ArrayList<>();

    public QuestManager(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
        initializeQuestTemplates();
        loadQuests();
        startDailyQuestRotation();
    }

    private void initializeQuestTemplates() {
        // Personal quest templates
        questTemplates.add(new QuestTemplate(
            "kill_mobs_personal",
            Quest.QuestType.PERSONAL,
            "Slay the {target}",
            "Eliminate {amount} {target} to prove your combat prowess.",
            1, 50,
            Collections.singletonList(new QuestTemplate.ObjectiveTemplate(
                Quest.ObjectiveType.KILL_MOBS,
                new String[]{"ZOMBIE", "SKELETON", "SPIDER", "CREEPER", "ENDERMAN"},
                5, 15
            )),
            Collections.singletonList(new QuestTemplate.RewardTemplate(
                QuestReward.RewardType.EXPERIENCE, "", 100, 10.0
            ))
        ));

        questTemplates.add(new QuestTemplate(
            "mining_personal",
            Quest.QuestType.PERSONAL,
            "Deep Mining",
            "Mine {amount} {target} from the depths.",
            1, 30,
            Collections.singletonList(new QuestTemplate.ObjectiveTemplate(
                Quest.ObjectiveType.MINE_BLOCKS,
                new String[]{"IRON_ORE", "COAL_ORE", "COPPER_ORE", "GOLD_ORE", "DIAMOND_ORE"},
                10, 30
            )),
            Collections.singletonList(new QuestTemplate.RewardTemplate(
                QuestReward.RewardType.PROFESSION_XP, "mining", 200, 15.0
            ))
        ));
    }

    private void startDailyQuestRotation() {
        // Set next daily quest time to next midnight + random offset
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        nextDailyQuestTime = cal.getTimeInMillis();

        // Generate initial daily quest
        generateNewDailyQuest();

        // Start rotation task
        new BukkitRunnable() {
            @Override
            public void run() {
                if (System.currentTimeMillis() >= nextDailyQuestTime) {
                    generateNewDailyQuest();
                    // Set next rotation time (24 hours later)
                    nextDailyQuestTime += 24 * 60 * 60 * 1000;
                }
            }
        }.runTaskTimer(plugin, 0L, 20L * 60); // Check every minute
    }

    private void generateNewDailyQuest() {
        // Create a special daily quest template
        QuestTemplate dailyTemplate = new QuestTemplate(
            "daily_challenge",
            Quest.QuestType.DAILY,
            "Daily Challenge",
            "Complete today's special challenge for bonus rewards!",
            1, 100,
            Collections.singletonList(new QuestTemplate.ObjectiveTemplate(
                Quest.ObjectiveType.KILL_MOBS,
                new String[]{"ZOMBIE", "SKELETON", "SPIDER", "CREEPER"},
                20, 50
            )),
            Arrays.asList(
                new QuestTemplate.RewardTemplate(QuestReward.RewardType.EXPERIENCE, "", 500, 25.0),
                new QuestTemplate.RewardTemplate(QuestReward.RewardType.ITEM, "DIAMOND", 1, 0.1)
            )
        );

        currentDailyQuest = dailyTemplate.generateQuest(25); // Average level
        currentDailyQuest.setExpirationTime(nextDailyQuestTime);

        // Notify all online players
        for (Player player : plugin.getServer().getOnlinePlayers()) {
            player.sendMessage(ChatColor.GOLD + "🌟 A new daily quest is now available!");
            player.sendMessage(ChatColor.YELLOW + "Use /mychar quests to view it.");
        }
    }

    private void loadQuests() {
        sharedQuests.clear();

        FileConfiguration config = plugin.getConfigHub().getConfig("quests.yml");
        if (config == null) return;

        for (String questId : config.getKeys(false)) {
            ConfigurationSection section = config.getConfigurationSection(questId);
            if (section != null) {
                Quest quest = loadQuest(questId, section);
                if (quest != null) {
                    sharedQuests.put(questId, quest);
                }
            }
        }

        plugin.getLogger().info("Loaded " + sharedQuests.size() + " shared quests");
    }

    // Public API methods
    public List<Quest> getAvailableQuests(Player player) {
        List<Quest> available = new ArrayList<>();
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        if (data == null) return available;

        int playerLevel = data.getClassLevel();

        // Add shared quests
        for (Quest quest : sharedQuests.values()) {
            if (quest.canPlayerAccept(playerLevel) && !data.getCompletedQuests().contains(quest.getId())
                && !data.getActiveQuests().containsKey(quest.getId())) {
                available.add(quest);
            }
        }

        // Add daily quest
        if (currentDailyQuest != null && !currentDailyQuest.isExpired()
            && currentDailyQuest.canPlayerAccept(playerLevel)
            && !data.getCompletedQuests().contains(currentDailyQuest.getId())
            && !data.getActiveQuests().containsKey(currentDailyQuest.getId())) {
            available.add(currentDailyQuest);
        }

        // Generate personal quests if player doesn't have enough
        List<Quest> playerPersonalQuests = personalQuests.getOrDefault(player.getUniqueId(), new ArrayList<>());
        if (playerPersonalQuests.size() < 3) { // Max 3 personal quests per player
            generatePersonalQuests(player, 3 - playerPersonalQuests.size());
        }

        // Add personal quests
        for (Quest quest : personalQuests.getOrDefault(player.getUniqueId(), new ArrayList<>())) {
            if (!data.getCompletedQuests().contains(quest.getId())
                && !data.getActiveQuests().containsKey(quest.getId())) {
                available.add(quest);
            }
        }

        return available;
    }

    public Quest getDailyQuest() {
        return currentDailyQuest;
    }

    public long getDailyQuestTimeRemaining() {
        if (currentDailyQuest == null) return 0;
        return currentDailyQuest.getTimeRemaining();
    }

    public String getFormattedDailyQuestTimeRemaining() {
        if (currentDailyQuest == null) return "No daily quest";
        return currentDailyQuest.getFormattedTimeRemaining();
    }

    private void generatePersonalQuests(Player player, int count) {
        List<Quest> playerQuests = personalQuests.computeIfAbsent(player.getUniqueId(), k -> new ArrayList<>());
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        if (data == null) return;

        int playerLevel = data.getClassLevel();

        for (int i = 0; i < count; i++) {
            // Select random template for personal quests
            List<QuestTemplate> personalTemplates = questTemplates.stream()
                .filter(t -> t.getType() == Quest.QuestType.PERSONAL)
                .filter(t -> playerLevel >= t.getMinLevel() && playerLevel <= t.getMaxLevel())
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);

            if (!personalTemplates.isEmpty()) {
                QuestTemplate template = personalTemplates.get(ThreadLocalRandom.current().nextInt(personalTemplates.size()));
                Quest personalQuest = template.generateQuest(playerLevel);
                playerQuests.add(personalQuest);
            }
        }
    }

    private Quest loadQuest(String id, ConfigurationSection section) {
        try {
            String name = section.getString("name", id);
            String description = section.getString("description", "");
            int minLevel = section.getInt("min_level", 1);

            // Load objectives
            List<QuestObjective> objectives = new ArrayList<>();
            ConfigurationSection objSection = section.getConfigurationSection("objectives");
            if (objSection != null) {
                for (String objKey : objSection.getKeys(false)) {
                    ConfigurationSection objConfig = objSection.getConfigurationSection(objKey);
                    if (objConfig != null) {
                        QuestObjective objective = loadObjective(objKey, objConfig);
                        if (objective != null) {
                            objectives.add(objective);
                        }
                    }
                }
            }

            // Load rewards
            List<QuestReward> rewards = new ArrayList<>();
            ConfigurationSection rewardSection = section.getConfigurationSection("rewards");
            if (rewardSection != null) {
                for (String rewardKey : rewardSection.getKeys(false)) {
                    ConfigurationSection rewardConfig = rewardSection.getConfigurationSection(rewardKey);
                    if (rewardConfig != null) {
                        QuestReward reward = loadReward(rewardKey, rewardConfig);
                        if (reward != null) {
                            rewards.add(reward);
                        }
                    }
                }
            }

            return new Quest(id, name, description, minLevel, objectives, rewards);

        } catch (Exception e) {
            plugin.getLogger().warning("Failed to load quest " + id + ": " + e.getMessage());
            return null;
        }
    }

    private QuestObjective loadObjective(String id, ConfigurationSection section) {
        String type = section.getString("type");
        String target = section.getString("target");
        int amount = section.getInt("amount", 1);
        String description = section.getString("description", "");

        return new QuestObjective(id, type, target, amount, description);
    }

    private QuestReward loadReward(String id, ConfigurationSection section) {
        String type = section.getString("type");
        String target = section.getString("target");
        int amount = section.getInt("amount", 1);

        return new QuestReward(id, type, target, amount);
    }

    public Quest findQuest(String questId) {
        // Check shared quests
        Quest quest = sharedQuests.get(questId);
        if (quest != null) return quest;

        // Check daily quest
        if (currentDailyQuest != null && currentDailyQuest.getId().equals(questId)) {
            return currentDailyQuest;
        }

        // Check personal quests
        for (List<Quest> playerQuests : personalQuests.values()) {
            for (Quest personalQuest : playerQuests) {
                if (personalQuest.getId().equals(questId)) {
                    return personalQuest;
                }
            }
        }

        return null;
    }

    public boolean startQuest(Player player, String questId) {
        Quest quest = findQuest(questId);
        if (quest == null) {
            player.sendMessage(ChatColor.RED + "Quest not found!");
            return false;
        }

        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);

        // Check if already completed
        if (data.getCompletedQuests().contains(questId)) {
            player.sendMessage(ChatColor.RED + "You have already completed this quest!");
            return false;
        }

        // Check if already active
        if (data.getActiveQuests().containsKey(questId)) {
            player.sendMessage(ChatColor.RED + "This quest is already active!");
            return false;
        }

        // Check level requirement
        if (data.getClassLevel() < quest.getMinLevel()) {
            player.sendMessage(ChatColor.RED + "You need to be level " + quest.getMinLevel() + " to start this quest!");
            return false;
        }

        // Start quest
        QuestProgress progress = new QuestProgress(questId);
        data.getActiveQuests().put(questId, progress);

        player.sendMessage(ChatColor.GREEN + "Quest Started: " + quest.getName());
        player.sendMessage(ChatColor.GRAY + quest.getDescription());
        player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.2f);

        return true;
    }

    @EventHandler
    public void onEntityDeath(EntityDeathEvent event) {
        if (event.getEntity().getKiller() instanceof Player) {
            Player player = (Player) event.getEntity().getKiller();
            updateQuestProgress(player, "kill", event.getEntityType().name().toLowerCase(), 1);
        }
    }

    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        Player player = event.getPlayer();
        updateQuestProgress(player, "mine", event.getBlock().getType().name().toLowerCase(), 1);
    }

    @EventHandler
    public void onPlayerFish(PlayerFishEvent event) {
        if (event.getState() == PlayerFishEvent.State.CAUGHT_FISH) {
            Player player = event.getPlayer();
            updateQuestProgress(player, "fish", "fish", 1);
        }
    }

    private void updateQuestProgress(Player player, String type, String target, int amount) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);

        for (Map.Entry<String, QuestProgress> entry : data.getActiveQuests().entrySet()) {
            String questId = entry.getKey();
            QuestProgress progress = entry.getValue();
            Quest quest = findQuest(questId);

            if (quest != null) {
                for (QuestObjective objective : quest.getObjectives()) {
                    if (objective.getType().equals(type) &&
                        (objective.getTarget().equals(target) || objective.getTarget().equals("any"))) {

                        int currentProgress = progress.getObjectiveProgress(objective.getId());
                        int newProgress = Math.min(currentProgress + amount, objective.getAmount());
                        progress.setObjectiveProgress(objective.getId(), newProgress);

                        // Check if objective completed
                        if (newProgress >= objective.getAmount() && currentProgress < objective.getAmount()) {
                            player.sendMessage(ChatColor.YELLOW + "Objective Complete: " + objective.getDescription());
                            player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.5f);
                        }

                        // Check if quest completed
                        if (isQuestComplete(quest, progress)) {
                            completeQuest(player, questId);
                        }
                    }
                }
            }
        }
    }

    private boolean isQuestComplete(Quest quest, QuestProgress progress) {
        for (QuestObjective objective : quest.getObjectives()) {
            int currentProgress = progress.getObjectiveProgress(objective.getId());
            if (currentProgress < objective.getAmount()) {
                return false;
            }
        }
        return true;
    }

    private void completeQuest(Player player, String questId) {
        Quest quest = findQuest(questId);
        if (quest == null) return;

        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);

        // Remove from active quests
        data.getActiveQuests().remove(questId);

        // Add to completed quests
        data.getCompletedQuests().add(questId);

        // Give rewards
        for (QuestReward reward : quest.getRewards()) {
            giveReward(player, reward);
        }

        // Fire event
        OrvynQuestCompleteEvent event = new OrvynQuestCompleteEvent(player, questId);
        plugin.getServer().getPluginManager().callEvent(event);

        // Notification
        player.sendMessage(ChatColor.GOLD + "✦ Quest Complete: " + quest.getName());
        player.playSound(player.getLocation(), Sound.UI_TOAST_CHALLENGE_COMPLETE, 1.0f, 1.0f);
    }

    private void giveReward(Player player, QuestReward reward) {
        switch (reward.getType()) {
            case "exp":
                PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
                data.addClassExp(reward.getAmount());
                player.sendMessage(ChatColor.GREEN + "+" + reward.getAmount() + " Experience");
                break;
            case "item":
                Material material = Material.valueOf(reward.getTarget().toUpperCase());
                ItemStack item = new ItemStack(material, reward.getAmount());
                player.getInventory().addItem(item);
                player.sendMessage(ChatColor.GREEN + "Received: " + reward.getAmount() + "x " + material.name());
                break;
            case "money":
                // Could integrate with economy plugin
                player.sendMessage(ChatColor.GREEN + "Received: " + reward.getAmount() + " coins");
                break;
        }
    }

    public Set<String> getQuestIds() {
        Set<String> allIds = new HashSet<>(sharedQuests.keySet());
        if (currentDailyQuest != null) {
            allIds.add(currentDailyQuest.getId());
        }
        for (List<Quest> playerQuests : personalQuests.values()) {
            for (Quest quest : playerQuests) {
                allIds.add(quest.getId());
            }
        }
        return allIds;
    }

    public boolean abandonQuest(Player player, String questId) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player.getUniqueId());
        if (data == null) return false;

        if (!data.getActiveQuests().containsKey(questId)) {
            player.sendMessage(ChatColor.RED + "You are not currently on this quest!");
            return false;
        }

        Quest quest = findQuest(questId);
        if (quest == null) {
            player.sendMessage(ChatColor.RED + "Quest not found!");
            return false;
        }

        // Remove from active quests
        data.getActiveQuests().remove(questId);

        player.sendMessage(ChatColor.YELLOW + "Abandoned quest: " + ChatColor.WHITE + quest.getName());
        return true;
    }

    public Quest getQuest(String id) {
        return findQuest(id);
    }

    public void reload() {
        loadQuests();
    }

    public void shutdown() {
        // Cleanup if needed
    }
}
