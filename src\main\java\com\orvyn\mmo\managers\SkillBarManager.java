package com.orvyn.mmo.managers;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerItemHeldEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.event.player.PlayerToggleSneakEvent;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scoreboard.Criteria;
import org.bukkit.scoreboard.DisplaySlot;
import org.bukkit.scoreboard.Objective;
import org.bukkit.scoreboard.Score;
import org.bukkit.scoreboard.Scoreboard;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class SkillBarManager implements Listener {

    private final OrvynMMOPlugin plugin;
    private final Map<UUID, Boolean> fKeyPressed = new HashMap<>();
    private final Map<UUID, Integer> lastHeldSlot = new HashMap<>();
    private final Map<UUID, Scoreboard> playerSkillBoards = new HashMap<>();
    private BukkitRunnable skillBarUpdateTask;

    public SkillBarManager(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
        startSkillBarUpdateTask();
    }

    private void startSkillBarUpdateTask() {
        skillBarUpdateTask = new BukkitRunnable() {
            @Override
            public void run() {
                for (Player player : Bukkit.getOnlinePlayers()) {
                    updateSkillBarDisplay(player);
                }
            }
        };
        skillBarUpdateTask.runTaskTimer(plugin, 0L, 10L); // Update every 0.5 seconds
    }

    @EventHandler
    public void onPlayerSneak(PlayerToggleSneakEvent event) {
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();
        
        if (event.isSneaking()) {
            // F key pressed (using sneak as F key substitute)
            fKeyPressed.put(playerId, true);
            player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 0.5f, 1.5f);
            
            // Show skill bar immediately
            updateSkillBarDisplay(player);
        } else {
            // F key released
            fKeyPressed.remove(playerId);
        }
    }

    @EventHandler
    public void onPlayerItemHeld(PlayerItemHeldEvent event) {
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();

        // Check if F key is pressed
        if (fKeyPressed.getOrDefault(playerId, false)) {
            int hotbarSlot = event.getNewSlot(); // 0-8
            int skillSlot = hotbarSlot + 1; // Convert to 1-9, but we only use 1-5

            if (skillSlot >= 1 && skillSlot <= 5) {
                // Activate skill in this slot
                activateSkillInSlot(player, skillSlot);
                event.setCancelled(true); // Prevent item switching

                // Reset to previous slot after a short delay to avoid conflicts
                int previousSlot = lastHeldSlot.getOrDefault(playerId, 0);
                plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                    if (player.isOnline()) {
                        player.getInventory().setHeldItemSlot(previousSlot);
                    }
                }, 1L);
            }
        }

        lastHeldSlot.put(playerId, event.getNewSlot());
    }

    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        UUID playerId = event.getPlayer().getUniqueId();

        // Clean up scoreboard
        Scoreboard skillBoard = playerSkillBoards.remove(playerId);
        if (skillBoard != null) {
            // Reset player's scoreboard to default
            event.getPlayer().setScoreboard(Bukkit.getScoreboardManager().getMainScoreboard());
        }

        // Clean up other data
        fKeyPressed.remove(playerId);
        lastHeldSlot.remove(playerId);
    }

    private void activateSkillInSlot(Player player, int slot) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        if (data == null) return;

        String skillId = data.getSkillBarSlot(slot);

        if (skillId == null || skillId.isEmpty()) {
            player.sendMessage(ChatColor.RED + "No skill equipped in slot " + slot + "!");
            player.playSound(player.getLocation(), Sound.BLOCK_NOTE_BLOCK_BASS, 1.0f, 0.5f);
            return;
        }

        // Use the skill through SkillManager - it will handle all checks
        boolean success = plugin.getSkillManager().useSkill(player, skillId);
        if (success) {
            player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.2f);
        } else {
            player.playSound(player.getLocation(), Sound.BLOCK_NOTE_BLOCK_BASS, 1.0f, 0.5f);
        }
    }

    private void updateSkillBarDisplay(Player player) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        if (data == null) return;

        UUID playerId = player.getUniqueId();
        boolean fPressed = fKeyPressed.getOrDefault(playerId, false);

        // Check if we should show the skill bar
        boolean shouldShow = fPressed || hasEquippedSkills(data);

        if (shouldShow) {
            // Get or create scoreboard for this player
            Scoreboard skillBoard = playerSkillBoards.get(playerId);
            if (skillBoard == null) {
                skillBoard = createSkillBoardForPlayer(player);
                playerSkillBoards.put(playerId, skillBoard);
            }

            // Update scoreboard content
            updateSkillBoardContent(skillBoard, data, fPressed);

            // Set the scoreboard for the player
            player.setScoreboard(skillBoard);
        } else {
            // Hide skill bar by resetting to main scoreboard
            Scoreboard skillBoard = playerSkillBoards.remove(playerId);
            if (skillBoard != null) {
                player.setScoreboard(Bukkit.getScoreboardManager().getMainScoreboard());
            }
        }
    }

    private boolean hasEquippedSkills(PlayerData data) {
        for (int i = 1; i <= 5; i++) {
            if (data.getSkillBarSlot(i) != null) {
                return true;
            }
        }
        return false;
    }

    private Scoreboard createSkillBoardForPlayer(Player player) {
        Scoreboard scoreboard = Bukkit.getScoreboardManager().getNewScoreboard();
        Objective objective = scoreboard.registerNewObjective("skillbar", Criteria.DUMMY,
            ChatColor.GOLD + "⚔ Skill Bar ⚔");
        objective.setDisplaySlot(DisplaySlot.SIDEBAR);
        return scoreboard;
    }

    private void updateSkillBoardContent(Scoreboard scoreboard, PlayerData data, boolean fPressed) {
        Objective objective = scoreboard.getObjective("skillbar");
        if (objective == null) return;

        // Clear existing scores
        for (String entry : scoreboard.getEntries()) {
            scoreboard.resetScores(entry);
        }

        // Add skill slots (in reverse order since scoreboard displays bottom-to-top)
        for (int i = 5; i >= 1; i--) {
            String skillId = data.getSkillBarSlot(i);
            String displayText = formatSkillSlotForSidebar(i, skillId, data, fPressed);

            Score score = objective.getScore(displayText);
            score.setScore(i); // Higher scores appear at the top
        }

        // Add header if F is pressed
        if (fPressed) {
            Score header = objective.getScore(ChatColor.YELLOW + "⚡ ACTIVE ⚡");
            header.setScore(6);
        }
    }

    private String formatSkillSlotForSidebar(int slot, String skillId, PlayerData data, boolean fPressed) {
        StringBuilder text = new StringBuilder();

        // Slot number
        text.append(ChatColor.WHITE).append("[").append(slot).append("] ");

        if (skillId == null || skillId.isEmpty()) {
            // Empty slot
            text.append(ChatColor.GRAY).append("Empty");
            if (fPressed) {
                text.append(ChatColor.DARK_GRAY).append(" (Press ").append(slot).append(")");
            }
        } else {
            String skillName = formatSkillName(skillId);

            if (!data.getUnlockedSkills().contains(skillId)) {
                // Skill not unlocked
                text.append(ChatColor.DARK_GRAY).append(skillName).append(" (Locked)");
            } else if (data.isSkillOnCooldown(skillId)) {
                // Skill on cooldown
                long remainingSec = data.getSkillCooldownRemaining(skillId) / 1000;
                text.append(ChatColor.RED).append(skillName).append(" (").append(remainingSec).append("s)");
            } else {
                // Check mana cost
                int manaCost = getSkillManaCost(skillId);
                if (data.getCurrentMana() < manaCost) {
                    // Not enough mana
                    text.append(ChatColor.BLUE).append(skillName).append(" (").append(manaCost).append(" mana)");
                } else {
                    // Ready to use
                    text.append(ChatColor.GREEN).append(skillName);
                    if (fPressed) {
                        text.append(ChatColor.YELLOW).append(" ✓");
                    }
                }
            }
        }

        return text.toString();
    }



    private String formatSkillName(String skillId) {
        String[] words = skillId.replace("_", " ").toLowerCase().split(" ");
        StringBuilder result = new StringBuilder();
        for (String word : words) {
            if (result.length() > 0) result.append(" ");
            if (word.length() > 0) {
                result.append(Character.toUpperCase(word.charAt(0)));
                if (word.length() > 1) {
                    result.append(word.substring(1));
                }
            }
        }
        return result.toString();
    }

    private int getSkillManaCost(String skillId) {
        // Basic mana costs for skills
        switch (skillId.toLowerCase()) {
            case "fireball":
            case "ice_blast":
            case "lightning_bolt":
                return 20;
            case "teleport":
                return 30;
            case "charge":
            case "shield_bash":
                return 15;
            case "battle_cry":
            case "whirlwind":
                return 25;
            case "power_shot":
            case "multi_shot":
                return 10;
            case "explosive_arrow":
                return 20;
            case "eagle_eye":
                return 15;
            default:
                return 10;
        }
    }

    private int getSkillCooldown(String skillId) {
        // Basic cooldowns for skills (in seconds)
        switch (skillId.toLowerCase()) {
            case "fireball":
            case "ice_blast":
                return 3;
            case "lightning_bolt":
                return 5;
            case "teleport":
                return 10;
            case "charge":
                return 8;
            case "shield_bash":
                return 6;
            case "battle_cry":
                return 30;
            case "whirlwind":
                return 12;
            case "power_shot":
                return 2;
            case "multi_shot":
                return 4;
            case "explosive_arrow":
                return 8;
            case "eagle_eye":
                return 15;
            default:
                return 5;
        }
    }

    public void shutdown() {
        if (skillBarUpdateTask != null) {
            skillBarUpdateTask.cancel();
        }

        // Reset all players to main scoreboard
        for (UUID playerId : playerSkillBoards.keySet()) {
            Player player = Bukkit.getPlayer(playerId);
            if (player != null && player.isOnline()) {
                player.setScoreboard(Bukkit.getScoreboardManager().getMainScoreboard());
            }
        }

        playerSkillBoards.clear();
        fKeyPressed.clear();
        lastHeldSlot.clear();
    }
}
