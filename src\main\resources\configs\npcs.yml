# NPCs Configuration
# Define interactive NPCs with dialogue, types, and locations

# Global settings for all NPCs
global_settings:
  dialogue_delay: 2000          # Milliseconds between dialogue messages
  interaction_cooldown: 1000    # Milliseconds between interactions per player
  max_interaction_distance: 5.0 # Maximum distance for interaction
  despawn_distance: 100.0       # Distance at which NPCs despawn when no players nearby
  respawn_time: 300000          # Milliseconds before respawning despawned NPCs

tutorial_guide:
  type: "tutorial"
  entity_type: "VILLAGER"
  name: "§6Tutorial Guide"
  location:
    world: "world"
    x: 0.5
    y: 64.0
    z: 0.5
    yaw: 0.0
    pitch: 0.0
  dialogue:
    - "§eWelcome to <PERSON><PERSON><PERSON><PERSON>, brave adventurer!"
    - "§7I am here to guide you through your first steps."
    - "§aFirst, you must choose your class to begin your journey."
    - "§bClick on me again to open the class selection menu!"
  profession: "FARMER"
  glow: true
  persistent: true

class_trainer_warrior:
  type: "class_trainer"
  entity_type: "VILLAGER"
  name: "§cWarrior Trainer"
  location:
    world: "world"
    x: 10.5
    y: 64.0
    z: 0.5
    yaw: 270.0
    pitch: 0.0
  dialogue:
    - "§cGreetings, warrior!"
    - "§7I can teach you the ways of combat and strength."
    - "§aWould you like to learn more about warrior skills?"
    - "§bTrain hard, and you shall become mighty!"
  profession: "WEAPONSMITH"
  class_filter: "warrior"
  glow: true
  persistent: true

class_trainer_mage:
  type: "class_trainer"
  entity_type: "VILLAGER"
  name: "§9Mage Trainer"
  location:
    world: "world"
    x: -10.5
    y: 64.0
    z: 0.5
    yaw: 90.0
    pitch: 0.0
  dialogue:
    - "§9Welcome, student of the arcane!"
    - "§7Magic flows through all things, learn to harness it."
    - "§aI can teach you powerful spells and enchantments."
    - "§bStudy well, and the mysteries of magic shall be yours!"
  profession: "LIBRARIAN"
  class_filter: "mage"
  glow: true
  persistent: true

class_trainer_archer:
  type: "class_trainer"
  entity_type: "VILLAGER"
  name: "§2Archer Trainer"
  location:
    world: "world"
    x: 0.5
    y: 64.0
    z: 10.5
    yaw: 180.0
    pitch: 0.0
  dialogue:
    - "§2Greetings, ranger!"
    - "§7The bow is an extension of your will and precision."
    - "§aI can teach you the art of marksmanship and agility."
    - "§bPractice your aim, and no target shall escape you!"
  profession: "FLETCHER"
  class_filter: "archer"
  glow: true
  persistent: true

quest_giver_village:
  type: "quest_giver"
  entity_type: "VILLAGER"
  name: "§eVillage Elder"
  location:
    world: "world"
    x: 20.5
    y: 64.0
    z: 20.5
    yaw: 225.0
    pitch: 0.0
  dialogue:
    - "§eHello there, adventurer!"
    - "§7Our village has been troubled by monsters lately."
    - "§aWould you be willing to help us with some tasks?"
    - "§bCheck your quest log for available missions!"
  profession: "NITWIT"
  available_quests:
    - "kill_10_zombies"
    - "collect_10_wheat"
  glow: true
  persistent: true

profession_trainer_mining:
  type: "profession_trainer"
  entity_type: "VILLAGER"
  name: "§8Master Miner"
  location:
    world: "world"
    x: -20.5
    y: 64.0
    z: 20.5
    yaw: 45.0
    pitch: 0.0
  dialogue:
    - "§8Ho there, future miner!"
    - "§7The earth holds many treasures for those who know where to look."
    - "§aI can teach you advanced mining techniques."
    - "§bDig deep, and fortune shall be yours!"
  profession: "TOOLSMITH"
  profession_filter: "mining"
  glow: true
  persistent: true

profession_trainer_fishing:
  type: "profession_trainer"
  entity_type: "VILLAGER"
  name: "§bMaster Angler"
  location:
    world: "world"
    x: -20.5
    y: 64.0
    z: -20.5
    yaw: 135.0
    pitch: 0.0
  dialogue:
    - "§bAhoy there, aspiring angler!"
    - "§7The waters hold secrets and sustenance for the patient."
    - "§aI can teach you the finest fishing techniques."
    - "§bCast your line with skill, and the sea shall provide!"
  profession: "FISHERMAN"
  profession_filter: "fishing"
  glow: true
  persistent: true

waypoint_guide:
  type: "waypoint_guide"
  entity_type: "VILLAGER"
  name: "§dWaypoint Guide"
  location:
    world: "world"
    x: 20.5
    y: 64.0
    z: -20.5
    yaw: 315.0
    pitch: 0.0
  dialogue:
    - "§dGreetings, traveler!"
    - "§7I know the paths and passages of this realm."
    - "§aI can teach you about waypoints and fast travel."
    - "§bExplore the world, and discover new destinations!"
  profession: "CARTOGRAPHER"
  glow: true
  persistent: true

# Global NPC settings
global_settings:
  dialogue_delay: 2000  # 2 seconds between dialogue lines
  interaction_cooldown: 1000  # 1 second cooldown between interactions
  max_interaction_distance: 5.0  # Maximum distance to interact
  despawn_distance: 100.0  # Despawn if no players within 100 blocks
  respawn_time: 300000  # 5 minutes respawn time if despawned
