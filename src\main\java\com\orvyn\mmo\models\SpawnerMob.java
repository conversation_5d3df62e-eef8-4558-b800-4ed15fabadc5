package com.orvyn.mmo.models;

import org.bukkit.entity.EntityType;

import java.util.List;

public class SpawnerMob {
    
    private final EntityType type;
    private final String name;
    private final int level;
    private final double spawnChance;
    private final int maxCount;
    private final List<CustomDrop> customDrops;
    
    public SpawnerMob(EntityType type, String name, int level, double spawnChance, int maxCount, List<CustomDrop> customDrops) {
        this.type = type;
        this.name = name;
        this.level = level;
        this.spawnChance = spawnChance;
        this.maxCount = maxCount;
        this.customDrops = customDrops;
    }
    
    public boolean shouldSpawn() {
        return Math.random() < spawnChance;
    }
    
    // Getters
    public EntityType getType() { return type; }
    public String getName() { return name; }
    public int getLevel() { return level; }
    public double getSpawnChance() { return spawnChance; }
    public int getMaxCount() { return maxCount; }
    public List<CustomDrop> getCustomDrops() { return customDrops; }
    
    public static class CustomDrop {
        private final String item;
        private final double chance;
        private final int amount;
        private final String name;
        
        public CustomDrop(String item, double chance, int amount, String name) {
            this.item = item;
            this.chance = chance;
            this.amount = amount;
            this.name = name;
        }
        
        public boolean shouldDrop() {
            return Math.random() < chance;
        }
        
        // Getters
        public String getItem() { return item; }
        public double getChance() { return chance; }
        public int getAmount() { return amount; }
        public String getName() { return name; }
    }
}
