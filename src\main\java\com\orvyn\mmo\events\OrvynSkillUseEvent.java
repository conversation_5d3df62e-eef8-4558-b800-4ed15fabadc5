package com.orvyn.mmo.events;

import com.orvyn.mmo.models.Skill;
import org.bukkit.entity.Player;
import org.bukkit.event.Cancellable;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

public class OrvynSkillUseEvent extends Event implements Cancellable {
    
    private static final HandlerList handlers = new HandlerList();
    private boolean cancelled = false;
    
    private final Player player;
    private final Skill skill;
    
    public OrvynSkillUseEvent(Player player, Skill skill) {
        this.player = player;
        this.skill = skill;
    }
    
    public Player getPlayer() {
        return player;
    }
    
    public Skill getSkill() {
        return skill;
    }
    
    @Override
    public boolean isCancelled() {
        return cancelled;
    }
    
    @Override
    public void setCancelled(boolean cancelled) {
        this.cancelled = cancelled;
    }
    
    @Override
    public HandlerList getHandlers() {
        return handlers;
    }
    
    public static HandlerList getHandlerList() {
        return handlers;
    }
}
