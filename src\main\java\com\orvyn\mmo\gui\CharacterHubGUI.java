package com.orvyn.mmo.gui;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import java.util.ArrayList;
import java.util.List;

public class CharacterHubGUI implements Listener {

    private final OrvynMMOPlugin plugin;
    private final Player player;
    private final PlayerData data;
    private static final Map<UUID, CharacterHubGUI> activeGUIs = new HashMap<>();

    public CharacterHubGUI(OrvynMMOPlugin plugin, Player player) {
        this.plugin = plugin;
        this.player = player;
        this.data = plugin.getPlayerDataManager().getPlayerData(player);

        // Unregister any existing GUI for this player
        CharacterHubGUI existing = activeGUIs.get(player.getUniqueId());
        if (existing != null) {
            HandlerList.unregisterAll(existing);
        }

        // Register this as a listener and track it
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
        activeGUIs.put(player.getUniqueId(), this);
    }
    
    public void open() {
        Inventory gui = Bukkit.createInventory(null, 54, ChatColor.DARK_BLUE + "Character Hub - " + player.getName());
        
        // Character Stats
        ItemStack stats = createItem(Material.DIAMOND_SWORD,
            ChatColor.AQUA + "Character Stats",
            ChatColor.GRAY + "View and allocate attribute points",
            "",
            ChatColor.YELLOW + "Level: " + data.getClassLevel(),
            ChatColor.YELLOW + "Class: " + capitalizeFirst(data.getPlayerClass()),
            ChatColor.YELLOW + "Experience: " + data.getClassExp(),
            "",
            ChatColor.GREEN + "Click to manage stats"
        );
        gui.setItem(10, stats);

        // Skills
        ItemStack skills = createItem(Material.ENCHANTED_BOOK,
            ChatColor.DARK_PURPLE + "Skills",
            ChatColor.GRAY + "Unlock and manage your skills",
            "",
            ChatColor.YELLOW + "Unlocked Skills: " + data.getUnlockedSkills().size(),
            ChatColor.YELLOW + "Class: " + capitalizeFirst(data.getPlayerClass()),
            "",
            ChatColor.GREEN + "Click to view skill tree"
        );
        gui.setItem(12, skills);
        
        // Skill Bar
        ItemStack skillBar = createItem(Material.BLAZE_POWDER,
            ChatColor.RED + "Skill Bar",
            ChatColor.GRAY + "Configure your skill hotkeys",
            "",
            ChatColor.YELLOW + "Equipped Skills: " + getEquippedSkillBarSkills(),
            ChatColor.GRAY + "Use Sneak + [1-5] to activate skills",
            "",
            ChatColor.GREEN + "Click to configure skill bar"
        );
        gui.setItem(14, skillBar);
        
        // Professions
        ItemStack professions = createItem(Material.IRON_PICKAXE,
            ChatColor.GOLD + "Professions",
            ChatColor.GRAY + "View your profession levels",
            "",
            ChatColor.YELLOW + "Mining: " + data.getProfessionLevel("mining"),
            ChatColor.YELLOW + "Woodcutting: " + data.getProfessionLevel("woodcutting"),
            ChatColor.YELLOW + "Fishing: " + data.getProfessionLevel("fishing"),
            ChatColor.YELLOW + "Farming: " + data.getProfessionLevel("farming"),
            "",
            ChatColor.GREEN + "Click to view professions"
        );
        gui.setItem(16, professions);
        
        // Quests
        ItemStack quests = createItem(Material.WRITABLE_BOOK,
            ChatColor.YELLOW + "Quests",
            ChatColor.GRAY + "View and manage your quests",
            "",
            ChatColor.YELLOW + "Active Quests: " + data.getActiveQuests().size(),
            ChatColor.YELLOW + "Completed Quests: " + data.getCompletedQuests().size(),
            "",
            ChatColor.GREEN + "Click to view quests"
        );
        gui.setItem(28, quests);
        
        // Equipment
        ItemStack equipment = createItem(Material.DIAMOND_CHESTPLATE,
            ChatColor.LIGHT_PURPLE + "Equipment",
            ChatColor.GRAY + "View your equipped items",
            "",
            ChatColor.YELLOW + "Armor Rating: " + calculateArmorRating(),
            ChatColor.YELLOW + "Weapon Damage: " + calculateWeaponDamage(),
            "",
            ChatColor.GREEN + "Click to view equipment"
        );
        gui.setItem(30, equipment);
        
        // Party
        ItemStack party = createItem(Material.PLAYER_HEAD,
            ChatColor.GREEN + "Party",
            ChatColor.GRAY + "Create and manage your party",
            "",
            ChatColor.YELLOW + "Party Status: " + (data.getPartyId() != null ? "In Party" : "No Party"),
            "",
            ChatColor.GREEN + "Click to manage party"
        );
        gui.setItem(32, party);
        
        // Close button
        ItemStack close = createItem(Material.BARRIER,
            ChatColor.RED + "Close",
            ChatColor.GRAY + "Close this menu"
        );
        gui.setItem(49, close);
        
        player.openInventory(gui);
    }
    
    public void openSection(String section) {
        switch (section.toLowerCase()) {
            case "stats":
                openStatsSection();
                break;
            case "skills":
                openSkillsSection();
                break;
            case "skillbar":
                openSkillBarSection();
                break;
            case "professions":
                openProfessionsSection();
                break;
            case "quests":
                openQuestsSection();
                break;
            case "equipment":
                openEquipmentSection();
                break;
            case "party":
                openPartySection();
                break;
            default:
                open();
                break;
        }
    }
    
    private void openStatsSection() {
        PlayerManagementGUI gui = new PlayerManagementGUI(plugin, player);
        gui.openStatsAllocation();
    }
    
    private void openSkillsSection() {
        PlayerManagementGUI gui = new PlayerManagementGUI(plugin, player);
        gui.openSkillTree();
    }
    
    private void openSkillBarSection() {
        SkillBarManagementGUI gui = new SkillBarManagementGUI(plugin);
        gui.openSkillBarGUI(player);
    }
    
    private void openProfessionsSection() {
        // Create a simple professions display
        Inventory gui = Bukkit.createInventory(null, 27, ChatColor.GOLD + "Professions");
        
        // Mining
        ItemStack mining = createItem(Material.IRON_PICKAXE,
            ChatColor.GRAY + "Mining",
            ChatColor.YELLOW + "Level: " + data.getProfessionLevel("mining"),
            ChatColor.YELLOW + "XP: " + data.getProfessionExp("mining") + "/" + (data.getProfessionLevel("mining") + 1) * 50
        );
        gui.setItem(10, mining);
        
        // Woodcutting
        ItemStack woodcutting = createItem(Material.IRON_AXE,
            ChatColor.GREEN + "Woodcutting",
            ChatColor.YELLOW + "Level: " + data.getProfessionLevel("woodcutting"),
            ChatColor.YELLOW + "XP: " + data.getProfessionExp("woodcutting") + "/" + (data.getProfessionLevel("woodcutting") + 1) * 50
        );
        gui.setItem(12, woodcutting);
        
        // Fishing
        ItemStack fishing = createItem(Material.FISHING_ROD,
            ChatColor.BLUE + "Fishing",
            ChatColor.YELLOW + "Level: " + data.getProfessionLevel("fishing"),
            ChatColor.YELLOW + "XP: " + data.getProfessionExp("fishing") + "/" + (data.getProfessionLevel("fishing") + 1) * 50
        );
        gui.setItem(14, fishing);
        
        // Farming
        ItemStack farming = createItem(Material.IRON_HOE,
            ChatColor.YELLOW + "Farming",
            ChatColor.YELLOW + "Level: " + data.getProfessionLevel("farming"),
            ChatColor.YELLOW + "XP: " + data.getProfessionExp("farming") + "/" + (data.getProfessionLevel("farming") + 1) * 50
        );
        gui.setItem(16, farming);
        
        // Back button
        ItemStack back = createItem(Material.ARROW,
            ChatColor.YELLOW + "Back",
            ChatColor.GRAY + "Return to character hub"
        );
        gui.setItem(22, back);
        
        player.openInventory(gui);
    }
    
    private void openQuestsSection() {
        QuestGUI questGUI = new QuestGUI(plugin, player);
        questGUI.openQuestHub();
    }
    
    private void openEquipmentSection() {
        EquipmentGUI equipmentGUI = new EquipmentGUI(plugin, player);
        equipmentGUI.openEquipmentHub();
    }
    
    private void openPartySection() {
        PartyManagementGUI gui = new PartyManagementGUI(plugin);
        gui.openPartyGUI(player);
    }
    
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        Player clickedPlayer = (Player) event.getWhoClicked();
        
        if (!clickedPlayer.equals(player)) return;
        
        String title = event.getView().getTitle();
        if (!title.startsWith(ChatColor.DARK_BLUE + "Character Hub") && 
            !title.equals(ChatColor.GOLD + "Professions")) return;
        
        event.setCancelled(true);
        
        ItemStack clicked = event.getCurrentItem();
        if (clicked == null || clicked.getType() == Material.AIR) return;
        
        int slot = event.getSlot();
        
        // Handle character hub clicks
        if (title.startsWith(ChatColor.DARK_BLUE + "Character Hub")) {
            handleCharacterHubClick(slot);
        } else if (title.equals(ChatColor.GOLD + "Professions")) {
            handleProfessionsClick(slot);
        }

        player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
    }

    @EventHandler
    public void onInventoryClose(org.bukkit.event.inventory.InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) return;
        Player closingPlayer = (Player) event.getPlayer();

        if (closingPlayer.equals(player)) {
            // Unregister this GUI when the player closes any inventory
            HandlerList.unregisterAll(this);
            activeGUIs.remove(player.getUniqueId());
        }
    }

    public static void cleanupAllGUIs() {
        for (CharacterHubGUI gui : activeGUIs.values()) {
            HandlerList.unregisterAll(gui);
        }
        activeGUIs.clear();
    }
    
    private void handleCharacterHubClick(int slot) {
        switch (slot) {
            case 10: // Stats
                openStatsSection();
                break;
            case 12: // Skills
                openSkillsSection();
                break;
            case 14: // Skill Bar
                openSkillBarSection();
                break;
            case 16: // Professions
                openProfessionsSection();
                break;
            case 28: // Quests
                openQuestsSection();
                break;
            case 30: // Equipment
                openEquipmentSection();
                break;
            case 32: // Party
                openPartySection();
                break;
            case 49: // Close
                player.closeInventory();
                break;
        }
    }
    
    private void handleProfessionsClick(int slot) {
        if (slot == 22) { // Back button
            open();
        }
    }
    
    private ItemStack createItem(Material material, String name, String... lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(name);
        
        List<String> loreList = new ArrayList<>();
        for (String line : lore) {
            loreList.add(line);
        }
        meta.setLore(loreList);
        
        item.setItemMeta(meta);
        return item;
    }
    
    private String getEquippedSkillBarSkills() {
        int count = 0;
        for (int i = 1; i <= 5; i++) {
            if (data.getSkillBarSlot(i) != null) {
                count++;
            }
        }
        return count + "/5";
    }
    
    private String calculateArmorRating() {
        // TODO: Implement armor calculation
        return "N/A";
    }
    
    private String calculateWeaponDamage() {
        // TODO: Implement weapon damage calculation
        return "N/A";
    }
    
    private String capitalizeFirst(String str) {
        if (str == null || str.isEmpty()) return str;
        return str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase();
    }
}
