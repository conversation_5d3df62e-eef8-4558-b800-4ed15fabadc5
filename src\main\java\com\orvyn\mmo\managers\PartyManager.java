package com.orvyn.mmo.managers;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import com.orvyn.mmo.models.Party;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;

import java.util.*;

public class PartyManager {

    private final OrvynMMOPlugin plugin;
    private final Map<UUID, Party> parties = new HashMap<>();
    private final Map<UUID, UUID> playerToParty = new HashMap<>();

    public PartyManager(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
    }

    public boolean createParty(Player leader) {
        if (playerToParty.containsKey(leader.getUniqueId())) {
            leader.sendMessage(ChatColor.RED + "You are already in a party!");
            return false;
        }

        UUID partyId = UUID.randomUUID();
        Party party = new Party(partyId, leader.getUniqueId());
        parties.put(partyId, party);
        playerToParty.put(leader.getUniqueId(), partyId);

        PlayerData data = plugin.getPlayerDataManager().getPlayerData(leader);
        data.setPartyId(partyId);

        leader.sendMessage(ChatColor.GREEN + "Party created! You are the leader.");
        return true;
    }

    public boolean invitePlayer(Player leader, Player target) {
        UUID partyId = playerToParty.get(leader.getUniqueId());
        if (partyId == null) {
            leader.sendMessage(ChatColor.RED + "You are not in a party!");
            return false;
        }

        Party party = parties.get(partyId);
        if (!party.getLeader().equals(leader.getUniqueId())) {
            leader.sendMessage(ChatColor.RED + "Only the party leader can invite players!");
            return false;
        }

        if (playerToParty.containsKey(target.getUniqueId())) {
            leader.sendMessage(ChatColor.RED + target.getName() + " is already in a party!");
            return false;
        }

        if (party.getMembers().size() >= 5) {
            leader.sendMessage(ChatColor.RED + "Party is full! (Max 5 members)");
            return false;
        }

        // Add to party
        party.addMember(target.getUniqueId());
        playerToParty.put(target.getUniqueId(), partyId);

        PlayerData targetData = plugin.getPlayerDataManager().getPlayerData(target);
        targetData.setPartyId(partyId);

        // Notify all party members
        broadcastToParty(partyId, ChatColor.GREEN + target.getName() + " joined the party!");
        target.sendMessage(ChatColor.GREEN + "You joined " + leader.getName() + "'s party!");

        return true;
    }

    public Party getParty(UUID partyId) {
        return parties.get(partyId);
    }

    public UUID getPlayerParty(UUID playerId) {
        return playerToParty.get(playerId);
    }

    public void broadcastToParty(UUID partyId, String message) {
        Party party = parties.get(partyId);
        if (party == null) return;

        for (UUID memberId : party.getMembers()) {
            Player member = plugin.getServer().getPlayer(memberId);
            if (member != null) {
                member.sendMessage(message);
            }
        }
    }

    public Party getPlayerParty(Player player) {
        UUID partyId = playerToParty.get(player.getUniqueId());
        return partyId != null ? parties.get(partyId) : null;
    }

    public boolean leaveParty(Player player) {
        UUID partyId = playerToParty.get(player.getUniqueId());
        if (partyId == null) {
            player.sendMessage(ChatColor.RED + "You are not in a party!");
            return false;
        }

        Party party = parties.get(partyId);
        if (party == null) return false;

        // Remove player from party
        party.removeMember(player.getUniqueId());
        playerToParty.remove(player.getUniqueId());

        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        data.setPartyId(null);

        player.sendMessage(ChatColor.YELLOW + "You left the party.");

        // Notify remaining members
        broadcastToParty(party.getId(), ChatColor.YELLOW + player.getName() + " left the party.");

        // If party is empty or leader left, disband
        if (party.getMembers().isEmpty() || player.getUniqueId().equals(party.getLeader())) {
            disbandPartyInternal(party);
        }

        return true;
    }

    public boolean disbandParty(Player leader) {
        UUID partyId = playerToParty.get(leader.getUniqueId());
        if (partyId == null) {
            leader.sendMessage(ChatColor.RED + "You are not in a party!");
            return false;
        }

        Party party = parties.get(partyId);
        if (party == null) return false;

        if (!party.getLeader().equals(leader.getUniqueId())) {
            leader.sendMessage(ChatColor.RED + "Only the party leader can disband the party!");
            return false;
        }

        broadcastToParty(party.getId(), ChatColor.RED + "The party has been disbanded by the leader.");
        disbandPartyInternal(party);
        return true;
    }

    public boolean kickPlayer(Player leader, Player target) {
        UUID partyId = playerToParty.get(leader.getUniqueId());
        if (partyId == null) {
            leader.sendMessage(ChatColor.RED + "You are not in a party!");
            return false;
        }

        Party party = parties.get(partyId);
        if (party == null) return false;

        if (!party.getLeader().equals(leader.getUniqueId())) {
            leader.sendMessage(ChatColor.RED + "Only the party leader can kick players!");
            return false;
        }

        if (!party.getMembers().contains(target.getUniqueId())) {
            leader.sendMessage(ChatColor.RED + target.getName() + " is not in your party!");
            return false;
        }

        // Remove player from party
        party.removeMember(target.getUniqueId());
        playerToParty.remove(target.getUniqueId());

        PlayerData data = plugin.getPlayerDataManager().getPlayerData(target);
        data.setPartyId(null);

        target.sendMessage(ChatColor.RED + "You have been kicked from the party by " + leader.getName());
        leader.sendMessage(ChatColor.YELLOW + "Kicked " + target.getName() + " from the party.");

        // Notify remaining members
        broadcastToParty(party.getId(), ChatColor.YELLOW + target.getName() + " was kicked from the party.");

        return true;
    }

    private void disbandPartyInternal(Party party) {
        // Remove all members from party tracking
        for (UUID memberId : party.getMembers()) {
            playerToParty.remove(memberId);
            Player member = plugin.getServer().getPlayer(memberId);
            if (member != null) {
                PlayerData data = plugin.getPlayerDataManager().getPlayerData(member);
                data.setPartyId(null);
            }
        }

        // Remove party
        parties.remove(party.getId());
    }

    public void shutdown() {
        parties.clear();
        playerToParty.clear();
    }
}
