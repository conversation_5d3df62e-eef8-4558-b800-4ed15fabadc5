package com.orvyn.mmo.models;

import java.io.Serializable;

public class ActiveEffect implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    private final String effectId;
    private final long startTime;
    private long endTime;
    private long lastTickTime;
    private int stacks;
    
    public ActiveEffect(String effectId, int durationTicks) {
        this.effectId = effectId;
        this.startTime = System.currentTimeMillis();
        this.endTime = startTime + (durationTicks * 50L); // Convert ticks to milliseconds
        this.lastTickTime = startTime;
        this.stacks = 1;
    }
    
    public ActiveEffect(String effectId, int durationTicks, int stacks) {
        this.effectId = effectId;
        this.startTime = System.currentTimeMillis();
        this.endTime = startTime + (durationTicks * 50L);
        this.lastTickTime = startTime;
        this.stacks = stacks;
    }
    
    public boolean isExpired() {
        return System.currentTimeMillis() >= endTime;
    }
    
    public long getRemainingTime() {
        return Math.max(0, endTime - System.currentTimeMillis());
    }
    
    public int getRemainingTicks() {
        return (int) (getRemainingTime() / 50L);
    }
    
    public boolean shouldTick(int tickInterval) {
        long currentTime = System.currentTimeMillis();
        long intervalMs = tickInterval * 50L; // Convert ticks to milliseconds
        return (currentTime - lastTickTime) >= intervalMs;
    }
    
    public void markTicked() {
        this.lastTickTime = System.currentTimeMillis();
    }
    
    public void refreshDuration(int durationTicks) {
        this.endTime = System.currentTimeMillis() + (durationTicks * 50L);
    }
    
    public void addStack() {
        this.stacks++;
    }
    
    public void removeStack() {
        this.stacks = Math.max(1, this.stacks - 1);
    }
    
    // Getters and setters
    public String getEffectId() { return effectId; }
    public long getStartTime() { return startTime; }
    public long getEndTime() { return endTime; }
    public void setEndTime(long endTime) { this.endTime = endTime; }
    public long getLastTickTime() { return lastTickTime; }
    public void setLastTickTime(long lastTickTime) { this.lastTickTime = lastTickTime; }
    public int getStacks() { return stacks; }
    public void setStacks(int stacks) { this.stacks = stacks; }
}
