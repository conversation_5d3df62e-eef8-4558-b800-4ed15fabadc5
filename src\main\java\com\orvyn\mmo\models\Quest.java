package com.orvyn.mmo.models;

import java.util.ArrayList;
import java.util.List;

public class Quest {

    public enum QuestType {
        PERSONAL,    // Unique to each player
        SHARED,      // Same for all players
        DAILY        // Daily rotating quest
    }

    public enum ObjectiveType {
        K<PERSON><PERSON>_MOBS,           // Kill X of specific mob type
        COLLECT_ITEMS,       // Collect X of specific item
        REACH_LOCATION,      // Go to specific coordinates
        MINE_BLOCKS,         // Mine X blocks of specific type
        FISH_ITEMS,          // Fish X items
        CRAFT_ITEMS,         // Craft X items
        GAIN_PROFESSION_XP,  // Gain X profession XP
        DEAL_DAMAGE,         // Deal X damage to mobs
        SURVIVE_TIME         // Survive for X minutes
    }

    private final String id;
    private final QuestType type;
    private final String name;
    private final String description;
    private final int minLevel;
    private final List<QuestObjective> objectives;
    private final List<QuestReward> rewards;
    private final long timeLimit; // 0 = no time limit, otherwise milliseconds
    private final long createdTime;

    // For daily quests
    private long expirationTime; // When this quest expires (for daily quests)

    public Quest(String id, String name, String description, int minLevel,
                List<QuestObjective> objectives, List<QuestReward> rewards) {
        this.id = id;
        this.type = QuestType.SHARED; // Default for backward compatibility
        this.name = name;
        this.description = description;
        this.minLevel = minLevel;
        this.objectives = new ArrayList<>(objectives);
        this.rewards = new ArrayList<>(rewards);
        this.timeLimit = 0;
        this.createdTime = System.currentTimeMillis();
        this.expirationTime = 0;
    }

    public Quest(String id, QuestType type, String name, String description,
                 List<QuestObjective> objectives, List<QuestReward> rewards,
                 int minLevel, long timeLimit) {
        this.id = id;
        this.type = type;
        this.name = name;
        this.description = description;
        this.minLevel = minLevel;
        this.objectives = new ArrayList<>(objectives);
        this.rewards = new ArrayList<>(rewards);
        this.timeLimit = timeLimit;
        this.createdTime = System.currentTimeMillis();
        this.expirationTime = 0;
    }

    // Getters
    public String getId() { return id; }
    public QuestType getType() { return type; }
    public String getName() { return name; }
    public String getDescription() { return description; }
    public int getMinLevel() { return minLevel; }
    public List<QuestObjective> getObjectives() { return new ArrayList<>(objectives); }
    public List<QuestReward> getRewards() { return new ArrayList<>(rewards); }
    public long getTimeLimit() { return timeLimit; }
    public long getCreatedTime() { return createdTime; }
    public long getExpirationTime() { return expirationTime; }

    public void setExpirationTime(long expirationTime) {
        this.expirationTime = expirationTime;
    }

    public boolean isExpired() {
        if (expirationTime == 0) return false;
        return System.currentTimeMillis() > expirationTime;
    }

    public long getTimeRemaining() {
        if (expirationTime == 0) return -1;
        return Math.max(0, expirationTime - System.currentTimeMillis());
    }

    public String getFormattedTimeRemaining() {
        long remaining = getTimeRemaining();
        if (remaining == -1) return "No time limit";
        if (remaining == 0) return "Expired";

        long hours = remaining / (1000 * 60 * 60);
        long minutes = (remaining % (1000 * 60 * 60)) / (1000 * 60);
        long seconds = (remaining % (1000 * 60)) / 1000;

        if (hours > 0) {
            return String.format("%dh %dm %ds", hours, minutes, seconds);
        } else if (minutes > 0) {
            return String.format("%dm %ds", minutes, seconds);
        } else {
            return String.format("%ds", seconds);
        }
    }

    public boolean canPlayerAccept(int playerLevel) {
        return playerLevel >= minLevel;
    }
}
