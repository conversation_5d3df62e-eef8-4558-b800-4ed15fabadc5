package com.orvyn.mmo.gui;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.managers.MagicItemManager;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.*;

public class MagicWorkbenchGUI implements Listener {
    
    private final OrvynMMOPlugin plugin;
    private final Map<UUID, Inventory> openWorkbenches = new HashMap<>();
    
    // Crafting grid slots (3x3)
    private final int[] craftingSlots = {10, 11, 12, 19, 20, 21, 28, 29, 30};
    private final int resultSlot = 24;
    private final int closeSlot = 49;
    
    public MagicWorkbenchGUI(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }
    
    public void openWorkbench(Player player) {
        Inventory workbench = Bukkit.createInventory(null, 54, ChatColor.DARK_PURPLE + "✨ Magic Bench ✨");
        
        // Fill with glass panes
        ItemStack glass = new ItemStack(Material.BLACK_STAINED_GLASS_PANE);
        ItemMeta glassMeta = glass.getItemMeta();
        glassMeta.setDisplayName(" ");
        glass.setItemMeta(glassMeta);
        
        for (int i = 0; i < 54; i++) {
            final int slot = i; // Make effectively final for lambda
            if (!Arrays.stream(craftingSlots).anyMatch(craftingSlot -> craftingSlot == slot) && slot != resultSlot && slot != closeSlot) {
                workbench.setItem(slot, glass);
            }
        }
        
        // Add crafting grid labels
        ItemStack craftingLabel = new ItemStack(Material.CRAFTING_TABLE);
        ItemMeta craftingMeta = craftingLabel.getItemMeta();
        craftingMeta.setDisplayName(ChatColor.YELLOW + "Crafting Grid");
        craftingMeta.setLore(Arrays.asList(
            ChatColor.GRAY + "Place items here to craft",
            ChatColor.GRAY + "magical equipment"
        ));
        craftingLabel.setItemMeta(craftingMeta);
        workbench.setItem(1, craftingLabel);
        
        // Add result label
        ItemStack resultLabel = new ItemStack(Material.NETHER_STAR);
        ItemMeta resultMeta = resultLabel.getItemMeta();
        resultMeta.setDisplayName(ChatColor.GOLD + "Crafting Result");
        resultMeta.setLore(Arrays.asList(
            ChatColor.GRAY + "Crafted item appears here",
            ChatColor.YELLOW + "Click to take result"
        ));
        resultLabel.setItemMeta(resultMeta);
        workbench.setItem(15, resultLabel);
        
        // Add close button
        ItemStack close = new ItemStack(Material.BARRIER);
        ItemMeta closeMeta = close.getItemMeta();
        closeMeta.setDisplayName(ChatColor.RED + "Close");
        close.setItemMeta(closeMeta);
        workbench.setItem(closeSlot, close);
        
        // Add recipe book
        ItemStack recipeBook = new ItemStack(Material.BOOK);
        ItemMeta bookMeta = recipeBook.getItemMeta();
        bookMeta.setDisplayName(ChatColor.AQUA + "Recipe Guide");
        bookMeta.setLore(Arrays.asList(
            ChatColor.GRAY + "Click to view available recipes",
            ChatColor.YELLOW + "Learn how to craft magical items!"
        ));
        recipeBook.setItemMeta(bookMeta);
        workbench.setItem(7, recipeBook);
        
        openWorkbenches.put(player.getUniqueId(), workbench);
        player.openInventory(workbench);
    }
    
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        Player player = (Player) event.getWhoClicked();
        
        Inventory clickedInventory = event.getClickedInventory();
        if (clickedInventory == null) return;
        
        String title = event.getView().getTitle();
        if (!title.contains("Magic Bench")) return;
        
        int slot = event.getSlot();
        
        // Handle close button
        if (slot == closeSlot) {
            event.setCancelled(true);
            player.closeInventory();
            return;
        }
        
        // Handle recipe book
        if (slot == 7) {
            event.setCancelled(true);
            showRecipeGuide(player);
            return;
        }
        
        // Prevent clicking on decoration items (glass panes and labels) but only if they're in decoration slots
        if (event.getCurrentItem() != null &&
            (event.getCurrentItem().getType() == Material.BLACK_STAINED_GLASS_PANE ||
             event.getCurrentItem().getType() == Material.CRAFTING_TABLE)) {
            // Only cancel if this is a decoration slot (not a crafting slot)
            final int finalSlot = slot;
            boolean isCraftingSlot = Arrays.stream(craftingSlots).anyMatch(craftingSlot -> craftingSlot == finalSlot);
            boolean isResultSlot = slot == resultSlot;

            if (!isCraftingSlot && !isResultSlot) {
                event.setCancelled(true);
                return;
            }
        }
        
        // Handle crafting grid
        if (Arrays.stream(craftingSlots).anyMatch(craftingSlot -> craftingSlot == slot)) {
            // Allow placing/removing items in crafting grid
            Bukkit.getScheduler().runTaskLater(plugin, () -> updateCraftingResult(player), 1L);
            return;
        }
        
        // Handle result slot
        if (slot == resultSlot) {
            ItemStack result = event.getCurrentItem();
            if (result != null && result.getType() != Material.AIR) {
                // Take crafted item and consume materials
                event.setCancelled(true);
                takeCraftedItem(player, result);
            }
            return;
        }
    }

    @EventHandler
    public void onInventoryDrag(org.bukkit.event.inventory.InventoryDragEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        Player dragPlayer = (Player) event.getWhoClicked();

        String title = event.getView().getTitle();
        if (!title.contains("Magic Bench")) return;

        // Check if dragging to valid slots
        for (int slot : event.getRawSlots()) {
            if (slot >= 54) continue; // Allow dragging in player inventory

            // Only allow dragging to crafting slots
            final int finalSlot = slot;
            if (!Arrays.stream(craftingSlots).anyMatch(craftingSlot -> craftingSlot == finalSlot)) {
                event.setCancelled(true);
                return;
            }
        }

        // Update crafting result after drag
        Bukkit.getScheduler().runTaskLater(plugin, () -> updateCraftingResult(dragPlayer), 1L);
    }

    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) return;
        Player player = (Player) event.getPlayer();
        
        String title = event.getView().getTitle();
        if (!title.contains("Magic Bench")) return;
        
        // Return items from crafting grid to player
        Inventory workbench = openWorkbenches.remove(player.getUniqueId());
        if (workbench != null) {
            for (int slot : craftingSlots) {
                ItemStack item = workbench.getItem(slot);
                if (item != null && item.getType() != Material.AIR) {
                    player.getInventory().addItem(item);
                }
            }
        }
    }
    
    private void updateCraftingResult(Player player) {
        Inventory workbench = openWorkbenches.get(player.getUniqueId());
        if (workbench == null) return;
        
        // Get crafting grid items
        ItemStack[] craftingGrid = new ItemStack[9];
        for (int i = 0; i < 9; i++) {
            craftingGrid[i] = workbench.getItem(craftingSlots[i]);
        }
        
        // Check for valid recipe
        MagicItemManager itemManager = plugin.getMagicItemManager();
        ItemStack result = itemManager.checkRecipe(craftingGrid);
        
        workbench.setItem(resultSlot, result);
    }
    
    private void takeCraftedItem(Player player, ItemStack result) {
        Inventory workbench = openWorkbenches.get(player.getUniqueId());
        if (workbench == null) return;
        
        // Give item to player
        player.getInventory().addItem(result.clone());
        
        // Consume crafting materials
        for (int slot : craftingSlots) {
            ItemStack item = workbench.getItem(slot);
            if (item != null && item.getType() != Material.AIR) {
                item.setAmount(item.getAmount() - 1);
                if (item.getAmount() <= 0) {
                    workbench.setItem(slot, null);
                }
            }
        }
        
        // Clear result slot
        workbench.setItem(resultSlot, null);
        
        player.sendMessage(ChatColor.GREEN + "✨ Magical item crafted!");
        player.playSound(player.getLocation(), org.bukkit.Sound.BLOCK_ENCHANTMENT_TABLE_USE, 1.0f, 1.5f);
        
        // Update result again in case more items can be crafted
        updateCraftingResult(player);
    }
    
    private void showRecipeGuide(Player player) {
        player.sendMessage("");
        player.sendMessage(ChatColor.DARK_PURPLE + "╔══════════════════════════════════════════════════════════╗");
        player.sendMessage(ChatColor.DARK_PURPLE + "║" + ChatColor.GOLD + "                    Magic Bench Recipes                    " + ChatColor.DARK_PURPLE + "║");
        player.sendMessage(ChatColor.DARK_PURPLE + "╠══════════════════════════════════════════════════════════╣");
        player.sendMessage(ChatColor.DARK_PURPLE + "║ " + ChatColor.YELLOW + "Vein Miner Pickaxe:" + ChatColor.GRAY + " Diamond Pick + 4 Redstone      " + ChatColor.DARK_PURPLE + "║");
        player.sendMessage(ChatColor.DARK_PURPLE + "║ " + ChatColor.YELLOW + "3x3 Excavator:" + ChatColor.GRAY + " Iron Pick + 4 TNT + Nether Star " + ChatColor.DARK_PURPLE + "║");
        player.sendMessage(ChatColor.DARK_PURPLE + "║ " + ChatColor.YELLOW + "Lumber Axe:" + ChatColor.GRAY + " Diamond Axe + 4 Oak Logs         " + ChatColor.DARK_PURPLE + "║");
        player.sendMessage(ChatColor.DARK_PURPLE + "║ " + ChatColor.YELLOW + "Harvester Hoe:" + ChatColor.GRAY + " Diamond Hoe + 4 Wheat Seeds      " + ChatColor.DARK_PURPLE + "║");
        player.sendMessage(ChatColor.DARK_PURPLE + "║ " + ChatColor.YELLOW + "Teleport Staff:" + ChatColor.GRAY + " Stick + 4 Ender Pearls          " + ChatColor.DARK_PURPLE + "║");
        player.sendMessage(ChatColor.DARK_PURPLE + "║ " + ChatColor.YELLOW + "Lightning Sword:" + ChatColor.GRAY + " Diamond Sword + 4 Glowstone     " + ChatColor.DARK_PURPLE + "║");
        player.sendMessage(ChatColor.DARK_PURPLE + "║ " + ChatColor.YELLOW + "Explosive Bow:" + ChatColor.GRAY + " Bow + 4 TNT + Gunpowder          " + ChatColor.DARK_PURPLE + "║");
        player.sendMessage(ChatColor.DARK_PURPLE + "║ " + ChatColor.YELLOW + "And 8 more magical items..." + ChatColor.GRAY + "                     " + ChatColor.DARK_PURPLE + "║");
        player.sendMessage(ChatColor.DARK_PURPLE + "╚══════════════════════════════════════════════════════════╝");
        player.sendMessage("");
        
        player.playSound(player.getLocation(), org.bukkit.Sound.ITEM_BOOK_PAGE_TURN, 1.0f, 1.0f);
    }
}
