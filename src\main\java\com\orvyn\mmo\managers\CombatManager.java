package com.orvyn.mmo.managers;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import com.orvyn.mmo.enums.Attribute;
import org.bukkit.ChatColor;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;

import java.util.Random;

public class CombatManager {
    
    private final OrvynMMOPlugin plugin;
    private final Random random = new Random();
    
    public CombatManager(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
    }
    
    public void dealDamage(Player attacker, LivingEntity target, double baseDamage) {
        PlayerData attackerData = plugin.getPlayerDataManager().getPlayerData(attacker);
        
        // Calculate final damage
        double finalDamage = calculateDamage(attackerData, baseDamage);
        
        // Apply damage
        if (target instanceof Player) {
            Player targetPlayer = (Player) target;
            PlayerData targetData = plugin.getPlayerDataManager().getPlayerData(targetPlayer);
            
            // Apply damage to player data
            targetData.takeDamage(finalDamage);
            
            // Update health bar
            double healthPercent = targetData.getCurrentHP() / targetData.getMaxHP();
            targetPlayer.setHealth(Math.max(0.1, targetPlayer.getMaxHealth() * healthPercent));
            
            // Send damage message
            targetPlayer.sendMessage(ChatColor.RED + "You took " + String.format("%.1f", finalDamage) + " damage!");

            // Check for death
            if (targetData.isDead()) {
                handlePlayerDeath(targetPlayer);
            }
        } else {
            // Regular entity damage
            target.damage(finalDamage);
        }

        // Send damage message to attacker
        attacker.sendMessage(ChatColor.YELLOW + "Dealt " + String.format("%.1f", finalDamage) + " damage!");
    }
    
    private double calculateDamage(PlayerData attackerData, double baseDamage) {
        double damage = baseDamage;
        
        // Apply strength bonus
        int str = attackerData.getTotalAttribute(Attribute.STR);
        damage += str * 0.5; // Each STR adds 0.5 damage
        
        // Apply critical hit
        int critChance = attackerData.getTotalAttribute(Attribute.CRIT);
        if (random.nextInt(100) < critChance) {
            damage *= 1.5; // 50% crit damage bonus
        }
        
        return damage;
    }
    
    private void handlePlayerDeath(Player player) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        
        // Reset HP to full
        data.setCurrentHP(data.getMaxHP());
        
        // Update health bar
        player.setHealth(player.getMaxHealth());
        
        // Teleport to spawn or respawn point
        player.teleport(player.getWorld().getSpawnLocation());
        
        player.sendMessage(ChatColor.RED + "You have died and been respawned!");
    }
    
    public void shutdown() {
        // Cleanup if needed
    }
}
