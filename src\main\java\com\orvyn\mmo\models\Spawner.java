package com.orvyn.mmo.models;

import org.bukkit.Location;
import org.bukkit.World;

import java.util.List;

public class Spawner {
    
    private final String id;
    private final Region region;
    private final List<SpawnerMob> mobs;
    private final int spawnInterval;
    private final boolean enabled;
    
    public Spawner(String id, Region region, List<SpawnerMob> mobs, int spawnInterval, boolean enabled) {
        this.id = id;
        this.region = region;
        this.mobs = mobs;
        this.spawnInterval = spawnInterval;
        this.enabled = enabled;
    }
    
    public boolean isInRegion(Location location) {
        return region.contains(location);
    }
    
    public Location getRandomLocationInRegion() {
        return region.getRandomLocation();
    }
    
    // Getters
    public String getId() { return id; }
    public Region getRegion() { return region; }
    public List<SpawnerMob> getMobs() { return mobs; }
    public int getSpawnInterval() { return spawnInterval; }
    public boolean isEnabled() { return enabled; }
    
    public static class Region {
        private final World world;
        private final Location pos1;
        private final Location pos2;
        private final double minX, maxX, minY, maxY, minZ, maxZ;
        
        public Region(World world, Location pos1, Location pos2) {
            this.world = world;
            this.pos1 = pos1;
            this.pos2 = pos2;
            
            this.minX = Math.min(pos1.getX(), pos2.getX());
            this.maxX = Math.max(pos1.getX(), pos2.getX());
            this.minY = Math.min(pos1.getY(), pos2.getY());
            this.maxY = Math.max(pos1.getY(), pos2.getY());
            this.minZ = Math.min(pos1.getZ(), pos2.getZ());
            this.maxZ = Math.max(pos1.getZ(), pos2.getZ());
        }
        
        public boolean contains(Location location) {
            if (!location.getWorld().equals(world)) {
                return false;
            }
            
            double x = location.getX();
            double y = location.getY();
            double z = location.getZ();
            
            return x >= minX && x <= maxX &&
                   y >= minY && y <= maxY &&
                   z >= minZ && z <= maxZ;
        }
        
        public Location getRandomLocation() {
            double x = minX + Math.random() * (maxX - minX);
            double y = minY + Math.random() * (maxY - minY);
            double z = minZ + Math.random() * (maxZ - minZ);
            
            return new Location(world, x, y, z);
        }
        
        public World getWorld() { return world; }
        public Location getPos1() { return pos1; }
        public Location getPos2() { return pos2; }
        public double getMinX() { return minX; }
        public double getMaxX() { return maxX; }
        public double getMinY() { return minY; }
        public double getMaxY() { return maxY; }
        public double getMinZ() { return minZ; }
        public double getMaxZ() { return maxZ; }
    }
}
