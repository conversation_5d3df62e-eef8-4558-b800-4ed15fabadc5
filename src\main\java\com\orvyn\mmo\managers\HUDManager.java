package com.orvyn.mmo.managers;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class HUDManager {
    
    private final OrvynMMOPlugin plugin;
    private final Map<Player, BukkitRunnable> hudTasks = new ConcurrentHashMap<>();
    
    public HUDManager(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
    }
    
    public void startHUD(Player player) {
        // Stop existing HUD task if any
        stopHUD(player);
        
        // Create new HUD task that updates every second (20 ticks)
        BukkitRunnable hudTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (!player.isOnline()) {
                    cancel();
                    hudTasks.remove(player);
                    return;
                }
                
                updateHUD(player);
            }
        };
        
        // Start the task and store it
        hudTask.runTaskTimer(plugin, 0L, 20L); // Update every 20 ticks (1 second)
        hudTasks.put(player, hudTask);
    }
    
    public void stopHUD(Player player) {
        BukkitRunnable task = hudTasks.remove(player);
        if (task != null) {
            task.cancel();
        }
    }
    
    private void updateHUD(Player player) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        
        // Build HUD string
        StringBuilder hud = new StringBuilder();
        
        // Health section: §c❤ HP: 95/120
        hud.append(ChatColor.RED).append("❤ HP: ")
           .append((int)data.getCurrentHP()).append("/").append((int)data.getMaxHP());
        
        // Mana section: §b✦ Mana: 80/100
        hud.append(" ").append(ChatColor.AQUA).append("✦ Mana: ")
           .append((int)data.getCurrentMana()).append("/").append((int)data.getMaxMana());
        
        // Level and class section: §eLv.5 Warrior
        String playerClass = data.getPlayerClass();
        if (playerClass == null || playerClass.isEmpty()) {
            playerClass = "None";
        }
        hud.append(" ").append(ChatColor.YELLOW).append("Lv.").append(data.getClassLevel())
           .append(" ").append(capitalize(playerClass));
        
        // Active cooldowns section: §7| §6Arc Slash: 3s
        String cooldowns = getActiveCooldowns(data);
        if (!cooldowns.isEmpty()) {
            hud.append(" ").append(ChatColor.GRAY).append("| ").append(cooldowns);
        }
        
        // Send action bar message
        // Use spigot method to send action bar
        player.spigot().sendMessage(net.md_5.bungee.api.ChatMessageType.ACTION_BAR,
            net.md_5.bungee.api.chat.TextComponent.fromLegacyText(hud.toString()));
    }
    
    private String getActiveCooldowns(PlayerData data) {
        StringBuilder cooldowns = new StringBuilder();
        boolean first = true;
        
        // Check all known skills for active cooldowns
        String[] skillIds = {"arc_slash", "fire_bolt", "power_shot", "iron_training_blade", "mage_staff", "hunter_bow"};
        
        for (String skillId : skillIds) {
            if (data.isSkillOnCooldown(skillId)) {
                long remainingMs = data.getSkillCooldownRemaining(skillId);
                long remainingSeconds = (remainingMs + 999) / 1000; // Round up to nearest second
                
                if (remainingSeconds > 0) {
                    if (!first) {
                        cooldowns.append(" ");
                    }
                    
                    String skillName = getSkillDisplayName(skillId);
                    cooldowns.append(ChatColor.GOLD).append(skillName).append(": ")
                             .append(ChatColor.WHITE).append(remainingSeconds).append("s");
                    first = false;
                }
            }
        }
        
        return cooldowns.toString();
    }
    
    private String getSkillDisplayName(String skillId) {
        switch (skillId) {
            case "arc_slash": return "Arc Slash";
            case "fire_bolt": return "Fire Bolt";
            case "power_shot": return "Power Shot";
            case "iron_training_blade": return "Sword Beam";
            case "mage_staff": return "Fireball";
            case "hunter_bow": return "Power Shot";
            default: return capitalize(skillId.replace("_", " "));
        }
    }
    
    private String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase();
    }
    
    public void stopAllHUDs() {
        for (BukkitRunnable task : hudTasks.values()) {
            task.cancel();
        }
        hudTasks.clear();
    }
    
    public void shutdown() {
        stopAllHUDs();
    }
}
