package com.orvyn.mmo.listeners;

import com.orvyn.mmo.OrvynMMOPlugin;
import org.bukkit.NamespacedKey;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.persistence.PersistentDataType;

public class SkillListener implements Listener {
    
    private final OrvynMMOPlugin plugin;
    
    public SkillListener(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItem();

        if (item != null && event.getAction().name().contains("RIGHT_CLICK")) {
            // First check for class weapons using class_weapon key
            String classWeaponId = null;
            if (item.hasItemMeta()) {
                NamespacedKey classWeaponKey = new NamespacedKey(plugin, "class_weapon");
                classWeaponId = item.getItemMeta().getPersistentDataContainer().get(classWeaponKey, PersistentDataType.STRING);
            }

            // If no class weapon, check regular item ID
            String itemId = classWeaponId != null ? classWeaponId : plugin.getItemManager().getItemId(item);

            // Only proceed if we have a valid item ID
            if (itemId == null) return;

            // Check for class weapon triggers
            switch (itemId) {
                case "iron_training_blade":
                case "warrior_blade":
                    plugin.getSkillManager().useSkill(player, "arc_slash");
                    event.setCancelled(true);
                    break;
                case "mage_staff":
                    plugin.getSkillManager().useSkill(player, "fire_bolt");
                    event.setCancelled(true);
                    break;
                case "hunter_bow":
                    plugin.getSkillManager().useSkill(player, "power_shot");
                    event.setCancelled(true);
                    break;
                case "rogue_dagger":
                    plugin.getSkillManager().useSkill(player, "stealth");
                    event.setCancelled(true);
                    break;
                case "paladin_mace":
                    plugin.getSkillManager().useSkill(player, "holy_strike");
                    event.setCancelled(true);
                    break;
                case "druid_staff":
                    plugin.getSkillManager().useSkill(player, "nature_bolt");
                    event.setCancelled(true);
                    break;
            }
        }
    }
}
