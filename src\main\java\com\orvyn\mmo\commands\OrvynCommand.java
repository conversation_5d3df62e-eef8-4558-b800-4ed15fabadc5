package com.orvyn.mmo.commands;

import com.orvyn.mmo.OrvynMMOPlugin;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class OrvynCommand implements CommandExecutor, TabCompleter {
    
    private final OrvynMMOPlugin plugin;
    
    public OrvynCommand(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (args.length == 0) {
            sendHelp(sender);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "help":
                sendHelp(sender);
                return true;
            case "class":
                return handleClass(sender, args);
            case "reload":
                return handleReload(sender);
            case "arcitem":
                return handleArcItem(sender);
            case "stats":
                return handleStats(sender);
            case "skills":
                return handleSkills(sender);
            case "waypoints":
                return handleWaypoints(sender);
            case "quests":
                return handleQuests(sender);
            case "party":
                return handleParty(sender, args);
            case "professions":
                return handleProfessions(sender);
            case "effect":
                return handleEffect(sender, args);
            case "spawner":
                return handleSpawner(sender, args);
            case "npc":
                return handleNPC(sender, args);
            case "magicbench":
                return handleMagicBench(sender);
            default:
                sendHelp(sender);
                return true;
        }
    }
    
    private void sendHelp(CommandSender sender) {
        boolean isPlayer = sender instanceof Player;

        sender.sendMessage("");
        sender.sendMessage(ChatColor.GOLD + "╔═══════════════════════════════════════════════════════════╗");
        sender.sendMessage(ChatColor.GOLD + "║" + ChatColor.YELLOW + "                    🎮 OrvynMMO Setup 🎮                     " + ChatColor.GOLD + "║");
        sender.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");

        if (isPlayer) {
            sender.sendMessage(ChatColor.GOLD + "║" + ChatColor.AQUA + "                    Player Commands                        " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.GREEN + "/ommo class" + ChatColor.GRAY + "              - Choose your class (Warrior/Mage/Archer) " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.GREEN + "/mychar help" + ChatColor.GRAY + "            - View all character commands           " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.GREEN + "/skill list" + ChatColor.GRAY + "             - View your unlocked skills             " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
            sender.sendMessage(ChatColor.GOLD + "║" + ChatColor.YELLOW + "                    Quick Access                           " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.LIGHT_PURPLE + "Type " + ChatColor.WHITE + "/mychar help" + ChatColor.LIGHT_PURPLE + " for the complete command list!     " + ChatColor.GOLD + "║");
        }

        // Admin commands (only show to ops/admins)
        if (sender.hasPermission("orvynmmo.admin")) {
            sender.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
            sender.sendMessage(ChatColor.GOLD + "║" + ChatColor.RED + "                    Admin Commands                         " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.RED + "/ommo reload" + ChatColor.GRAY + "             - Reload plugin configuration           " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.RED + "/ommo arcitem" + ChatColor.GRAY + "            - Get test weapon for debugging          " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.RED + "/ommo effect <args>" + ChatColor.GRAY + "       - Apply effects to players                " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.RED + "/ommo spawner list" + ChatColor.GRAY + "       - List all custom spawners                " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.RED + "/ommo npc <command>" + ChatColor.GRAY + "      - Manage lobby NPCs for new players       " + ChatColor.GOLD + "║");
            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.RED + "/ommo magicbench" + ChatColor.GRAY + "         - Get Magic Bench for custom crafting      " + ChatColor.GOLD + "║");
        }

        sender.sendMessage(ChatColor.GOLD + "╚═══════════════════════════════════════════════════════════╝");
        sender.sendMessage("");
    }
    
    private boolean handleReload(CommandSender sender) {
        if (!sender.hasPermission("orvynmmo.reload")) {
            sender.sendMessage(ChatColor.RED + "No permission!");
            return true;
        }

        boolean success = plugin.reload();
        if (success) {
            sender.sendMessage(ChatColor.GREEN + "OrvynMMO reloaded successfully!");
        } else {
            sender.sendMessage(ChatColor.RED + "Failed to reload OrvynMMO! Check console for errors.");
        }
        return true;
    }
    
    private boolean handleArcItem(CommandSender sender) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "Only players can use this command!");
            return true;
        }

        Player player = (Player) sender;
        plugin.getItemManager().giveItem(player, "iron_training_blade");
        player.sendMessage(ChatColor.GREEN + "Given iron training blade!");
        return true;
    }
    
    private boolean handleClass(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "Only players can use this command!");
            return true;
        }

        Player player = (Player) sender;

        // If no arguments, open class selection GUI
        if (args.length < 2) {
            plugin.getUIManager().openClassSelectionGUI(player);
            return true;
        }

        // Legacy command support: /ommo class set <class>
        if (args[1].equalsIgnoreCase("set") && args.length >= 3) {
            String className = args[2];
            boolean success = plugin.getClassManager().setPlayerClass(player, className);
            if (success) {
                player.sendMessage(ChatColor.GREEN + "Class changed to " + className + "!");
            } else {
                player.sendMessage(ChatColor.RED + "Unknown class: " + className);
            }
        } else {
            // Open GUI for any other case
            plugin.getUIManager().openClassSelectionGUI(player);
        }
        return true;
    }

    private boolean handleStats(CommandSender sender) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "Only players can use this command!");
            return true;
        }

        Player player = (Player) sender;
        plugin.getUIManager().openStatsGUI(player);
        return true;
    }

    private boolean handleSkills(CommandSender sender) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "Only players can use this command!");
            return true;
        }

        Player player = (Player) sender;
        // Open the player management GUI with skills tab
        com.orvyn.mmo.gui.PlayerManagementGUI gui = new com.orvyn.mmo.gui.PlayerManagementGUI(plugin, player);
        gui.openSkillTree();
        return true;
    }

    private boolean handleWaypoints(CommandSender sender) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "Only players can use this command!");
            return true;
        }

        Player player = (Player) sender;
        // Show waypoints list for now
        showWaypointsList(player);
        return true;
    }

    private boolean handleQuests(CommandSender sender) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "Only players can use this command!");
            return true;
        }

        Player player = (Player) sender;
        // Show quests list
        showQuestsList(player);
        return true;
    }

    private boolean handleProfessions(CommandSender sender) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "Only players can use this command!");
            return true;
        }

        Player player = (Player) sender;
        // Show professions list
        showProfessionsList(player);
        return true;
    }

    private boolean handleWaypoint(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "Only players can use this command!");
            return true;
        }

        if (args.length < 3 || !args[1].equalsIgnoreCase("tp")) {
            sender.sendMessage(ChatColor.RED + "Usage: /ommo waypoint tp <name>");
            return true;
        }

        Player player = (Player) sender;
        String waypointName = args[2];

        // Use waypoint manager to teleport
        boolean success = plugin.getWaypointManager().teleportToWaypoint(player, waypointName);
        if (!success) {
            player.sendMessage(ChatColor.RED + "Could not teleport to waypoint: " + waypointName);
            player.sendMessage(ChatColor.GRAY + "Make sure you have discovered this waypoint and have enough coins!");
        }
        return true;
    }

    private boolean handleQuest(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "Only players can use this command!");
            return true;
        }

        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "Usage: /ommo quest list|start|abandon <quest_id>");
            return true;
        }

        Player player = (Player) sender;
        String action = args[1];

        switch (action.toLowerCase()) {
            case "list":
                showQuestsList(player);
                break;
            case "start":
                if (args.length < 3) {
                    player.sendMessage(ChatColor.RED + "Usage: /ommo quest start <quest_id>");
                    return true;
                }
                plugin.getQuestManager().startQuest(player, args[2]);
                break;
            case "abandon":
                if (args.length < 3) {
                    player.sendMessage(ChatColor.RED + "Usage: /ommo quest abandon <quest_id>");
                    return true;
                }
                plugin.getQuestManager().abandonQuest(player, args[2]);
                break;
            default:
                player.sendMessage(ChatColor.RED + "Usage: /ommo quest list|start|abandon <quest_id>");
        }
        return true;
    }

    private boolean handleParty(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "Only players can use this command!");
            return true;
        }

        Player player = (Player) sender;

        if (args.length < 2) {
            // Open party GUI if no arguments
            com.orvyn.mmo.gui.PartyManagementGUI gui = new com.orvyn.mmo.gui.PartyManagementGUI(plugin);
            gui.openPartyGUI(player);
            return true;
        }
        String action = args[1];

        switch (action.toLowerCase()) {
            case "create":
                plugin.getPartyManager().createParty(player);
                break;
            case "invite":
                if (args.length < 3) {
                    player.sendMessage(ChatColor.RED + "Usage: /ommo party invite <player>");
                    return true;
                }
                Player target = plugin.getServer().getPlayer(args[2]);
                if (target != null) {
                    plugin.getPartyManager().invitePlayer(player, target);
                } else {
                    player.sendMessage(ChatColor.RED + "Player not found: " + args[2]);
                }
                break;
            case "leave":
                // For now, just show message - PartyManager needs implementation
                player.sendMessage(ChatColor.YELLOW + "Left party (feature in development)");
                break;
            case "list":
                showPartyInfo(player);
                break;
            default:
                player.sendMessage(ChatColor.RED + "Usage: /ommo party create|invite|leave|list");
        }
        return true;
    }
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();
        
        if (args.length == 1) {
            completions.addAll(Arrays.asList("reload", "arcitem", "class", "stats", "waypoint", "quest", "party", "effect", "waypoints", "quests", "skills", "professions", "spawner"));
        } else if (args.length == 2) {
            switch (args[0].toLowerCase()) {
                case "class":
                    completions.add("set");
                    break;
                case "waypoint":
                    completions.add("tp");
                    break;
                case "quest":
                    completions.add("list");
                    break;
                case "party":
                    completions.addAll(Arrays.asList("create", "invite", "leave", "list"));
                    break;
                case "effect":
                    completions.addAll(Arrays.asList("apply", "remove", "clear", "list"));
                    break;
            }
        } else if (args.length == 3) {
            if (args[0].equalsIgnoreCase("class") && args[1].equalsIgnoreCase("set")) {
                completions.addAll(plugin.getClassManager().getClassNames());
            } else if (args[0].equalsIgnoreCase("effect")) {
                if (args[1].equalsIgnoreCase("apply") || args[1].equalsIgnoreCase("remove") || args[1].equalsIgnoreCase("clear") || args[1].equalsIgnoreCase("list")) {
                    // Tab complete player names
                    for (Player onlinePlayer : plugin.getServer().getOnlinePlayers()) {
                        if (onlinePlayer.getName().toLowerCase().startsWith(args[2].toLowerCase())) {
                            completions.add(onlinePlayer.getName());
                        }
                    }
                }
            }
        } else if (args.length == 4) {
            if (args[0].equalsIgnoreCase("effect")) {
                if (args[1].equalsIgnoreCase("apply") || args[1].equalsIgnoreCase("remove")) {
                    // Tab complete effect names
                    String input = args[3].toLowerCase();
                    for (String effect : Arrays.asList("poison", "regeneration", "strength", "speed", "weakness", "mana_burn", "mana_regen", "invisibility", "stun", "haste", "critical_boost")) {
                        if (effect.startsWith(input)) {
                            completions.add(effect);
                        }
                    }
                } else if (args[1].equalsIgnoreCase("clear")) {
                    // Tab complete categories
                    completions.addAll(Arrays.asList("buff", "debuff", "neutral"));
                }
            }
        } else if (args.length == 5) {
            if (args[0].equalsIgnoreCase("effect") && args[1].equalsIgnoreCase("apply")) {
                // Tab complete duration suggestions
                completions.addAll(Arrays.asList("10", "30", "60", "120", "300"));
            }
        } else if (args.length == 6) {
            if (args[0].equalsIgnoreCase("effect") && args[1].equalsIgnoreCase("apply")) {
                // Tab complete stack suggestions
                completions.addAll(Arrays.asList("1", "2", "3", "4", "5"));
            }
        }
        
        return completions;
    }

    private boolean handleEffect(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "This command can only be used by players!");
            return true;
        }

        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "Usage: /ommo effect <subcommand>");
            sender.sendMessage(ChatColor.YELLOW + "Subcommands:");
            sender.sendMessage(ChatColor.GRAY + "  apply <player> <effect> <duration> [stacks] - Apply effect");
            sender.sendMessage(ChatColor.GRAY + "  remove <player> <effect> - Remove specific effect");
            sender.sendMessage(ChatColor.GRAY + "  clear <player> [category] - Clear effects");
            sender.sendMessage(ChatColor.GRAY + "  list <player> - List active effects");
            return true;
        }

        String subCommand = args[1].toLowerCase();
        switch (subCommand) {
            case "apply":
                return handleEffectApply(sender, args);
            case "remove":
                return handleEffectRemove(sender, args);
            case "clear":
                return handleEffectClear(sender, args);
            case "list":
                return handleEffectList(sender, args);
            default:
                sender.sendMessage(ChatColor.RED + "Unknown effect subcommand: " + subCommand);
                return true;
        }
    }

    private boolean handleEffectApply(CommandSender sender, String[] args) {
        if (args.length < 5) {
            sender.sendMessage(ChatColor.RED + "Usage: /ommo effect apply <player> <effect_name> <duration_seconds> [stacks]");
            sender.sendMessage(ChatColor.YELLOW + "Available effects: poison, regeneration, strength, speed, weakness, mana_burn, mana_regen, invisibility, stun, haste, critical_boost");
            sender.sendMessage(ChatColor.GRAY + "Example: /ommo effect apply Steve poison 30 2");
            return true;
        }

        Player target = plugin.getServer().getPlayer(args[2]);
        if (target == null) {
            sender.sendMessage(ChatColor.RED + "Player not found: " + args[2]);
            return true;
        }

        String effectName = args[3].toLowerCase();
        int duration;
        int stacks = 1;

        try {
            duration = Integer.parseInt(args[4]) * 20; // Convert seconds to ticks
        } catch (NumberFormatException e) {
            sender.sendMessage(ChatColor.RED + "Invalid duration: " + args[4]);
            return true;
        }

        if (args.length > 5) {
            try {
                stacks = Integer.parseInt(args[5]);
            } catch (NumberFormatException e) {
                sender.sendMessage(ChatColor.RED + "Invalid stacks: " + args[5]);
                return true;
            }
        }

        plugin.getEffectManager().applyEffect(target, effectName, duration, stacks);
        sender.sendMessage(ChatColor.GREEN + "✓ Applied " + effectName + " to " + target.getName() + " for " + (duration/20) + " seconds" + (stacks > 1 ? " (" + stacks + " stacks)" : ""));
        if (!sender.equals(target)) {
            target.sendMessage(ChatColor.YELLOW + "You have been affected by " + effectName + "!");
        }

        return true;
    }

    private boolean handleEffectRemove(CommandSender sender, String[] args) {
        if (args.length < 4) {
            sender.sendMessage(ChatColor.RED + "Usage: /ommo effect remove <player> <effect_name>");
            return true;
        }

        Player target = plugin.getServer().getPlayer(args[2]);
        if (target == null) {
            sender.sendMessage(ChatColor.RED + "Player not found: " + args[2]);
            return true;
        }

        String effectName = args[3].toLowerCase();
        plugin.getEffectManager().removeEffect(target, effectName);
        sender.sendMessage(ChatColor.GREEN + "✓ Removed " + effectName + " from " + target.getName());

        return true;
    }

    private boolean handleEffectClear(CommandSender sender, String[] args) {
        if (args.length < 3) {
            sender.sendMessage(ChatColor.RED + "Usage: /ommo effect clear <player> [category]");
            sender.sendMessage(ChatColor.YELLOW + "Categories: buff, debuff, neutral");
            return true;
        }

        Player target = plugin.getServer().getPlayer(args[2]);
        if (target == null) {
            sender.sendMessage(ChatColor.RED + "Player not found: " + args[2]);
            return true;
        }

        if (args.length > 3) {
            String categoryStr = args[3].toLowerCase();
            try {
                com.orvyn.mmo.models.Effect.EffectCategory category = com.orvyn.mmo.models.Effect.EffectCategory.valueOf(categoryStr.toUpperCase());
                plugin.getEffectManager().clearEffectsByCategory(target, category);
                sender.sendMessage(ChatColor.GREEN + "✓ Cleared all " + categoryStr + " effects from " + target.getName());
            } catch (IllegalArgumentException e) {
                sender.sendMessage(ChatColor.RED + "Invalid category: " + categoryStr);
            }
        } else {
            plugin.getEffectManager().clearAllEffects(target);
            sender.sendMessage(ChatColor.GREEN + "✓ Cleared all effects from " + target.getName());
        }

        return true;
    }

    private boolean handleEffectList(CommandSender sender, String[] args) {
        if (args.length < 3) {
            sender.sendMessage(ChatColor.RED + "Usage: /ommo effect list <player>");
            return true;
        }

        Player target = plugin.getServer().getPlayer(args[2]);
        if (target == null) {
            sender.sendMessage(ChatColor.RED + "Player not found: " + args[2]);
            return true;
        }

        com.orvyn.mmo.data.PlayerData data = plugin.getPlayerDataManager().getPlayerData(target);
        if (data == null) {
            sender.sendMessage(ChatColor.RED + "No data found for player: " + target.getName());
            return true;
        }

        java.util.Map<String, com.orvyn.mmo.models.ActiveEffect> activeEffects = data.getActiveEffects();
        if (activeEffects.isEmpty()) {
            sender.sendMessage(ChatColor.YELLOW + target.getName() + " has no active effects.");
            return true;
        }

        sender.sendMessage(ChatColor.GOLD + "=== Active Effects for " + target.getName() + " ===");
        for (java.util.Map.Entry<String, com.orvyn.mmo.models.ActiveEffect> entry : activeEffects.entrySet()) {
            com.orvyn.mmo.models.ActiveEffect activeEffect = entry.getValue();
            com.orvyn.mmo.models.Effect effect = plugin.getEffectManager().getEffect(entry.getKey());

            String effectName = effect != null ? effect.getName() : entry.getKey();
            int remainingSeconds = activeEffect.getRemainingTicks() / 20;
            String stackText = activeEffect.getStacks() > 1 ? " (" + activeEffect.getStacks() + " stacks)" : "";

            sender.sendMessage(ChatColor.WHITE + "• " + effectName + " - " + remainingSeconds + "s" + stackText);
        }

        return true;
    }

    private boolean handleSpawner(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "This command can only be used by players!");
            return true;
        }

        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "Usage: /ommo spawner list");
            return true;
        }

        String subCommand = args[1].toLowerCase();

        if (subCommand.equals("list")) {
            sender.sendMessage(ChatColor.GOLD + "=== Active Spawners ===");

            for (String spawnerId : plugin.getSpawnerManager().getSpawners().keySet()) {
                int entityCount = 0;
                for (String id : plugin.getSpawnerManager().getSpawnedEntities().values()) {
                    if (id.equals(spawnerId)) {
                        entityCount++;
                    }
                }

                sender.sendMessage(ChatColor.YELLOW + spawnerId + ChatColor.GRAY + " - " + entityCount + " entities");
            }

            return true;
        }

        sender.sendMessage(ChatColor.RED + "Unknown spawner command: " + subCommand);
        return true;
    }

    private void showWaypointsList(Player player) {
        player.sendMessage("");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");
        player.sendMessage(ChatColor.YELLOW + "    Discovered Waypoints");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");

        com.orvyn.mmo.data.PlayerData data = plugin.getPlayerDataManager().getPlayerData(player.getUniqueId());
        if (data == null || data.getDiscoveredWaypoints().isEmpty()) {
            player.sendMessage(ChatColor.GRAY + "No waypoints discovered yet!");
        } else {
            for (String waypointName : data.getDiscoveredWaypoints()) {
                com.orvyn.mmo.models.Waypoint waypoint = plugin.getWaypointManager().getWaypoint(waypointName);
                if (waypoint != null) {
                    player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + waypoint.getDisplayName() +
                                     ChatColor.GRAY + " - Cost: " + waypoint.getCost() + " coins");
                }
            }
        }

        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");
        player.sendMessage(ChatColor.GRAY + "Use " + ChatColor.YELLOW + "/ommo waypoint tp <name>" + ChatColor.GRAY + " to teleport");
        player.sendMessage("");
    }

    private void showQuestsList(Player player) {
        player.sendMessage("");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");
        player.sendMessage(ChatColor.YELLOW + "    Quest Progress");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");

        com.orvyn.mmo.data.PlayerData data = plugin.getPlayerDataManager().getPlayerData(player.getUniqueId());
        if (data == null) return;

        // Show active quests
        if (!data.getActiveQuests().isEmpty()) {
            player.sendMessage(ChatColor.GREEN + "Active Quests:");
            for (String questId : data.getActiveQuests().keySet()) {
                com.orvyn.mmo.models.Quest quest = plugin.getQuestManager().getQuest(questId);
                if (quest != null) {
                    player.sendMessage(ChatColor.YELLOW + "• " + ChatColor.WHITE + quest.getName() +
                                     ChatColor.GRAY + " - " + quest.getDescription());
                }
            }
        }

        // Show completed quests count
        player.sendMessage(ChatColor.AQUA + "Completed Quests: " + ChatColor.WHITE + data.getCompletedQuests().size());

        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");
        player.sendMessage("");
    }

    private void showProfessionsList(Player player) {
        player.sendMessage("");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");
        player.sendMessage(ChatColor.YELLOW + "    Profession Levels");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");

        com.orvyn.mmo.data.PlayerData data = plugin.getPlayerDataManager().getPlayerData(player.getUniqueId());
        if (data == null) return;

        String[] professions = {"mining", "woodcutting", "fishing", "farming"};

        for (String profession : professions) {
            int level = data.getProfessionLevel(profession);
            long exp = data.getProfessionExp(profession);

            String displayName = profession.substring(0, 1).toUpperCase() + profession.substring(1);
            player.sendMessage(ChatColor.AQUA + displayName + ": " + ChatColor.WHITE + "Level " + level +
                             ChatColor.GRAY + " (" + exp + " XP)");
        }

        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");
        player.sendMessage("");
    }

    private void showPartyInfo(Player player) {
        player.sendMessage("");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");
        player.sendMessage(ChatColor.YELLOW + "    Party Information");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");

        // For now, show basic party info
        player.sendMessage(ChatColor.GRAY + "Party system is in development!");
        player.sendMessage(ChatColor.AQUA + "Available commands:");
        player.sendMessage(ChatColor.YELLOW + "• /ommo party create" + ChatColor.GRAY + " - Create a new party");
        player.sendMessage(ChatColor.YELLOW + "• /ommo party invite <player>" + ChatColor.GRAY + " - Invite a player");
        player.sendMessage(ChatColor.YELLOW + "• /ommo party leave" + ChatColor.GRAY + " - Leave current party");

        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");
        player.sendMessage("");
    }

    private boolean handleNPC(CommandSender sender, String[] args) {
        if (!sender.hasPermission("orvynmmo.admin")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to use this command!");
            return true;
        }

        if (args.length < 2) {
            showNPCHelp(sender);
            return true;
        }

        String action = args[1];
        switch (action.toLowerCase()) {
            case "spawn":
                return handleNPCSpawn(sender, args);
            case "remove":
                return handleNPCRemove(sender, args);
            case "list":
                return handleNPCList(sender);
            case "teleport":
                return handleNPCTeleport(sender, args);
            default:
                showNPCHelp(sender);
                return true;
        }
    }

    private boolean handleNPCSpawn(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "Only players can spawn NPCs!");
            return true;
        }

        Player player = (Player) sender;

        if (args.length < 3) {
            sender.sendMessage(ChatColor.RED + "Usage: /ommo npc spawn <type>");
            sender.sendMessage(ChatColor.YELLOW + "Available types: class, tutorial, quest, profession, waypoint");
            return true;
        }

        String npcType = args[2].toLowerCase();

        // Map command arguments to NPC types
        com.orvyn.mmo.models.NPC.NPCType type;
        switch (npcType) {
            case "class":
                type = com.orvyn.mmo.models.NPC.NPCType.CLASS_TRAINER;
                break;
            case "tutorial":
                type = com.orvyn.mmo.models.NPC.NPCType.TUTORIAL;
                break;
            case "quest":
                type = com.orvyn.mmo.models.NPC.NPCType.QUEST_GIVER;
                break;
            case "profession":
                type = com.orvyn.mmo.models.NPC.NPCType.PROFESSION_TRAINER;
                break;
            case "waypoint":
                type = com.orvyn.mmo.models.NPC.NPCType.WAYPOINT_GUIDE;
                break;
            default:
                sender.sendMessage(ChatColor.RED + "Invalid NPC type: " + npcType);
                sender.sendMessage(ChatColor.YELLOW + "Available types: class, tutorial, quest, profession, waypoint");
                return true;
        }

        // Create and spawn NPC at player's location
        String npcId = createAndSpawnNPC(type, player.getLocation());
        if (npcId != null) {
            sender.sendMessage(ChatColor.GREEN + "✅ Successfully spawned " + type.name() + " NPC with ID: " + npcId);
            sender.sendMessage(ChatColor.GRAY + "Location: " +
                player.getLocation().getBlockX() + ", " +
                player.getLocation().getBlockY() + ", " +
                player.getLocation().getBlockZ());
        } else {
            sender.sendMessage(ChatColor.RED + "❌ Failed to spawn NPC!");
        }

        return true;
    }

    private boolean handleNPCRemove(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "Only players can remove NPCs!");
            return true;
        }

        Player player = (Player) sender;

        if (args.length >= 3) {
            // Remove specific NPC by ID
            String npcId = args[2];
            if (removeNPCById(npcId)) {
                sender.sendMessage(ChatColor.GREEN + "✅ Successfully removed NPC: " + npcId);
            } else {
                sender.sendMessage(ChatColor.RED + "❌ NPC not found: " + npcId);
            }
        } else {
            // Remove nearest NPC
            String removedId = removeNearestNPC(player.getLocation());
            if (removedId != null) {
                sender.sendMessage(ChatColor.GREEN + "✅ Successfully removed nearest NPC: " + removedId);
            } else {
                sender.sendMessage(ChatColor.RED + "❌ No NPCs found nearby!");
            }
        }

        return true;
    }

    private boolean handleNPCList(CommandSender sender) {
        var npcs = plugin.getNPCManager().getNPCs();

        if (npcs.isEmpty()) {
            sender.sendMessage(ChatColor.YELLOW + "No NPCs are currently spawned.");
            return true;
        }

        sender.sendMessage("");
        sender.sendMessage(ChatColor.GOLD + "╔═══════════════════════════════════════════════════════════╗");
        sender.sendMessage(ChatColor.GOLD + "║" + ChatColor.YELLOW + "                      Active NPCs                          " + ChatColor.GOLD + "║");
        sender.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");

        for (var npc : npcs.values()) {
            String location = npc.getLocation() != null ?
                npc.getLocation().getWorld().getName() + " (" +
                npc.getLocation().getBlockX() + ", " +
                npc.getLocation().getBlockY() + ", " +
                npc.getLocation().getBlockZ() + ")" : "Unknown";

            sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.GREEN + npc.getId() +
                ChatColor.GRAY + " - " + ChatColor.AQUA + npc.getType().name() +
                ChatColor.GRAY + " at " + location + " " + ChatColor.GOLD + "║");
        }

        sender.sendMessage(ChatColor.GOLD + "╚═══════════════════════════════════════════════════════════╝");
        sender.sendMessage("");
        return true;
    }

    private boolean handleNPCTeleport(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "Only players can teleport to NPCs!");
            return true;
        }

        if (args.length < 3) {
            sender.sendMessage(ChatColor.RED + "Usage: /ommo npc teleport <npc_id>");
            return true;
        }

        Player player = (Player) sender;
        String npcId = args[2];

        var npc = plugin.getNPCManager().getNPCs().get(npcId);
        if (npc == null) {
            sender.sendMessage(ChatColor.RED + "❌ NPC not found: " + npcId);
            return true;
        }

        if (npc.getLocation() == null) {
            sender.sendMessage(ChatColor.RED + "❌ NPC location is invalid!");
            return true;
        }

        player.teleport(npc.getLocation());
        sender.sendMessage(ChatColor.GREEN + "✅ Teleported to NPC: " + npcId);
        return true;
    }

    private void showNPCHelp(CommandSender sender) {
        sender.sendMessage("");
        sender.sendMessage(ChatColor.GOLD + "╔═══════════════════════════════════════════════════════════╗");
        sender.sendMessage(ChatColor.GOLD + "║" + ChatColor.YELLOW + "                    NPC Management                         " + ChatColor.GOLD + "║");
        sender.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
        sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.GREEN + "/ommo npc spawn <type>" + ChatColor.GRAY + "     - Spawn NPC at your location        " + ChatColor.GOLD + "║");
        sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.GREEN + "/ommo npc remove [id]" + ChatColor.GRAY + "      - Remove NPC (nearest or by ID)     " + ChatColor.GOLD + "║");
        sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.GREEN + "/ommo npc list" + ChatColor.GRAY + "             - List all active NPCs             " + ChatColor.GOLD + "║");
        sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.GREEN + "/ommo npc teleport <id>" + ChatColor.GRAY + "   - Teleport to specific NPC           " + ChatColor.GOLD + "║");
        sender.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
        sender.sendMessage(ChatColor.GOLD + "║" + ChatColor.YELLOW + "                    NPC Types                              " + ChatColor.GOLD + "║");
        sender.sendMessage(ChatColor.GOLD + "╠═══════════════════════════════════════════════════════════╣");
        sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.AQUA + "class" + ChatColor.GRAY + "      - Class selection trainer (opens class GUI)    " + ChatColor.GOLD + "║");
        sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.AQUA + "tutorial" + ChatColor.GRAY + "   - Tutorial guide for new players              " + ChatColor.GOLD + "║");
        sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.AQUA + "quest" + ChatColor.GRAY + "      - Quest giver (opens quest system)            " + ChatColor.GOLD + "║");
        sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.AQUA + "profession" + ChatColor.GRAY + " - Profession trainer                          " + ChatColor.GOLD + "║");
        sender.sendMessage(ChatColor.GOLD + "║ " + ChatColor.AQUA + "waypoint" + ChatColor.GRAY + "   - Waypoint guide for travel                   " + ChatColor.GOLD + "║");
        sender.sendMessage(ChatColor.GOLD + "╚═══════════════════════════════════════════════════════════╝");
        sender.sendMessage("");
    }

    // Helper methods for NPC management
    private String createAndSpawnNPC(com.orvyn.mmo.models.NPC.NPCType type, org.bukkit.Location location) {
        try {
            // Create NPC configuration data
            String npcId = generateNPCId(type);
            String name = getDefaultNPCName(type);
            java.util.List<String> dialogue = getDefaultDialogue(type);

            // Create NPC object
            com.orvyn.mmo.models.NPC npc = new com.orvyn.mmo.models.NPC(
                npcId, type, org.bukkit.entity.EntityType.VILLAGER, name, location,
                dialogue, org.bukkit.entity.Villager.Profession.LIBRARIAN,
                null, null, new java.util.ArrayList<>(), true, true
            );

            // Add to NPCManager and spawn directly
            boolean success = plugin.getNPCManager().addAndSpawnNPC(npc);

            return success ? npcId : null;
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to create NPC: " + e.getMessage());
            return null;
        }
    }

    private boolean removeNPCById(String npcId) {
        return plugin.getNPCManager().removeNPC(npcId);
    }

    private String removeNearestNPC(org.bukkit.Location location) {
        try {
            var npcs = plugin.getNPCManager().getNPCs();
            String nearestId = null;
            double nearestDistance = Double.MAX_VALUE;

            for (var entry : npcs.entrySet()) {
                var npc = entry.getValue();
                if (npc.getLocation() != null &&
                    npc.getLocation().getWorld().equals(location.getWorld())) {

                    double distance = npc.getLocation().distance(location);
                    if (distance < nearestDistance && distance <= 10.0) { // Within 10 blocks
                        nearestDistance = distance;
                        nearestId = entry.getKey();
                    }
                }
            }

            if (nearestId != null) {
                boolean success = plugin.getNPCManager().removeNPC(nearestId);
                return success ? nearestId : null;
            }

            return null;
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to remove nearest NPC: " + e.getMessage());
            return null;
        }
    }

    private String generateNPCId(com.orvyn.mmo.models.NPC.NPCType type) {
        String base = type.name().toLowerCase() + "_";
        int counter = 1;
        String id = base + counter;

        var npcs = plugin.getNPCManager().getNPCs();
        while (npcs.containsKey(id)) {
            counter++;
            id = base + counter;
        }

        return id;
    }

    private String getDefaultNPCName(com.orvyn.mmo.models.NPC.NPCType type) {
        switch (type) {
            case CLASS_TRAINER:
                return "§6✨ §eClass Trainer §6✨";
            case TUTORIAL:
                return "§6✨ §eAdventure Guide §6✨";
            case QUEST_GIVER:
                return "§6✨ §eQuest Master §6✨";
            case PROFESSION_TRAINER:
                return "§6✨ §eProfession Trainer §6✨";
            case WAYPOINT_GUIDE:
                return "§6✨ §eWaypoint Guide §6✨";
            default:
                return "§6✨ §eNPC §6✨";
        }
    }

    private java.util.List<String> getDefaultDialogue(com.orvyn.mmo.models.NPC.NPCType type) {
        java.util.List<String> dialogue = new java.util.ArrayList<>();

        switch (type) {
            case CLASS_TRAINER:
                dialogue.add("§eWelcome, adventurer!");
                dialogue.add("§aChoose your class to begin your journey in OrvynMMO!");
                dialogue.add("§7Each class has unique skills and abilities.");
                break;
            case TUTORIAL:
                dialogue.add("§eGreetings, new adventurer!");
                dialogue.add("§aI'm here to help you get started with OrvynMMO.");
                dialogue.add("§7Let me show you how to choose your class!");
                break;
            case QUEST_GIVER:
                dialogue.add("§eHello there, brave soul!");
                dialogue.add("§aI have exciting quests waiting for you!");
                dialogue.add("§7Complete quests to gain experience and rewards.");
                break;
            case PROFESSION_TRAINER:
                dialogue.add("§eWelcome to the training grounds!");
                dialogue.add("§aI can help you master various professions.");
                dialogue.add("§7Mining, farming, and combat await!");
                break;
            case WAYPOINT_GUIDE:
                dialogue.add("§eReady to explore the world?");
                dialogue.add("§aI can help you set up waypoints for fast travel!");
                dialogue.add("§7Never get lost again with our waypoint system.");
                break;
            default:
                dialogue.add("§eHello there!");
                dialogue.add("§aHow can I help you today?");
                break;
        }

        return dialogue;
    }

    private boolean handleMagicBench(CommandSender sender) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "Only players can use this command!");
            return true;
        }

        Player player = (Player) sender;

        // Check if player has permission (op or specific permission)
        if (!player.isOp() && !player.hasPermission("orvynmmo.admin.magicbench")) {
            player.sendMessage(ChatColor.RED + "You don't have permission to use this command!");
            return true;
        }

        // Give magic bench to player
        org.bukkit.inventory.ItemStack magicBench = plugin.getMagicWorkbenchManager().createMagicWorkbench();
        player.getInventory().addItem(magicBench);

        player.sendMessage(ChatColor.DARK_PURPLE + "✨ Magic Bench given! Place it in the world and right-click to use.");
        player.sendMessage(ChatColor.GRAY + "Use this magical crafting station to create powerful items!");
        player.playSound(player.getLocation(), org.bukkit.Sound.BLOCK_ENCHANTMENT_TABLE_USE, 1.0f, 1.0f);

        return true;
    }
}
