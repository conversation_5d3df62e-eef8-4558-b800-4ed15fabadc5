package com.orvyn.mmo.events;

import org.bukkit.entity.Player;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

public class OrvynQuestCompleteEvent extends Event {
    
    private static final HandlerList handlers = new HandlerList();
    
    private final Player player;
    private final String questId;
    
    public OrvynQuestCompleteEvent(Player player, String questId) {
        this.player = player;
        this.questId = questId;
    }
    
    public Player getPlayer() {
        return player;
    }
    
    public String getQuestId() {
        return questId;
    }
    
    @Override
    public HandlerList getHandlers() {
        return handlers;
    }
    
    public static HandlerList getHandlerList() {
        return handlers;
    }
}
