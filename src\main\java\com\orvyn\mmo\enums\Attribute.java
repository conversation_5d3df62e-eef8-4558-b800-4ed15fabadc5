package com.orvyn.mmo.enums;

public enum Attribute {
    HP("Health Points"),
    MANA("Mana Points"),
    STR("Strength"),
    AGI("Agility"),
    INT("Intelligence"),
    CRIT("Critical Chance"),
    HASTE("Haste");
    
    private final String displayName;
    
    Attribute(String displayName) {
        this.displayName = displayName;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public static Attribute fromString(String name) {
        try {
            return valueOf(name.toUpperCase());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
}
