package com.orvyn.mmo.models;

import org.bukkit.Location;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;

import java.io.Serializable;
import java.util.UUID;

public class OrvynNPC implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String id;
    private String name;
    private NPCType type;
    private Location location;
    private EntityType entityType;
    private UUID entityUUID;
    private boolean persistent;
    
    public enum NPCType {
        CLASS_TRAINER("Class Trainer", "Choose your adventure class!"),
        ADVENTURE_GUIDE("Adventure Guide", "Learn about OrvynMMO features!"),
        QUEST_GIVER("Quest Master", "Accept exciting quests!"),
        EQUIPMENT_VENDOR("Equipment Vendor", "Enhance your gear!");
        
        private final String displayName;
        private final String description;
        
        NPCType(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    public OrvynNPC(String id, String name, NPCType type, Location location) {
        this.id = id;
        this.name = name;
        this.type = type;
        this.location = location.clone();
        this.entityType = EntityType.VILLAGER; // Default to villager
        this.persistent = true;
    }
    
    public OrvynNPC(String id, NPCType type, Location location) {
        this(id, type.getDisplayName(), type, location);
    }
    
    // Getters and setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public NPCType getType() {
        return type;
    }
    
    public void setType(NPCType type) {
        this.type = type;
    }
    
    public Location getLocation() {
        return location != null ? location.clone() : null;
    }
    
    public void setLocation(Location location) {
        this.location = location != null ? location.clone() : null;
    }
    
    public EntityType getEntityType() {
        return entityType;
    }
    
    public void setEntityType(EntityType entityType) {
        this.entityType = entityType;
    }
    
    public UUID getEntityUUID() {
        return entityUUID;
    }
    
    public void setEntityUUID(UUID entityUUID) {
        this.entityUUID = entityUUID;
    }
    
    public boolean isPersistent() {
        return persistent;
    }
    
    public void setPersistent(boolean persistent) {
        this.persistent = persistent;
    }
    
    @Override
    public String toString() {
        return "OrvynNPC{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", type=" + type +
                ", location=" + (location != null ? 
                    location.getWorld().getName() + ":" + 
                    location.getBlockX() + "," + 
                    location.getBlockY() + "," + 
                    location.getBlockZ() : "null") +
                '}';
    }
}
