package com.orvyn.mmo.managers;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import org.bukkit.Bukkit;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

public class RegenerationManager implements Listener {
    
    private final OrvynMMOPlugin plugin;
    
    // Combat tracking
    private final Map<UUID, Long> lastCombatTime = new ConcurrentHashMap<>();
    
    // Configuration
    private double healthRegenRate;
    private double manaRegenRate;
    private long combatTimeout;
    private long regenInterval;
    private boolean enableHealthRegen;
    private boolean enableManaRegen;
    private boolean showRegenEffects;
    private double healthRegenPercent;
    private double manaRegenPercent;
    
    // Regeneration task
    private BukkitRunnable regenTask;
    
    public RegenerationManager(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
        loadConfiguration();
        startRegenerationTask();
    }
    
    private void loadConfiguration() {
        FileConfiguration config = plugin.getConfigHub().getConfig("config");
        if (config == null) {
            plugin.getLogger().warning("Config not found! Using default regeneration settings.");
            useDefaultSettings();
            return;
        }
        
        // Load regeneration settings
        enableHealthRegen = config.getBoolean("regeneration.health.enabled", true);
        enableManaRegen = config.getBoolean("regeneration.mana.enabled", true);
        
        healthRegenRate = config.getDouble("regeneration.health.rate", 2.0);
        manaRegenRate = config.getDouble("regeneration.mana.rate", 5.0);
        
        healthRegenPercent = config.getDouble("regeneration.health.percent_per_tick", 0.02); // 2% per tick
        manaRegenPercent = config.getDouble("regeneration.mana.percent_per_tick", 0.05); // 5% per tick
        
        combatTimeout = config.getLong("regeneration.combat_timeout", 8000); // 8 seconds
        regenInterval = config.getLong("regeneration.interval", 2000); // 2 seconds
        
        showRegenEffects = config.getBoolean("regeneration.show_effects", true);
    }
    
    private void useDefaultSettings() {
        enableHealthRegen = true;
        enableManaRegen = true;
        healthRegenRate = 2.0;
        manaRegenRate = 5.0;
        healthRegenPercent = 0.02;
        manaRegenPercent = 0.05;
        combatTimeout = 8000;
        regenInterval = 2000;
        showRegenEffects = true;
    }
    
    private void startRegenerationTask() {
        regenTask = new BukkitRunnable() {
            @Override
            public void run() {
                processRegeneration();
            }
        };
        regenTask.runTaskTimer(plugin, 0L, regenInterval / 50L); // Convert ms to ticks
    }
    
    private void processRegeneration() {
        long currentTime = System.currentTimeMillis();
        
        for (Player player : Bukkit.getOnlinePlayers()) {
            if (player.isDead()) continue;
            
            PlayerData data = plugin.getPlayerDataManager().getPlayerData(player.getUniqueId());
            if (data == null) continue;
            
            // Check if player is out of combat
            if (isInCombat(player.getUniqueId(), currentTime)) {
                continue;
            }
            
            boolean regenerated = false;
            
            // Health regeneration
            if (enableHealthRegen && data.getCurrentHP() < data.getMaxHP()) {
                double regenAmount = calculateHealthRegen(data);
                if (regenAmount > 0) {
                    data.heal(regenAmount);
                    player.setHealth(Math.min(player.getMaxHealth(), player.getHealth() + regenAmount));
                    regenerated = true;
                }
            }
            
            // Mana regeneration
            if (enableManaRegen && data.getCurrentMana() < data.getMaxMana()) {
                double regenAmount = calculateManaRegen(data);
                if (regenAmount > 0) {
                    data.restoreMana(regenAmount);
                    regenerated = true;
                }
            }
            
            // Show regeneration effects
            if (regenerated && showRegenEffects) {
                showRegenerationEffects(player);
            }
        }
    }
    
    private double calculateHealthRegen(PlayerData data) {
        // Use either flat rate or percentage, whichever is higher
        double flatRegen = healthRegenRate;
        double percentRegen = data.getMaxHP() * healthRegenPercent;
        
        return Math.max(flatRegen, percentRegen);
    }
    
    private double calculateManaRegen(PlayerData data) {
        // Use either flat rate or percentage, whichever is higher
        double flatRegen = manaRegenRate;
        double percentRegen = data.getMaxMana() * manaRegenPercent;
        
        return Math.max(flatRegen, percentRegen);
    }
    
    private void showRegenerationEffects(Player player) {
        // Subtle particle effect
        player.getWorld().spawnParticle(Particle.HEART, 
            player.getLocation().add(0, 2, 0), 
            1, 0.3, 0.3, 0.3, 0.01);
        
        // Quiet sound effect (only for the player)
        player.playSound(player.getLocation(), Sound.BLOCK_NOTE_BLOCK_CHIME, 0.1f, 2.0f);
    }
    
    private boolean isInCombat(UUID playerId, long currentTime) {
        Long lastCombat = lastCombatTime.get(playerId);
        if (lastCombat == null) return false;
        
        return (currentTime - lastCombat) < combatTimeout;
    }
    
    public void enterCombat(UUID playerId) {
        lastCombatTime.put(playerId, System.currentTimeMillis());
    }
    
    public void exitCombat(UUID playerId) {
        lastCombatTime.remove(playerId);
    }
    
    public boolean isPlayerInCombat(UUID playerId) {
        return isInCombat(playerId, System.currentTimeMillis());
    }
    
    public long getCombatTimeRemaining(UUID playerId) {
        Long lastCombat = lastCombatTime.get(playerId);
        if (lastCombat == null) return 0;
        
        long elapsed = System.currentTimeMillis() - lastCombat;
        return Math.max(0, combatTimeout - elapsed);
    }
    
    @EventHandler
    public void onEntityDamage(EntityDamageEvent event) {
        if (!(event.getEntity() instanceof Player)) return;
        
        Player player = (Player) event.getEntity();
        enterCombat(player.getUniqueId());
    }
    
    @EventHandler
    public void onEntityDamageByEntity(EntityDamageByEntityEvent event) {
        // Mark both attacker and victim as in combat
        if (event.getDamager() instanceof Player) {
            Player attacker = (Player) event.getDamager();
            enterCombat(attacker.getUniqueId());
        }
        
        if (event.getEntity() instanceof Player) {
            Player victim = (Player) event.getEntity();
            enterCombat(victim.getUniqueId());
        }
    }
    
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        // Player starts out of combat
        exitCombat(event.getPlayer().getUniqueId());
    }
    
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        // Clean up combat data
        lastCombatTime.remove(event.getPlayer().getUniqueId());
    }
    
    public void shutdown() {
        if (regenTask != null) {
            regenTask.cancel();
        }
        lastCombatTime.clear();
    }
    
    // Configuration getters and setters
    public double getHealthRegenRate() { return healthRegenRate; }
    public void setHealthRegenRate(double rate) { this.healthRegenRate = rate; }
    
    public double getManaRegenRate() { return manaRegenRate; }
    public void setManaRegenRate(double rate) { this.manaRegenRate = rate; }
    
    public long getCombatTimeout() { return combatTimeout; }
    public void setCombatTimeout(long timeout) { this.combatTimeout = timeout; }
    
    public boolean isHealthRegenEnabled() { return enableHealthRegen; }
    public void setHealthRegenEnabled(boolean enabled) { this.enableHealthRegen = enabled; }
    
    public boolean isManaRegenEnabled() { return enableManaRegen; }
    public void setManaRegenEnabled(boolean enabled) { this.enableManaRegen = enabled; }
    
    public boolean isShowRegenEffects() { return showRegenEffects; }
    public void setShowRegenEffects(boolean show) { this.showRegenEffects = show; }
}
