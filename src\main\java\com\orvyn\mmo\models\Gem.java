package com.orvyn.mmo.models;

import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.*;

public class Gem {
    
    public enum GemType {
        DAMAGE, DEFENSE, SPEED, CRITIC<PERSON>, ELEMENTAL, SKILL
    }
    
    public enum GemRarity {
        COMMON(ChatColor.WHITE, 1.0),
        UNCOMMON(ChatColor.GREEN, 1.5),
        RARE(ChatColor.BLUE, 2.0),
        EPIC(ChatColor.DARK_PURPLE, 3.0),
        LEGENDARY(ChatColor.GOLD, 5.0);
        
        private final ChatColor color;
        private final double multiplier;
        
        GemRarity(ChatColor color, double multiplier) {
            this.color = color;
            this.multiplier = multiplier;
        }
        
        public ChatColor getColor() { return color; }
        public double getMultiplier() { return multiplier; }
    }
    
    private final String gemId;
    private final GemType type;
    private final GemRarity rarity;
    private final String name;
    private final Map<String, Integer> statBonuses;
    private final String skillId; // For skill gems
    private final String description;
    
    public Gem(String gemId, GemType type, GemRarity rarity, String name, 
               Map<String, Integer> statBonuses, String skillId, String description) {
        this.gemId = gemId;
        this.type = type;
        this.rarity = rarity;
        this.name = name;
        this.statBonuses = new HashMap<>();
        
        // Apply rarity multiplier to stat bonuses
        for (Map.Entry<String, Integer> bonus : statBonuses.entrySet()) {
            int enhancedValue = (int) (bonus.getValue() * rarity.getMultiplier());
            this.statBonuses.put(bonus.getKey(), enhancedValue);
        }
        
        this.skillId = skillId;
        this.description = description;
    }
    
    public ItemStack createItemStack() {
        Material material = getMaterialForType();
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        // Set display name with rarity color
        meta.setDisplayName(rarity.getColor() + name + " Gem");
        
        // Create lore
        List<String> lore = new ArrayList<>();
        
        // Gem info
        lore.add(ChatColor.GRAY + "Type: " + ChatColor.WHITE + capitalizeFirst(type.name()));
        lore.add(ChatColor.GRAY + "Rarity: " + rarity.getColor() + capitalizeFirst(rarity.name()));
        lore.add("");
        
        // Description
        if (description != null && !description.isEmpty()) {
            lore.add(ChatColor.YELLOW + description);
            lore.add("");
        }
        
        // Stat bonuses
        if (!statBonuses.isEmpty()) {
            lore.add(ChatColor.GREEN + "Stat Bonuses:");
            for (Map.Entry<String, Integer> bonus : statBonuses.entrySet()) {
                String statName = capitalizeFirst(bonus.getKey().replace("_", " "));
                lore.add(ChatColor.GRAY + "• +" + bonus.getValue() + " " + statName);
            }
        }
        
        // Skill effect
        if (skillId != null && !skillId.isEmpty()) {
            lore.add("");
            lore.add(ChatColor.DARK_PURPLE + "Skill Effect:");
            lore.add(ChatColor.GRAY + "• Grants: " + capitalizeFirst(skillId.replace("_", " ")));
        }
        
        lore.add("");
        lore.add(ChatColor.AQUA + "Right-click to socket into equipment");
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        
        return item;
    }
    
    private Material getMaterialForType() {
        switch (type) {
            case DAMAGE:
                return Material.REDSTONE;
            case DEFENSE:
                return Material.IRON_INGOT;
            case SPEED:
                return Material.FEATHER;
            case CRITICAL:
                return Material.DIAMOND;
            case ELEMENTAL:
                return Material.BLAZE_POWDER;
            case SKILL:
                return Material.ENDER_PEARL;
            default:
                return Material.EMERALD;
        }
    }
    
    public String getDisplayName() {
        return rarity.getColor() + name;
    }
    
    private String capitalizeFirst(String str) {
        if (str == null || str.isEmpty()) return str;
        return str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase();
    }
    
    // Getters
    public String getGemId() { return gemId; }
    public GemType getType() { return type; }
    public GemRarity getRarity() { return rarity; }
    public String getName() { return name; }
    public Map<String, Integer> getStatBonuses() { return new HashMap<>(statBonuses); }
    public String getSkillId() { return skillId; }
    public String getDescription() { return description; }
    
    // Static factory methods for common gems
    public static Gem createDamageGem(GemRarity rarity) {
        Map<String, Integer> stats = new HashMap<>();
        stats.put("attack_damage", 5);
        
        return new Gem(
            "damage_gem_" + rarity.name().toLowerCase(),
            GemType.DAMAGE,
            rarity,
            "Ruby",
            stats,
            null,
            "Increases weapon damage"
        );
    }
    
    public static Gem createDefenseGem(GemRarity rarity) {
        Map<String, Integer> stats = new HashMap<>();
        stats.put("armor", 3);
        stats.put("armor_toughness", 1);
        
        return new Gem(
            "defense_gem_" + rarity.name().toLowerCase(),
            GemType.DEFENSE,
            rarity,
            "Sapphire",
            stats,
            null,
            "Increases armor and toughness"
        );
    }
    
    public static Gem createSpeedGem(GemRarity rarity) {
        Map<String, Integer> stats = new HashMap<>();
        stats.put("movement_speed", 2);
        stats.put("attack_speed", 1);
        
        return new Gem(
            "speed_gem_" + rarity.name().toLowerCase(),
            GemType.SPEED,
            rarity,
            "Topaz",
            stats,
            null,
            "Increases movement and attack speed"
        );
    }
    
    public static Gem createCriticalGem(GemRarity rarity) {
        Map<String, Integer> stats = new HashMap<>();
        stats.put("critical_chance", 5);
        stats.put("critical_damage", 10);
        
        return new Gem(
            "critical_gem_" + rarity.name().toLowerCase(),
            GemType.CRITICAL,
            rarity,
            "Diamond",
            stats,
            null,
            "Increases critical hit chance and damage"
        );
    }
    
    public static Gem createElementalGem(GemRarity rarity, String element) {
        Map<String, Integer> stats = new HashMap<>();
        stats.put(element.toLowerCase() + "_damage", 8);
        stats.put(element.toLowerCase() + "_resistance", 5);
        
        return new Gem(
            element.toLowerCase() + "_gem_" + rarity.name().toLowerCase(),
            GemType.ELEMENTAL,
            rarity,
            capitalizeFirstStatic(element) + " Crystal",
            stats,
            null,
            "Adds " + element.toLowerCase() + " damage and resistance"
        );
    }
    
    public static Gem createSkillGem(GemRarity rarity, String skillId, String skillName) {
        Map<String, Integer> stats = new HashMap<>();
        // Skill gems might have minor stat bonuses too
        stats.put("mana", 10);
        
        return new Gem(
            "skill_gem_" + skillId,
            GemType.SKILL,
            rarity,
            skillName,
            stats,
            skillId,
            "Grants the " + skillName + " skill"
        );
    }
    
    private static String capitalizeFirstStatic(String str) {
        if (str == null || str.isEmpty()) return str;
        return str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase();
    }
}
