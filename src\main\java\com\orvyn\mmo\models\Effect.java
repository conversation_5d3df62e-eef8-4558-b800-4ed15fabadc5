package com.orvyn.mmo.models;

import com.orvyn.mmo.enums.Attribute;
import org.bukkit.Particle;
import org.bukkit.Sound;

public class Effect {
    
    private final String id;
    private final String name;
    private final String description;
    private final EffectType type;
    private final EffectCategory category;
    private final int defaultDuration;
    private final boolean stackable;
    private final int maxStacks;
    
    // Damage/Heal over time properties
    private final double damagePerTick;
    private final double healPerTick;
    private final int tickInterval;
    
    // Attribute modifier properties
    private final Attribute attribute;
    private final ModifierType modifierType;
    private final double modifierValue;
    
    // Visual and audio properties
    private final Particle particle;
    private final int particleCount;
    private final String color;
    private final Sound sound;
    
    public Effect(String id, String name, String description, EffectType type, EffectCategory category,
                  int defaultDuration, boolean stackable, int maxStacks,
                  double damagePerTick, double healPerTick, int tickInterval,
                  Attribute attribute, ModifierType modifierType, double modifierValue,
                  Particle particle, int particleCount, String color, Sound sound) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.type = type;
        this.category = category;
        this.defaultDuration = defaultDuration;
        this.stackable = stackable;
        this.maxStacks = maxStacks;
        this.damagePerTick = damagePerTick;
        this.healPerTick = healPerTick;
        this.tickInterval = tickInterval;
        this.attribute = attribute;
        this.modifierType = modifierType;
        this.modifierValue = modifierValue;
        this.particle = particle;
        this.particleCount = particleCount;
        this.color = color;
        this.sound = sound;
    }
    
    // Getters
    public String getId() { return id; }
    public String getName() { return name; }
    public String getDescription() { return description; }
    public EffectType getType() { return type; }
    public EffectCategory getCategory() { return category; }
    public int getDefaultDuration() { return defaultDuration; }
    public boolean isStackable() { return stackable; }
    public int getMaxStacks() { return maxStacks; }
    public double getDamagePerTick() { return damagePerTick; }
    public double getHealPerTick() { return healPerTick; }
    public int getTickInterval() { return tickInterval; }
    public Attribute getAttribute() { return attribute; }
    public ModifierType getModifierType() { return modifierType; }
    public double getModifierValue() { return modifierValue; }
    public Particle getParticle() { return particle; }
    public int getParticleCount() { return particleCount; }
    public String getColor() { return color; }
    public Sound getSound() { return sound; }
    
    public enum EffectType {
        DAMAGE_OVER_TIME,
        HEAL_OVER_TIME,
        ATTRIBUTE_MODIFIER,
        MANA_DRAIN,
        MANA_REGENERATION,
        MOVEMENT_SPEED,
        INVISIBILITY,
        INVULNERABILITY,
        STUN,
        SILENCE,
        BLIND,
        SLOW,
        HASTE,
        WEAKNESS,
        STRENGTH_BOOST,
        FIRE_IMMUNITY,
        WATER_BREATHING,
        NIGHT_VISION,
        JUMP_BOOST,
        FALL_DAMAGE_IMMUNITY,
        THORNS,
        LIFE_STEAL,
        MANA_STEAL,
        CRITICAL_CHANCE_BOOST,
        DODGE_CHANCE_BOOST
    }

    public enum EffectCategory {
        BUFF,
        DEBUFF,
        NEUTRAL
    }
    
    public enum ModifierType {
        ADD,
        MULTIPLY
    }
}
