package com.orvyn.mmo.models;

import com.orvyn.mmo.enums.Attribute;
import java.util.Map;
import java.util.HashMap;

public class PlayerClass {
    
    private final String name;
    private final Map<Attribute, Integer> baseAttributes = new HashMap<>();
    private final Map<String, Integer> skills = new HashMap<>();
    private String description;
    
    public PlayerClass(String name) {
        this.name = name;
    }
    
    public String getName() { return name; }
    public Map<Attribute, Integer> getBaseAttributes() { return baseAttributes; }
    public Map<String, Integer> getSkills() { return skills; }
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
}
