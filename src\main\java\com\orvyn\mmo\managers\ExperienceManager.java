package com.orvyn.mmo.managers;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import com.orvyn.mmo.enums.Attribute;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.event.player.PlayerFishEvent;
import org.bukkit.event.block.BlockBreakEvent;

import java.util.HashMap;
import java.util.Map;

public class ExperienceManager implements Listener {
    
    private final OrvynMMOPlugin plugin;
    
    // XP Configuration
    private int baseXpPerLevel;
    private double xpMultiplier;
    private int maxLevel;
    
    // XP Sources
    private final Map<String, Integer> mobXpRewards = new HashMap<>();
    private final Map<String, Integer> blockXpRewards = new HashMap<>();
    private int fishingXp;
    private int questBaseXp;
    private int professionXpMultiplier;
    
    // Level-up rewards
    private final Map<String, Map<Attribute, Integer>> classLevelUpRewards = new HashMap<>();
    private int skillPointsPerLevel;
    private int attributePointsPerLevel;
    
    public ExperienceManager(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
        loadConfiguration();
        setupDefaultRewards();
    }
    
    private void loadConfiguration() {
        FileConfiguration config = plugin.getConfigHub().getConfig("config");
        if (config == null) {
            plugin.getLogger().warning("Config not found! Using default XP settings.");
            useDefaultSettings();
            return;
        }
        
        // Load XP settings
        baseXpPerLevel = config.getInt("experience.base_xp_per_level", 100);
        xpMultiplier = config.getDouble("experience.xp_multiplier", 1.5);
        maxLevel = config.getInt("experience.max_level", 100);
        
        // Load XP sources
        fishingXp = config.getInt("experience.sources.fishing", 5);
        questBaseXp = config.getInt("experience.sources.quest_base", 50);
        professionXpMultiplier = config.getInt("experience.sources.profession_multiplier", 2);
        
        // Load level-up rewards
        skillPointsPerLevel = config.getInt("experience.rewards.skill_points_per_level", 1);
        attributePointsPerLevel = config.getInt("experience.rewards.attribute_points_per_level", 2);
        
        // Load mob XP rewards
        if (config.contains("experience.mob_rewards")) {
            for (String mobType : config.getConfigurationSection("experience.mob_rewards").getKeys(false)) {
                mobXpRewards.put(mobType.toUpperCase(), config.getInt("experience.mob_rewards." + mobType));
            }
        }
        
        // Load block XP rewards
        if (config.contains("experience.block_rewards")) {
            for (String blockType : config.getConfigurationSection("experience.block_rewards").getKeys(false)) {
                blockXpRewards.put(blockType.toUpperCase(), config.getInt("experience.block_rewards." + blockType));
            }
        }
    }
    
    private void useDefaultSettings() {
        baseXpPerLevel = 100;
        xpMultiplier = 1.5;
        maxLevel = 100;
        fishingXp = 5;
        questBaseXp = 50;
        professionXpMultiplier = 2;
        skillPointsPerLevel = 1;
        attributePointsPerLevel = 2;
    }
    
    private void setupDefaultRewards() {
        // Setup default mob XP if not configured
        if (mobXpRewards.isEmpty()) {
            mobXpRewards.put("ZOMBIE", 10);
            mobXpRewards.put("SKELETON", 12);
            mobXpRewards.put("CREEPER", 15);
            mobXpRewards.put("SPIDER", 8);
            mobXpRewards.put("ENDERMAN", 25);
            mobXpRewards.put("WITCH", 20);
            mobXpRewards.put("BLAZE", 30);
            mobXpRewards.put("WITHER_SKELETON", 40);
        }
        
        // Setup default block XP if not configured
        if (blockXpRewards.isEmpty()) {
            blockXpRewards.put("COAL_ORE", 2);
            blockXpRewards.put("IRON_ORE", 5);
            blockXpRewards.put("GOLD_ORE", 8);
            blockXpRewards.put("DIAMOND_ORE", 15);
            blockXpRewards.put("EMERALD_ORE", 20);
            blockXpRewards.put("ANCIENT_DEBRIS", 50);
        }
        
        // Setup class-specific level-up rewards
        setupClassRewards();
    }
    
    private void setupClassRewards() {
        // Warrior rewards
        Map<Attribute, Integer> warriorRewards = new HashMap<>();
        warriorRewards.put(Attribute.HP, 6);
        warriorRewards.put(Attribute.MANA, 4);
        warriorRewards.put(Attribute.STR, 2);
        warriorRewards.put(Attribute.AGI, 1);
        warriorRewards.put(Attribute.INT, 1);
        classLevelUpRewards.put("warrior", warriorRewards);
        
        // Mage rewards
        Map<Attribute, Integer> mageRewards = new HashMap<>();
        mageRewards.put(Attribute.HP, 3);
        mageRewards.put(Attribute.MANA, 8);
        mageRewards.put(Attribute.STR, 1);
        mageRewards.put(Attribute.AGI, 1);
        mageRewards.put(Attribute.INT, 2);
        classLevelUpRewards.put("mage", mageRewards);
        
        // Archer rewards
        Map<Attribute, Integer> archerRewards = new HashMap<>();
        archerRewards.put(Attribute.HP, 4);
        archerRewards.put(Attribute.MANA, 6);
        archerRewards.put(Attribute.STR, 1);
        archerRewards.put(Attribute.AGI, 2);
        archerRewards.put(Attribute.INT, 1);
        classLevelUpRewards.put("archer", archerRewards);
    }
    
    public void giveExperience(Player player, int amount, String source) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player.getUniqueId());
        if (data == null) return;
        
        int oldLevel = data.getClassLevel();
        long oldExp = data.getClassExp();
        
        // Apply XP
        data.addExp(amount);
        
        // Check for level up
        int newLevel = data.getClassLevel();
        if (newLevel > oldLevel) {
            handleLevelUp(player, data, oldLevel, newLevel);
        }
        
        // Show XP gain message
        showXpGain(player, amount, source);
    }
    
    private void handleLevelUp(Player player, PlayerData data, int oldLevel, int newLevel) {
        // Apply class-specific attribute rewards
        String playerClass = data.getPlayerClass().toLowerCase();
        Map<Attribute, Integer> rewards = classLevelUpRewards.get(playerClass);
        
        if (rewards != null) {
            for (Map.Entry<Attribute, Integer> entry : rewards.entrySet()) {
                Attribute attr = entry.getKey();
                int bonus = entry.getValue();
                int currentValue = data.getBaseAttributes().getOrDefault(attr, 0);
                data.getBaseAttributes().put(attr, currentValue + bonus);
            }
        }
        
        // Recalculate resources and full heal
        data.recalculateResources();
        data.setCurrentHP(data.getMaxHP());
        data.setCurrentMana(data.getMaxMana());
        
        // Check for new skills to unlock
        plugin.getClassManager().checkLevelUpSkills(player, newLevel);

        // Show level-up effects
        showLevelUpEffects(player, newLevel);

        // Broadcast level up
        String message = ChatColor.GOLD + "✦ " + ChatColor.YELLOW + player.getName() +
                        ChatColor.GOLD + " reached level " + ChatColor.AQUA + newLevel + ChatColor.GOLD + "! ✦";
        Bukkit.broadcastMessage(message);
    }
    
    private void showLevelUpEffects(Player player, int newLevel) {
        // Sound effect
        player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.0f);
        
        // Particle effects
        player.getWorld().spawnParticle(Particle.FIREWORK, player.getLocation().add(0, 1, 0), 50, 0.5, 1, 0.5, 0.1);
        player.getWorld().spawnParticle(Particle.ENCHANT, player.getLocation().add(0, 1, 0), 30, 1, 2, 1, 0.1);
        
        // Personal message
        player.sendMessage("");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");
        player.sendMessage(ChatColor.YELLOW + "    🎉 LEVEL UP! 🎉");
        player.sendMessage(ChatColor.AQUA + "    Level " + newLevel);
        player.sendMessage(ChatColor.GREEN + "    +" + skillPointsPerLevel + " Skill Point(s)");
        player.sendMessage(ChatColor.GREEN + "    +" + attributePointsPerLevel + " Attribute Point(s)");
        player.sendMessage(ChatColor.LIGHT_PURPLE + "    Health and Mana restored!");
        player.sendMessage(ChatColor.GOLD + "═══════════════════════════════");
        player.sendMessage("");
    }
    
    private void showXpGain(Player player, int amount, String source) {
        String message = ChatColor.GREEN + "+" + amount + " XP" + 
                        (source != null ? ChatColor.GRAY + " (" + source + ")" : "");
        
        // Send as action bar for less spam
        player.spigot().sendMessage(net.md_5.bungee.api.ChatMessageType.ACTION_BAR, 
            net.md_5.bungee.api.chat.TextComponent.fromLegacyText(message));
    }
    
    public long getRequiredExpForLevel(int level) {
        if (level <= 1) return 0;
        return (long) (baseXpPerLevel * Math.pow(xpMultiplier, level - 2));
    }
    
    public long getTotalExpForLevel(int level) {
        long total = 0;
        for (int i = 2; i <= level; i++) {
            total += getRequiredExpForLevel(i);
        }
        return total;
    }
    
    public int getProgressToNextLevel(PlayerData data) {
        int currentLevel = data.getClassLevel();
        if (currentLevel >= maxLevel) return 100;
        
        long currentExp = data.getClassExp();
        long currentLevelExp = getTotalExpForLevel(currentLevel);
        long nextLevelExp = getTotalExpForLevel(currentLevel + 1);
        
        if (nextLevelExp == currentLevelExp) return 100;
        
        return (int) ((currentExp - currentLevelExp) * 100 / (nextLevelExp - currentLevelExp));
    }
    
    @EventHandler
    public void onEntityDeath(EntityDeathEvent event) {
        if (event.getEntity().getKiller() instanceof Player) {
            Player killer = event.getEntity().getKiller();
            String mobType = event.getEntity().getType().name();
            
            int xpReward = mobXpRewards.getOrDefault(mobType, 5); // Default 5 XP
            giveExperience(killer, xpReward, "Mob Kill");
        }
    }
    
    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        String blockType = event.getBlock().getType().name();
        if (blockXpRewards.containsKey(blockType)) {
            int xpReward = blockXpRewards.get(blockType);
            giveExperience(event.getPlayer(), xpReward, "Mining");
        }
    }
    
    @EventHandler
    public void onPlayerFish(PlayerFishEvent event) {
        if (event.getState() == PlayerFishEvent.State.CAUGHT_FISH) {
            giveExperience(event.getPlayer(), fishingXp, "Fishing");
        }
    }
    
    // Public methods for other managers to give XP
    public void giveQuestExperience(Player player, int questLevel) {
        int xpReward = questBaseXp + (questLevel * 10);
        giveExperience(player, xpReward, "Quest Completion");
    }
    
    public void giveProfessionExperience(Player player, String profession, int amount) {
        int xpReward = amount * professionXpMultiplier;
        giveExperience(player, xpReward, profession + " Profession");
    }
    
    public int getMaxLevel() {
        return maxLevel;
    }
}
