package com.orvyn.mmo.managers;

import com.orvyn.mmo.OrvynMMOPlugin;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.event.entity.EntitySpawnEvent;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class HolographicHealthBarManager implements Listener {
    
    private final OrvynMMOPlugin plugin;
    private final Map<UUID, ArmorStand> mobHealthBars = new ConcurrentHashMap<>();
    private final Map<UUID, Integer> mobLevels = new ConcurrentHashMap<>();
    private final Map<UUID, Double> mobOriginalHealth = new ConcurrentHashMap<>();
    private double maxViewDistance;
    private double fieldOfViewAngle;
    private int updateInterval;
    private double blocksPerLevel;
    private double healthScalingPerLevel;
    private int maxLevel;
    private BukkitRunnable updateTask;
    
    public HolographicHealthBarManager(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
        loadConfiguration();
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
        startUpdateTask();
    }

    private void loadConfiguration() {
        // Load configuration values with defaults
        maxViewDistance = plugin.getConfig().getDouble("mob_system.health_bars.view_distance", 20.0);
        fieldOfViewAngle = plugin.getConfig().getDouble("mob_system.health_bars.field_of_view_angle", 60.0);
        updateInterval = plugin.getConfig().getInt("mob_system.health_bars.update_interval", 10);
        blocksPerLevel = plugin.getConfig().getDouble("mob_system.level_system.blocks_per_level", 50.0);
        healthScalingPerLevel = plugin.getConfig().getDouble("mob_system.level_system.health_scaling_per_level", 0.2);
        maxLevel = plugin.getConfig().getInt("mob_system.level_system.max_level", 50);
    }
    
    private void startUpdateTask() {
        updateTask = new BukkitRunnable() {
            @Override
            public void run() {
                updateHealthBarsForAllPlayers();
            }
        };
        updateTask.runTaskTimer(plugin, 0L, updateInterval); // Configurable update interval
    }
    
    @EventHandler
    public void onEntitySpawn(EntitySpawnEvent event) {
        if (event.getEntity() instanceof LivingEntity && !(event.getEntity() instanceof Player) && !(event.getEntity() instanceof ArmorStand)) {
            LivingEntity mob = (LivingEntity) event.getEntity();
            setupMobLevel(mob);
        }
    }
    
    @EventHandler
    public void onEntityDamage(EntityDamageEvent event) {
        if (event.getEntity() instanceof LivingEntity && !(event.getEntity() instanceof Player) && !(event.getEntity() instanceof ArmorStand)) {
            LivingEntity mob = (LivingEntity) event.getEntity();
            // Update health bar after damage
            Bukkit.getScheduler().runTaskLater(plugin, () -> updateHealthBarForMob(mob), 1L);
        }
    }
    
    @EventHandler
    public void onEntityDeath(EntityDeathEvent event) {
        removeHealthBar(event.getEntity());
    }
    
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        // Clean up any health bars that might be visible to this player
        cleanupHealthBars();
    }
    
    @EventHandler
    public void onPlayerMove(PlayerMoveEvent event) {
        // Only update if player actually moved (not just head movement)
        if (event.getFrom().distanceSquared(event.getTo()) > 0.01) {
            updateHealthBarsForPlayer(event.getPlayer());
        }
    }
    
    private void setupMobLevel(LivingEntity mob) {
        int level = calculateMobLevel(mob);
        mobLevels.put(mob.getUniqueId(), level);
        
        // Store original health before scaling
        mobOriginalHealth.put(mob.getUniqueId(), mob.getMaxHealth());
        
        // Scale mob health based on level
        scaleMobForLevel(mob, level);
        
        // Set metadata for other systems
        mob.setMetadata("mob_level", new FixedMetadataValue(plugin, level));
        mob.setMetadata("original_health", new FixedMetadataValue(plugin, mobOriginalHealth.get(mob.getUniqueId())));
    }
    
    private int calculateMobLevel(LivingEntity mob) {
        Location spawnLocation = mob.getWorld().getSpawnLocation();
        double distance = mob.getLocation().distance(spawnLocation);

        // Level increases based on configured distance, minimum level 1
        int level = Math.max(1, (int) (distance / blocksPerLevel) + 1);
        return Math.min(level, maxLevel); // Cap at configured max level
    }
    
    private void scaleMobForLevel(LivingEntity mob, int level) {
        double originalHealth = mobOriginalHealth.get(mob.getUniqueId());
        double scaledHealth = originalHealth * (1.0 + (level - 1) * healthScalingPerLevel);

        mob.setMaxHealth(scaledHealth);
        mob.setHealth(scaledHealth);
    }
    
    private void updateHealthBarsForAllPlayers() {
        for (Player player : Bukkit.getOnlinePlayers()) {
            updateHealthBarsForPlayer(player);
        }
    }
    
    private void updateHealthBarsForPlayer(Player player) {
        Set<UUID> visibleMobs = new HashSet<>();
        
        // Find all mobs within view distance and field of view
        for (Entity entity : player.getNearbyEntities(maxViewDistance, maxViewDistance, maxViewDistance)) {
            if (entity instanceof LivingEntity && !(entity instanceof Player) && !(entity instanceof ArmorStand)) {
                LivingEntity mob = (LivingEntity) entity;
                
                if (isInFieldOfView(player, mob)) {
                    visibleMobs.add(mob.getUniqueId());
                    createOrUpdateHealthBar(mob, player);
                }
            }
        }
        
        // Remove health bars for mobs no longer in view
        removeHealthBarsNotInView(visibleMobs);
    }
    
    private boolean isInFieldOfView(Player player, LivingEntity mob) {
        Vector playerDirection = player.getLocation().getDirection().normalize();
        Vector toMob = mob.getLocation().subtract(player.getEyeLocation()).toVector().normalize();
        
        double angle = Math.toDegrees(Math.acos(playerDirection.dot(toMob)));
        return angle <= fieldOfViewAngle / 2.0;
    }
    
    private void createOrUpdateHealthBar(LivingEntity mob, Player player) {
        ArmorStand healthBar = mobHealthBars.get(mob.getUniqueId());
        
        if (healthBar == null || healthBar.isDead()) {
            // Create new health bar
            healthBar = createHealthBar(mob);
            if (healthBar != null) {
                mobHealthBars.put(mob.getUniqueId(), healthBar);
            }
        }
        
        if (healthBar != null) {
            updateHealthBarDisplay(healthBar, mob);
            updateHealthBarPosition(healthBar, mob);
        }
    }
    
    private ArmorStand createHealthBar(LivingEntity mob) {
        Location location = mob.getLocation().add(0, mob.getHeight() + 0.3, 0);
        ArmorStand armorStand = (ArmorStand) mob.getWorld().spawnEntity(location, EntityType.ARMOR_STAND);
        
        armorStand.setVisible(false);
        armorStand.setGravity(false);
        armorStand.setCanPickupItems(false);
        armorStand.setCustomNameVisible(true);
        armorStand.setMarker(true);
        armorStand.setSmall(true);
        armorStand.setInvulnerable(true);
        
        // Mark as health bar
        armorStand.setMetadata("health_bar", new FixedMetadataValue(plugin, true));
        armorStand.setMetadata("mob_uuid", new FixedMetadataValue(plugin, mob.getUniqueId().toString()));
        
        return armorStand;
    }
    
    private void updateHealthBarDisplay(ArmorStand healthBar, LivingEntity mob) {
        int level = mobLevels.getOrDefault(mob.getUniqueId(), 1);
        String mobName = mob.getCustomName() != null ? mob.getCustomName() : 
                        mob.getType().name().toLowerCase().replace("_", " ");
        
        double currentHealth = mob.getHealth();
        double maxHealth = mob.getMaxHealth();
        
        String title = formatHealthBarTitle(mobName, level, currentHealth, maxHealth);
        healthBar.setCustomName(title);
    }
    
    private void updateHealthBarPosition(ArmorStand healthBar, LivingEntity mob) {
        Location newLocation = mob.getLocation().add(0, mob.getHeight() + 0.3, 0);
        healthBar.teleport(newLocation);
    }
    
    private String formatHealthBarTitle(String mobName, int level, double currentHealth, double maxHealth) {
        ChatColor healthColor = getHealthColor(currentHealth, maxHealth);
        
        return ChatColor.YELLOW + "[Lv." + level + "] " + 
               ChatColor.WHITE + capitalizeFirst(mobName) + " " +
               healthColor + "(" + (int)currentHealth + "/" + (int)maxHealth + ")";
    }
    
    private ChatColor getHealthColor(double currentHealth, double maxHealth) {
        double healthPercent = currentHealth / maxHealth;
        
        if (healthPercent > 0.75) return ChatColor.GREEN;
        else if (healthPercent > 0.5) return ChatColor.YELLOW;
        else if (healthPercent > 0.25) return ChatColor.RED;
        else return ChatColor.DARK_RED;
    }
    
    private String capitalizeFirst(String str) {
        if (str == null || str.isEmpty()) return str;
        return str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase();
    }
    
    private void removeHealthBarsNotInView(Set<UUID> visibleMobs) {
        Iterator<Map.Entry<UUID, ArmorStand>> iterator = mobHealthBars.entrySet().iterator();
        
        while (iterator.hasNext()) {
            Map.Entry<UUID, ArmorStand> entry = iterator.next();
            UUID mobId = entry.getKey();
            ArmorStand healthBar = entry.getValue();
            
            if (!visibleMobs.contains(mobId)) {
                if (healthBar != null && !healthBar.isDead()) {
                    healthBar.remove();
                }
                iterator.remove();
            }
        }
    }
    
    private void updateHealthBarForMob(LivingEntity mob) {
        ArmorStand healthBar = mobHealthBars.get(mob.getUniqueId());
        if (healthBar != null && !healthBar.isDead()) {
            updateHealthBarDisplay(healthBar, mob);
            updateHealthBarPosition(healthBar, mob);
        }
    }
    
    private void removeHealthBar(LivingEntity mob) {
        ArmorStand healthBar = mobHealthBars.remove(mob.getUniqueId());
        if (healthBar != null && !healthBar.isDead()) {
            healthBar.remove();
        }
        
        // Clean up stored data
        mobLevels.remove(mob.getUniqueId());
        mobOriginalHealth.remove(mob.getUniqueId());
    }
    
    private void cleanupHealthBars() {
        Iterator<Map.Entry<UUID, ArmorStand>> iterator = mobHealthBars.entrySet().iterator();
        
        while (iterator.hasNext()) {
            Map.Entry<UUID, ArmorStand> entry = iterator.next();
            ArmorStand healthBar = entry.getValue();
            
            if (healthBar == null || healthBar.isDead()) {
                iterator.remove();
            }
        }
    }
    
    public void shutdown() {
        if (updateTask != null) {
            updateTask.cancel();
        }
        
        // Remove all health bars
        for (ArmorStand healthBar : mobHealthBars.values()) {
            if (healthBar != null && !healthBar.isDead()) {
                healthBar.remove();
            }
        }
        
        mobHealthBars.clear();
        mobLevels.clear();
        mobOriginalHealth.clear();
    }
    
    // Getters for other systems
    public Map<UUID, Integer> getMobLevels() { return mobLevels; }
    public Map<UUID, Double> getMobOriginalHealth() { return mobOriginalHealth; }
}
