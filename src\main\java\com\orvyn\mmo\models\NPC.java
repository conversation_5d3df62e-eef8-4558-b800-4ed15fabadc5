package com.orvyn.mmo.models;

import org.bukkit.Location;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Villager;

import java.util.List;

public class NPC {
    
    private final String id;
    private final NPCType type;
    private final EntityType entityType;
    private final String name;
    private final Location location;
    private final List<String> dialogue;
    private final Villager.Profession profession;
    private final String classFilter;
    private final String professionFilter;
    private final List<String> availableQuests;
    private final boolean glow;
    private final boolean persistent;
    
    public NPC(String id, NPCType type, EntityType entityType, String name, Location location,
               List<String> dialogue, Villager.Profession profession, String classFilter,
               String professionFilter, List<String> availableQuests, boolean glow, boolean persistent) {
        this.id = id;
        this.type = type;
        this.entityType = entityType;
        this.name = name;
        this.location = location;
        this.dialogue = dialogue;
        this.profession = profession;
        this.classFilter = classFilter;
        this.professionFilter = professionFilter;
        this.availableQuests = availableQuests;
        this.glow = glow;
        this.persistent = persistent;
    }
    
    // Getters
    public String getId() { return id; }
    public NPCType getType() { return type; }
    public EntityType getEntityType() { return entityType; }
    public String getName() { return name; }
    public Location getLocation() { return location; }
    public List<String> getDialogue() { return dialogue; }
    public Villager.Profession getProfession() { return profession; }
    public String getClassFilter() { return classFilter; }
    public String getProfessionFilter() { return professionFilter; }
    public List<String> getAvailableQuests() { return availableQuests; }
    public boolean isGlow() { return glow; }
    public boolean isPersistent() { return persistent; }
    
    public enum NPCType {
        TUTORIAL,
        CLASS_TRAINER,
        QUEST_GIVER,
        PROFESSION_TRAINER,
        WAYPOINT_GUIDE
    }
}
