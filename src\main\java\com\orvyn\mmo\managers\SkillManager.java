package com.orvyn.mmo.managers;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import com.orvyn.mmo.events.OrvynSkillUseEvent;
import com.orvyn.mmo.models.Skill;

import com.orvyn.mmo.utils.ExpressionParser;
import org.bukkit.ChatColor;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class SkillManager {
    
    private final OrvynMMOPlugin plugin;
    private final Map<String, Skill> skills = new HashMap<>();
    
    public SkillManager(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
        loadSkills();
    }
    
    public void loadSkills() {
        skills.clear();
        
        FileConfiguration config = plugin.getConfigHub().getConfig("skills.yml");
        if (config == null) return;
        
        for (String skillId : config.getKeys(false)) {
            ConfigurationSection skillSection = config.getConfigurationSection(skillId);
            if (skillSection != null) {
                Skill skill = loadSkill(skillId, skillSection);
                skills.put(skillId, skill);
            }
        }
        
        plugin.getLogger().info("Loaded " + skills.size() + " skills");
    }
    
    private Skill loadSkill(String id, ConfigurationSection section) {
        Skill skill = new Skill(id);
        
        skill.setType(section.getString("type", "custom"));
        skill.setRange(section.getDouble("range", 0));
        
        // Parse cooldown
        String cooldownStr = section.getString("cooldown", "0s");
        skill.setCooldown(parseDuration(cooldownStr));
        
        // Parse costs
        ConfigurationSection costSection = section.getConfigurationSection("cost");
        if (costSection != null) {
            skill.setManaCost(costSection.getInt("mana", 0));
        }
        
        return skill;
    }
    
    private long parseDuration(String duration) {
        if (duration.endsWith("s")) {
            return Long.parseLong(duration.substring(0, duration.length() - 1)) * 1000;
        } else if (duration.endsWith("m")) {
            return Long.parseLong(duration.substring(0, duration.length() - 1)) * 60 * 1000;
        }
        return Long.parseLong(duration);
    }
    
    public boolean useSkill(Player player, String skillId) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        if (data == null) {
            player.sendMessage(ChatColor.RED + "Unable to load your data!");
            return false;
        }

        // Check if player has the skill
        if (!data.getUnlockedSkills().contains(skillId)) {
            player.sendMessage(ChatColor.RED + "You haven't learned this skill yet!");
            return false;
        }

        // Fire event before execution
        Skill skill = skills.get(skillId);
        if (skill != null) {
            OrvynSkillUseEvent event = new OrvynSkillUseEvent(player, skill);
            plugin.getServer().getPluginManager().callEvent(event);
            if (event.isCancelled()) {
                return false;
            }
        }

        // Execute skill - SkillExecutor will handle all checks, mana consumption, and cooldowns
        return plugin.getSkillExecutor().executeSkill(player, skillId);
    }
    
    public Skill getSkill(String id) {
        return skills.get(id);
    }
    
    public void reload() {
        loadSkills();
    }

    public void shutdown() {
        // Cleanup if needed
    }
}
