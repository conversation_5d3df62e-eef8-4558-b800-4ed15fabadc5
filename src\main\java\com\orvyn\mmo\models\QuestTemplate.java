package com.orvyn.mmo.models;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

public class QuestTemplate {
    
    private final String templateId;
    private final Quest.QuestType type;
    private final String titleTemplate;
    private final String descriptionTemplate;
    private final int minLevel;
    private final int maxLevel;
    private final List<ObjectiveTemplate> objectiveTemplates;
    private final List<RewardTemplate> rewardTemplates;
    
    public QuestTemplate(String templateId, Quest.QuestType type, String titleTemplate, 
                        String descriptionTemplate, int minLevel, int maxLevel,
                        List<ObjectiveTemplate> objectiveTemplates, 
                        List<RewardTemplate> rewardTemplates) {
        this.templateId = templateId;
        this.type = type;
        this.titleTemplate = titleTemplate;
        this.descriptionTemplate = descriptionTemplate;
        this.minLevel = minLevel;
        this.maxLevel = maxLevel;
        this.objectiveTemplates = new ArrayList<>(objectiveTemplates);
        this.rewardTemplates = new ArrayList<>(rewardTemplates);
    }
    
    public Quest generateQuest(int playerLevel) {
        String questId = templateId + "_" + System.currentTimeMillis() + "_" + ThreadLocalRandom.current().nextInt(1000);
        
        // Generate objectives
        List<QuestObjective> objectives = new ArrayList<>();
        for (ObjectiveTemplate template : objectiveTemplates) {
            objectives.add(template.generateObjective());
        }
        
        // Generate rewards
        List<QuestReward> rewards = new ArrayList<>();
        for (RewardTemplate template : rewardTemplates) {
            rewards.add(template.generateReward(playerLevel));
        }
        
        // Replace placeholders in title and description
        String title = replacePlaceholders(titleTemplate, objectives);
        String description = replacePlaceholders(descriptionTemplate, objectives);
        
        // Calculate appropriate level requirement
        int questLevel = Math.max(minLevel, Math.min(maxLevel, playerLevel - 2));
        
        return new Quest(questId, type, title, description, objectives, rewards, questLevel, 0);
    }
    
    private String replacePlaceholders(String template, List<QuestObjective> objectives) {
        String result = template;
        
        // Replace common placeholders
        if (!objectives.isEmpty()) {
            QuestObjective firstObj = objectives.get(0);
            result = result.replace("{amount}", String.valueOf(firstObj.getAmount()));
            result = result.replace("{target}", firstObj.getTarget());
        }
        
        return result;
    }
    
    public String getTemplateId() { return templateId; }
    public Quest.QuestType getType() { return type; }
    public int getMinLevel() { return minLevel; }
    public int getMaxLevel() { return maxLevel; }
    
    public static class ObjectiveTemplate {
        private final Quest.ObjectiveType type;
        private final String[] possibleTargets;
        private final int minAmount;
        private final int maxAmount;
        
        public ObjectiveTemplate(Quest.ObjectiveType type, String[] possibleTargets, int minAmount, int maxAmount) {
            this.type = type;
            this.possibleTargets = possibleTargets;
            this.minAmount = minAmount;
            this.maxAmount = maxAmount;
        }
        
        public QuestObjective generateObjective() {
            String target = possibleTargets[ThreadLocalRandom.current().nextInt(possibleTargets.length)];
            int amount = ThreadLocalRandom.current().nextInt(minAmount, maxAmount + 1);
            String description = generateDescription(type, target, amount);
            
            return new QuestObjective(
                "obj_" + System.currentTimeMillis(),
                type,
                target,
                amount,
                description
            );
        }
        
        private String generateDescription(Quest.ObjectiveType type, String target, int amount) {
            switch (type) {
                case KILL_MOBS:
                    return "Kill " + amount + " " + formatTarget(target);
                case COLLECT_ITEMS:
                    return "Collect " + amount + " " + formatTarget(target);
                case MINE_BLOCKS:
                    return "Mine " + amount + " " + formatTarget(target);
                case FISH_ITEMS:
                    return "Fish " + amount + " items";
                case CRAFT_ITEMS:
                    return "Craft " + amount + " " + formatTarget(target);
                case GAIN_PROFESSION_XP:
                    return "Gain " + amount + " " + target + " XP";
                case DEAL_DAMAGE:
                    return "Deal " + amount + " damage to mobs";
                case SURVIVE_TIME:
                    return "Survive for " + (amount / 60) + " minutes";
                default:
                    return "Complete objective";
            }
        }
        
        private String formatTarget(String target) {
            return target.toLowerCase().replace("_", " ");
        }
    }
    
    public static class RewardTemplate {
        private final QuestReward.RewardType type;
        private final String target;
        private final int baseAmount;
        private final double levelMultiplier;
        
        public RewardTemplate(QuestReward.RewardType type, String target, int baseAmount, double levelMultiplier) {
            this.type = type;
            this.target = target;
            this.baseAmount = baseAmount;
            this.levelMultiplier = levelMultiplier;
        }
        
        public QuestReward generateReward(int playerLevel) {
            int amount = (int) (baseAmount + (playerLevel * levelMultiplier));
            String description = generateDescription(type, target, amount);
            
            return new QuestReward(type, target, amount, description);
        }
        
        private String generateDescription(QuestReward.RewardType type, String target, int amount) {
            switch (type) {
                case EXPERIENCE:
                    return amount + " Experience";
                case PROFESSION_XP:
                    return amount + " " + target + " XP";
                case ITEM:
                    return amount + "x " + formatTarget(target);
                case CURRENCY:
                    return amount + " " + target;
                default:
                    return "Reward";
            }
        }
        
        private String formatTarget(String target) {
            return target.toLowerCase().replace("_", " ");
        }
    }
}
