package com.orvyn.mmo.managers;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import com.orvyn.mmo.enums.Attribute;
import com.orvyn.mmo.models.ActiveEffect;
import com.orvyn.mmo.models.Effect;

import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

public class EffectManager {

    private final OrvynMMOPlugin plugin;
    private final Map<String, Effect> effects = new HashMap<>();
    private BukkitRunnable effectTask;

    public EffectManager(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
        reload();
        startEffectTask();
    }

    public void reload() {
        effects.clear();
        loadEffects();
        plugin.getLogger().info("Loaded " + effects.size() + " effects");
    }

    private void loadEffects() {
        FileConfiguration config = plugin.getConfigHub().getConfig("effects");

        if (config == null) {
            plugin.getLogger().warning("Effects configuration not found! Skipping effect loading.");
            return;
        }

        for (String effectId : config.getKeys(false)) {
            ConfigurationSection section = config.getConfigurationSection(effectId);
            if (section != null) {
                Effect effect = loadEffect(effectId, section);
                if (effect != null) {
                    effects.put(effectId, effect);
                }
            }
        }
    }

    private Effect loadEffect(String id, ConfigurationSection section) {
        try {
            String name = section.getString("name", id);
            String description = section.getString("description", "");
            String typeStr = section.getString("type", "damage_over_time");
            Effect.EffectType type = Effect.EffectType.valueOf(typeStr.toUpperCase());

            int defaultDuration = section.getInt("default_duration", 200);
            double damagePerTick = section.getDouble("damage_per_tick", 0.0);
            double healPerTick = section.getDouble("heal_per_tick", 0.0);
            int tickInterval = section.getInt("tick_interval", 20);

            // Attribute modifier properties
            Attribute attribute = null;
            String attrStr = section.getString("attribute");
            if (attrStr != null) {
                try {
                    attribute = Attribute.valueOf(attrStr.toUpperCase());
                } catch (IllegalArgumentException e) {
                    plugin.getLogger().warning("Invalid attribute for effect " + id + ": " + attrStr);
                }
            }

            Effect.ModifierType modifierType = Effect.ModifierType.ADD;
            String modTypeStr = section.getString("modifier_type", "add");
            try {
                modifierType = Effect.ModifierType.valueOf(modTypeStr.toUpperCase());
            } catch (IllegalArgumentException e) {
                plugin.getLogger().warning("Invalid modifier type for effect " + id + ": " + modTypeStr);
            }

            double modifierValue = section.getDouble("modifier_value", 0.0);

            // Visual and audio properties
            Particle particle = Particle.CRIT;
            String particleStr = section.getString("particle", "CRIT");
            try {
                particle = Particle.valueOf(particleStr.toUpperCase());
            } catch (IllegalArgumentException e) {
                plugin.getLogger().warning("Invalid particle for effect " + id + ": " + particleStr);
            }

            int particleCount = section.getInt("particle_count", 5);
            String color = section.getString("color", "§7");

            Sound sound = Sound.ENTITY_PLAYER_LEVELUP;
            String soundStr = section.getString("sound", "ENTITY_PLAYER_LEVELUP");
            try {
                sound = Sound.valueOf(soundStr.toUpperCase());
            } catch (IllegalArgumentException e) {
                plugin.getLogger().warning("Invalid sound for effect " + id + ": " + soundStr);
            }

            // New fields
            Effect.EffectCategory category = Effect.EffectCategory.NEUTRAL;
            String categoryStr = section.getString("category", "neutral");
            try {
                category = Effect.EffectCategory.valueOf(categoryStr.toUpperCase());
            } catch (IllegalArgumentException e) {
                plugin.getLogger().warning("Invalid category for effect " + id + ": " + categoryStr);
            }

            boolean stackable = section.getBoolean("stackable", false);
            int maxStacks = section.getInt("max_stacks", 1);

            return new Effect(id, name, description, type, category, defaultDuration, stackable, maxStacks,
                            damagePerTick, healPerTick, tickInterval,
                            attribute, modifierType, modifierValue,
                            particle, particleCount, color, sound);

        } catch (Exception e) {
            plugin.getLogger().warning("Failed to load effect " + id + ": " + e.getMessage());
            return null;
        }
    }

    private void startEffectTask() {
        effectTask = new BukkitRunnable() {
            @Override
            public void run() {
                processAllEffects();
            }
        };
        effectTask.runTaskTimer(plugin, 0L, 1L); // Run every tick
    }

    private void processAllEffects() {
        for (Player player : plugin.getServer().getOnlinePlayers()) {
            PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
            processPlayerEffects(player, data);
        }
    }

    private void processPlayerEffects(Player player, PlayerData data) {
        Map<String, ActiveEffect> activeEffects = data.getActiveEffects();
        Iterator<Map.Entry<String, ActiveEffect>> iterator = activeEffects.entrySet().iterator();

        while (iterator.hasNext()) {
            Map.Entry<String, ActiveEffect> entry = iterator.next();
            ActiveEffect activeEffect = entry.getValue();

            // Remove expired effects
            if (activeEffect.isExpired()) {
                removeEffect(player, data, activeEffect.getEffectId());
                iterator.remove();
                continue;
            }

            // Process effect tick
            Effect effect = effects.get(activeEffect.getEffectId());
            if (effect != null && activeEffect.shouldTick(effect.getTickInterval())) {
                processEffectTick(player, data, effect, activeEffect);
                activeEffect.markTicked();
            }

            // Spawn particles every second
            if (activeEffect.shouldTick(20)) { // Every 20 ticks = 1 second
                spawnEffectParticles(player, effect);
            }
        }
    }

    private void processEffectTick(Player player, PlayerData data, Effect effect, ActiveEffect activeEffect) {
        switch (effect.getType()) {
            case DAMAGE_OVER_TIME:
                double damage = effect.getDamagePerTick() * activeEffect.getStacks();
                double newHP = Math.max(0, data.getCurrentHP() - damage);
                data.setCurrentHP(newHP);
                player.sendMessage(effect.getColor() + "You take " + damage + " damage from " + effect.getName());
                break;

            case HEAL_OVER_TIME:
                double heal = effect.getHealPerTick() * activeEffect.getStacks();
                double newHeal = Math.min(data.getMaxHP(), data.getCurrentHP() + heal);
                data.setCurrentHP(newHeal);
                player.sendMessage(effect.getColor() + "You heal " + heal + " HP from " + effect.getName());
                break;

            case MANA_DRAIN:
                double manaDrain = effect.getDamagePerTick() * activeEffect.getStacks(); // Reuse damage field
                double newMana = Math.max(0, data.getCurrentMana() - manaDrain);
                data.setCurrentMana(newMana);
                player.sendMessage(effect.getColor() + "You lose " + manaDrain + " mana from " + effect.getName());
                break;

            case MANA_REGENERATION:
                double manaRegen = effect.getHealPerTick() * activeEffect.getStacks(); // Reuse heal field
                double newManaRegen = Math.min(data.getMaxMana(), data.getCurrentMana() + manaRegen);
                data.setCurrentMana(newManaRegen);
                player.sendMessage(effect.getColor() + "You regenerate " + manaRegen + " mana from " + effect.getName());
                break;

            case ATTRIBUTE_MODIFIER:
            case MOVEMENT_SPEED:
            case INVISIBILITY:
            case INVULNERABILITY:
            case STUN:
            case SILENCE:
            case BLIND:
            case SLOW:
            case HASTE:
            case WEAKNESS:
            case STRENGTH_BOOST:
            case FIRE_IMMUNITY:
            case WATER_BREATHING:
            case NIGHT_VISION:
            case JUMP_BOOST:
            case FALL_DAMAGE_IMMUNITY:
            case THORNS:
            case LIFE_STEAL:
            case MANA_STEAL:
            case CRITICAL_CHANCE_BOOST:
            case DODGE_CHANCE_BOOST:
                // These effects are applied when effect starts and removed when it ends
                // No tick processing needed for most of them
                break;
        }
    }

    private void spawnEffectParticles(Player player, Effect effect) {
        if (effect == null) return;

        Location loc = player.getLocation().add(0, 1, 0);
        player.getWorld().spawnParticle(effect.getParticle(), loc, effect.getParticleCount(), 0.5, 0.5, 0.5, 0.1);
    }

    public void applyEffect(Player player, String effectId, int durationTicks) {
        applyEffect(player, effectId, durationTicks, 1);
    }

    public void applyEffect(Player player, String effectId, int durationTicks, int stacks) {
        Effect effect = effects.get(effectId);
        if (effect == null) {
            plugin.getLogger().warning("Unknown effect: " + effectId);
            return;
        }

        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        if (data == null) return;

        Map<String, ActiveEffect> activeEffects = data.getActiveEffects();

        // Check if effect is already active
        if (activeEffects.containsKey(effectId)) {
            ActiveEffect existing = activeEffects.get(effectId);

            if (effect.isStackable()) {
                // Add stacks up to max
                int newStacks = Math.min(existing.getStacks() + stacks, effect.getMaxStacks());
                existing.setStacks(newStacks);
                existing.refreshDuration(durationTicks);
                player.sendMessage(effect.getColor() + "§l✓ " + effect.getName() + " stacked! (" + newStacks + "/" + effect.getMaxStacks() + ")");
            } else {
                // Just refresh duration
                existing.refreshDuration(durationTicks);
                player.sendMessage(effect.getColor() + "§l✓ " + effect.getName() + " refreshed!");
            }
        } else {
            ActiveEffect newEffect = new ActiveEffect(effectId, durationTicks, stacks);
            activeEffects.put(effectId, newEffect);
            applyEffectStart(player, data, effect);

            String stackText = effect.isStackable() && stacks > 1 ? " (" + stacks + "/" + effect.getMaxStacks() + ")" : "";
            player.sendMessage(effect.getColor() + "§l✓ " + effect.getName() + " applied!" + stackText);
            player.playSound(player.getLocation(), effect.getSound(), 1.0f, 1.0f);
        }
    }

    private void applyEffectStart(Player player, PlayerData data, Effect effect) {
        if (effect.getType() == Effect.EffectType.ATTRIBUTE_MODIFIER) {
            // Apply attribute modifier
            Attribute attr = effect.getAttribute();
            if (attr != null) {
                Map<Attribute, Integer> buffAttrs = data.getBuffAttributes();
                int currentValue = buffAttrs.getOrDefault(attr, 0);
                int baseValue = data.getBaseAttributes().getOrDefault(attr, 0);

                int modifierAmount;
                if (effect.getModifierType() == Effect.ModifierType.MULTIPLY) {
                    modifierAmount = (int) (baseValue * effect.getModifierValue());
                } else {
                    modifierAmount = (int) effect.getModifierValue();
                }

                buffAttrs.put(attr, currentValue + modifierAmount);
                data.recalculateResources();
            }
        }
    }

    private void removeEffect(Player player, PlayerData data, String effectId) {
        Effect effect = effects.get(effectId);
        if (effect != null && effect.getType() == Effect.EffectType.ATTRIBUTE_MODIFIER) {
            // Remove attribute modifier
            Attribute attr = effect.getAttribute();
            if (attr != null) {
                Map<Attribute, Integer> buffAttrs = data.getBuffAttributes();
                int currentValue = buffAttrs.getOrDefault(attr, 0);
                int baseValue = data.getBaseAttributes().getOrDefault(attr, 0);

                int modifierAmount;
                if (effect.getModifierType() == Effect.ModifierType.MULTIPLY) {
                    modifierAmount = (int) (baseValue * effect.getModifierValue());
                } else {
                    modifierAmount = (int) effect.getModifierValue();
                }

                buffAttrs.put(attr, Math.max(0, currentValue - modifierAmount));
                data.recalculateResources();
            }

            player.sendMessage(effect.getColor() + "§l✗ " + effect.getName() + " expired!");
        }
    }

    public Effect getEffect(String effectId) {
        return effects.get(effectId);
    }

    public void removeEffect(Player player, String effectId) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        if (data == null) return;

        Map<String, ActiveEffect> activeEffects = data.getActiveEffects();
        ActiveEffect activeEffect = activeEffects.remove(effectId);

        if (activeEffect != null) {
            removeEffect(player, data, effectId);
            player.sendMessage("§c§l✗ " + effectId + " removed!");
        }
    }

    public void clearAllEffects(Player player) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        if (data == null) return;

        Map<String, ActiveEffect> activeEffects = data.getActiveEffects();
        for (String effectId : activeEffects.keySet()) {
            removeEffect(player, data, effectId);
        }
        activeEffects.clear();
        player.sendMessage("§c§l✗ All effects cleared!");
    }

    public void clearEffectsByCategory(Player player, Effect.EffectCategory category) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        if (data == null) return;

        Map<String, ActiveEffect> activeEffects = data.getActiveEffects();
        activeEffects.entrySet().removeIf(entry -> {
            Effect effect = effects.get(entry.getKey());
            if (effect != null && effect.getCategory() == category) {
                removeEffect(player, data, entry.getKey());
                return true;
            }
            return false;
        });

        player.sendMessage("§c§l✗ All " + category.name().toLowerCase() + " effects cleared!");
    }

    public boolean hasEffect(Player player, String effectId) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        if (data == null) return false;
        return data.getActiveEffects().containsKey(effectId);
    }

    public boolean hasEffectCategory(Player player, Effect.EffectCategory category) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        if (data == null) return false;

        for (String effectId : data.getActiveEffects().keySet()) {
            Effect effect = effects.get(effectId);
            if (effect != null && effect.getCategory() == category) {
                return true;
            }
        }
        return false;
    }

    public void shutdown() {
        if (effectTask != null) {
            effectTask.cancel();
        }
    }
}
