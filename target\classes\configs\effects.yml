# ========================================
# OrvynMMO Effects Configuration
# ========================================
#
# This file defines status effects that can be applied to players.
# Effects can be applied through skills, items, or admin commands.
#
# Configuration Guide:
# - name: Display name of the effect
# - description: Brief description shown to players
# - type: Effect behavior type (see types below)
# - damage_per_tick/heal_per_tick: Amount per tick (for DoT/HoT effects)
# - tick_interval: Ticks between effect applications (20 ticks = 1 second)
# - default_duration: Default duration in ticks when applied
# - attribute: Which attribute to modify (for ATTRIBUTE_MODIFIER type)
# - modifier_type: ADD (flat bonus) or MULTIPLY (percentage bonus)
# - modifier_value: Amount to modify attribute by
# - particle: Visual particle effect (see Bukkit Particle enum)
# - particle_count: Number of particles to spawn
# - color: Chat color code for effect messages (§c = red, §a = green, etc.)
# - sound: Sound effect when applied (see Bukkit Sound enum)
#
# Effect Types:
# - DAMAGE_OVER_TIME: Deals damage every tick_interval
# - HEAL_OVER_TIME: Heals health every tick_interval
# - ATTRIBUTE_MODIFIER: Modifies player attributes while active
# - MANA_DRAIN: Drains mana every tick_interval
#
# Usage: /ommo effect <player> <effect_name> <duration_seconds>
# ========================================
# Define custom status effects with duration, damage/heal amounts, and visual effects

poison:
  name: "Poison"
  description: "Deals damage over time"
  type: "damage_over_time"
  category: "debuff"
  stackable: true
  max_stacks: 5
  damage_per_tick: 1.0  # Damage dealt every tick interval
  tick_interval: 40     # Ticks between damage (40 ticks = 2 seconds)
  default_duration: 200 # Default duration in ticks (200 ticks = 10 seconds)
  particle: "VILLAGER_ANGRY"
  particle_count: 5
  color: "§2"           # Dark green color for messages
  sound: "ENTITY_SPIDER_AMBIENT"

regeneration:
  name: "Regeneration"
  description: "Heals over time"
  type: "heal_over_time"
  category: "buff"
  stackable: true
  max_stacks: 3
  heal_per_tick: 1.0    # Health restored every tick interval
  tick_interval: 40     # Ticks between healing (40 ticks = 2 seconds)
  default_duration: 200 # Default duration in ticks (200 ticks = 10 seconds)
  particle: "HEART"
  particle_count: 3
  color: "§d"           # Light purple color for messages
  sound: "ENTITY_PLAYER_LEVELUP"

strength:
  name: "Strength"
  description: "Increases STR attribute by 20%"
  type: "attribute_modifier"
  category: "buff"
  stackable: false
  max_stacks: 1
  attribute: "STR"
  modifier_type: "multiply"
  modifier_value: 0.2   # 20% increase
  default_duration: 600 # Default duration in ticks (600 ticks = 30 seconds)
  particle: "CRIT"
  particle_count: 8
  color: "§c"           # Red color for messages
  sound: "ENTITY_PLAYER_ATTACK_CRIT"

speed:
  name: "Speed"
  description: "Increases AGI attribute by 20%"
  type: "attribute_modifier"
  category: "buff"
  stackable: false
  max_stacks: 1
  attribute: "AGI"
  modifier_type: "multiply"
  modifier_value: 0.2   # 20% increase
  default_duration: 600 # Default duration in ticks (600 ticks = 30 seconds)
  particle: "CLOUD"
  particle_count: 10
  color: "§f"           # White color for messages
  sound: "ENTITY_HORSE_GALLOP"

weakness:
  name: "Weakness"
  description: "Decreases STR attribute by 30%"
  type: "attribute_modifier"
  category: "debuff"
  stackable: false
  max_stacks: 1
  attribute: "STR"
  modifier_type: "multiply"
  modifier_value: -0.3  # 30% decrease
  default_duration: 300 # Default duration in ticks (300 ticks = 15 seconds)
  particle: "SMOKE_NORMAL"
  particle_count: 6
  color: "§8"           # Dark gray color for messages
  sound: "ENTITY_WITHER_AMBIENT"

# New Effects

mana_burn:
  name: "Mana Burn"
  description: "Drains mana over time"
  type: "mana_drain"
  category: "debuff"
  stackable: true
  max_stacks: 3
  damage_per_tick: 2.0  # Mana drained every tick interval
  tick_interval: 60     # Ticks between drain (60 ticks = 3 seconds)
  default_duration: 300 # Default duration in ticks (300 ticks = 15 seconds)
  particle: "WITCH"
  particle_count: 8
  color: "§5"           # Purple color for messages
  sound: "ENTITY_WITCH_AMBIENT"

mana_regen:
  name: "Mana Regeneration"
  description: "Restores mana over time"
  type: "mana_regeneration"
  category: "buff"
  stackable: true
  max_stacks: 2
  heal_per_tick: 3.0    # Mana restored every tick interval
  tick_interval: 40     # Ticks between regen (40 ticks = 2 seconds)
  default_duration: 400 # Default duration in ticks (400 ticks = 20 seconds)
  particle: "ENCHANT"
  particle_count: 5
  color: "§b"           # Aqua color for messages
  sound: "BLOCK_ENCHANTMENT_TABLE_USE"

invisibility:
  name: "Invisibility"
  description: "Makes the player invisible"
  type: "invisibility"
  category: "buff"
  stackable: false
  max_stacks: 1
  default_duration: 200 # Default duration in ticks (200 ticks = 10 seconds)
  particle: "SMOKE"
  particle_count: 10
  color: "§7"           # Gray color for messages
  sound: "ENTITY_ENDERMAN_TELEPORT"

stun:
  name: "Stun"
  description: "Prevents movement and actions"
  type: "stun"
  category: "debuff"
  stackable: false
  max_stacks: 1
  default_duration: 60  # Default duration in ticks (60 ticks = 3 seconds)
  particle: "CRIT_MAGIC"
  particle_count: 15
  color: "§4"           # Dark red color for messages
  sound: "ENTITY_ZOMBIE_ATTACK_IRON_DOOR"

haste:
  name: "Haste"
  description: "Increases HASTE attribute by 50%"
  type: "attribute_modifier"
  category: "buff"
  stackable: false
  max_stacks: 1
  attribute: "HASTE"
  modifier_type: "multiply"
  modifier_value: 0.5   # 50% increase
  default_duration: 400 # Default duration in ticks (400 ticks = 20 seconds)
  particle: "HAPPY_VILLAGER"
  particle_count: 12
  color: "§e"           # Yellow color for messages
  sound: "ENTITY_PLAYER_LEVELUP"

critical_boost:
  name: "Critical Boost"
  description: "Increases CRIT attribute by 25%"
  type: "attribute_modifier"
  category: "buff"
  stackable: false
  max_stacks: 1
  attribute: "CRIT"
  modifier_type: "multiply"
  modifier_value: 0.25  # 25% increase
  default_duration: 300 # Default duration in ticks (300 ticks = 15 seconds)
  particle: "CRIT"
  particle_count: 20
  color: "§6"           # Gold color for messages
  sound: "ENTITY_PLAYER_ATTACK_CRIT"
