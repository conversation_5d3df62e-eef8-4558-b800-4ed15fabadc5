package com.orvyn.mmo.models;

import com.orvyn.mmo.enums.Attribute;
import java.util.Map;
import java.util.HashMap;
import org.bukkit.Material;

import java.util.ArrayList;
import java.util.List;

public class ItemTemplate {
    
    private final String id;
    private Material material;
    private String tier;
    private String displayName;
    private List<String> lore = new ArrayList<>();
    private final Map<Attribute, Integer> attributes = new HashMap<>();
    private List<String> triggers = new ArrayList<>();
    
    public ItemTemplate(String id) {
        this.id = id;
    }
    
    public String getId() { return id; }
    public Material getMaterial() { return material; }
    public void setMaterial(Material material) { this.material = material; }
    public String getTier() { return tier; }
    public void setTier(String tier) { this.tier = tier; }
    public String getDisplayName() { return displayName; }
    public void setDisplayName(String displayName) { this.displayName = displayName; }
    public List<String> getLore() { return lore; }
    public void setLore(List<String> lore) { this.lore = lore; }
    public Map<Attribute, Integer> getAttributes() { return attributes; }
    public List<String> getTriggers() { return triggers; }
    public void setTriggers(List<String> triggers) { this.triggers = triggers; }
}
