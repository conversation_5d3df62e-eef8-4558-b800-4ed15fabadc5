package com.orvyn.mmo.models;

import org.bukkit.Location;

public class Waypoint {
    
    private final String name;
    private final String displayName;
    private final String description;
    private final Location location;
    private final int cost;
    private final int cooldown;
    private final boolean requiresDiscovery;
    private final double discoveryRadius;
    
    public Waypoint(String name, String displayName, String description, Location location, 
                   int cost, int cooldown, boolean requiresDiscovery, double discoveryRadius) {
        this.name = name;
        this.displayName = displayName;
        this.description = description;
        this.location = location;
        this.cost = cost;
        this.cooldown = cooldown;
        this.requiresDiscovery = requiresDiscovery;
        this.discoveryRadius = discoveryRadius;
    }
    
    public String getName() { return name; }
    public String getDisplayName() { return displayName; }
    public String getDescription() { return description; }
    public Location getLocation() { return location; }
    public int getCost() { return cost; }
    public int getCooldown() { return cooldown; }
    public boolean requiresDiscovery() { return requiresDiscovery; }
    public double getDiscoveryRadius() { return discoveryRadius; }
}
