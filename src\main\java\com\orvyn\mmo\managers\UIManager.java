package com.orvyn.mmo.managers;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import com.orvyn.mmo.data.QuestProgress;
import com.orvyn.mmo.enums.Attribute;
import com.orvyn.mmo.models.PlayerClass;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.*;

public class UIManager implements Listener {

    private final OrvynMMOPlugin plugin;
    private final Map<UUID, String> openGUIs = new HashMap<>();

    public UIManager(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    // Class Selection GUI
    public void openClassSelectionGUI(Player player) {
        Inventory gui = Bukkit.createInventory(null, 54, ChatColor.GOLD + "Choose Your Class");

        // Warrior
        ItemStack warrior = new ItemStack(Material.IRON_SWORD);
        ItemMeta warriorMeta = warrior.getItemMeta();
        warriorMeta.setDisplayName(ChatColor.RED + "Warrior");
        List<String> warriorLore = Arrays.asList(
            ChatColor.GRAY + "A mighty melee fighter",
            ChatColor.GRAY + "High HP and Strength",
            "",
            ChatColor.YELLOW + "Base Stats:",
            ChatColor.GREEN + "HP: 24",
            ChatColor.BLUE + "Mana: 60",
            ChatColor.RED + "STR: 7",
            ChatColor.GREEN + "AGI: 5",
            ChatColor.AQUA + "INT: 3",
            "",
            ChatColor.YELLOW + "Starting Skill: Arc Slash",
            ChatColor.GRAY + "Click to select!"
        );
        warriorMeta.setLore(warriorLore);
        warrior.setItemMeta(warriorMeta);
        gui.setItem(11, warrior);

        // Mage
        ItemStack mage = new ItemStack(Material.STICK);
        ItemMeta mageMeta = mage.getItemMeta();
        mageMeta.setDisplayName(ChatColor.BLUE + "Mage");
        List<String> mageLore = Arrays.asList(
            ChatColor.GRAY + "A powerful spellcaster",
            ChatColor.GRAY + "High Mana and Intelligence",
            "",
            ChatColor.YELLOW + "Base Stats:",
            ChatColor.GREEN + "HP: 18",
            ChatColor.BLUE + "Mana: 120",
            ChatColor.RED + "STR: 3",
            ChatColor.GREEN + "AGI: 4",
            ChatColor.AQUA + "INT: 8",
            "",
            ChatColor.YELLOW + "Starting Skill: Fire Bolt",
            ChatColor.GRAY + "Click to select!"
        );
        mageMeta.setLore(mageLore);
        mage.setItemMeta(mageMeta);
        gui.setItem(13, mage);

        // Archer
        ItemStack archer = new ItemStack(Material.BOW);
        ItemMeta archerMeta = archer.getItemMeta();
        archerMeta.setDisplayName(ChatColor.GREEN + "Archer");
        List<String> archerLore = Arrays.asList(
            ChatColor.GRAY + "A swift ranged attacker",
            ChatColor.GRAY + "High Agility and Critical Chance",
            "",
            ChatColor.YELLOW + "Base Stats:",
            ChatColor.GREEN + "HP: 20",
            ChatColor.BLUE + "Mana: 80",
            ChatColor.RED + "STR: 5",
            ChatColor.GREEN + "AGI: 8",
            ChatColor.AQUA + "INT: 4",
            "",
            ChatColor.YELLOW + "Starting Skill: Power Shot",
            ChatColor.GRAY + "Click to select!"
        );
        archerMeta.setLore(archerLore);
        archer.setItemMeta(archerMeta);
        gui.setItem(15, archer);

        // Rogue
        ItemStack rogue = new ItemStack(Material.IRON_SWORD);
        ItemMeta rogueMeta = rogue.getItemMeta();
        rogueMeta.setDisplayName(ChatColor.DARK_PURPLE + "Rogue");
        List<String> rogueLore = Arrays.asList(
            ChatColor.GRAY + "A stealthy assassin",
            ChatColor.GRAY + "High Agility and Critical Hit",
            "",
            ChatColor.YELLOW + "Base Stats:",
            ChatColor.GREEN + "HP: 18",
            ChatColor.BLUE + "Mana: 100",
            ChatColor.RED + "STR: 6",
            ChatColor.GREEN + "AGI: 9",
            ChatColor.AQUA + "INT: 5",
            "",
            ChatColor.YELLOW + "Starting Skill: Stealth",
            ChatColor.GRAY + "Click to select!"
        );
        rogueMeta.setLore(rogueLore);
        rogue.setItemMeta(rogueMeta);
        gui.setItem(28, rogue);

        // Paladin
        ItemStack paladin = new ItemStack(Material.IRON_AXE);
        ItemMeta paladinMeta = paladin.getItemMeta();
        paladinMeta.setDisplayName(ChatColor.GOLD + "Paladin");
        List<String> paladinLore = Arrays.asList(
            ChatColor.GRAY + "A holy warrior",
            ChatColor.GRAY + "High HP and healing abilities",
            "",
            ChatColor.YELLOW + "Base Stats:",
            ChatColor.GREEN + "HP: 26",
            ChatColor.BLUE + "Mana: 90",
            ChatColor.RED + "STR: 8",
            ChatColor.GREEN + "AGI: 4",
            ChatColor.AQUA + "INT: 6",
            "",
            ChatColor.YELLOW + "Starting Skill: Holy Strike",
            ChatColor.GRAY + "Click to select!"
        );
        paladinMeta.setLore(paladinLore);
        paladin.setItemMeta(paladinMeta);
        gui.setItem(30, paladin);

        // Druid
        ItemStack druid = new ItemStack(Material.STICK);
        ItemMeta druidMeta = druid.getItemMeta();
        druidMeta.setDisplayName(ChatColor.DARK_GREEN + "Druid");
        List<String> druidLore = Arrays.asList(
            ChatColor.GRAY + "A nature-wielding mystic",
            ChatColor.GRAY + "Balanced stats with nature magic",
            "",
            ChatColor.YELLOW + "Base Stats:",
            ChatColor.GREEN + "HP: 22",
            ChatColor.BLUE + "Mana: 110",
            ChatColor.RED + "STR: 4",
            ChatColor.GREEN + "AGI: 6",
            ChatColor.AQUA + "INT: 7",
            "",
            ChatColor.YELLOW + "Starting Skill: Nature Bolt",
            ChatColor.GRAY + "Click to select!"
        );
        druidMeta.setLore(druidLore);
        druid.setItemMeta(druidMeta);
        gui.setItem(32, druid);

        // Close button
        ItemStack close = new ItemStack(Material.BARRIER);
        ItemMeta closeMeta = close.getItemMeta();
        closeMeta.setDisplayName(ChatColor.RED + "Close");
        close.setItemMeta(closeMeta);
        gui.setItem(53, close);

        player.openInventory(gui);
        openGUIs.put(player.getUniqueId(), "class_selection");
    }

    // Stats GUI
    public void openStatsGUI(Player player) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        Inventory gui = Bukkit.createInventory(null, 54, ChatColor.GOLD + "Your Stats");

        // Class info
        ItemStack classItem = new ItemStack(Material.BOOK);
        ItemMeta classMeta = classItem.getItemMeta();
        classMeta.setDisplayName(ChatColor.YELLOW + "Class: " + data.getPlayerClass());
        List<String> classLore = Arrays.asList(
            ChatColor.GRAY + "Level: " + data.getClassLevel(),
            ChatColor.GRAY + "Experience: " + data.getClassExp(),
            ChatColor.GRAY + "Next Level: " + getRequiredExpForLevel(data.getClassLevel() + 1)
        );
        classMeta.setLore(classLore);
        classItem.setItemMeta(classMeta);
        gui.setItem(4, classItem);

        // Health
        ItemStack health = new ItemStack(Material.RED_DYE);
        ItemMeta healthMeta = health.getItemMeta();
        healthMeta.setDisplayName(ChatColor.RED + "Health");
        healthMeta.setLore(Arrays.asList(
            ChatColor.GRAY + "Current: " + (int)data.getCurrentHP(),
            ChatColor.GRAY + "Maximum: " + (int)data.getMaxHP(),
            ChatColor.GRAY + "Total HP Attribute: " + data.getTotalAttribute(Attribute.HP)
        ));
        health.setItemMeta(healthMeta);
        gui.setItem(19, health);

        // Mana
        ItemStack mana = new ItemStack(Material.BLUE_DYE);
        ItemMeta manaMeta = mana.getItemMeta();
        manaMeta.setDisplayName(ChatColor.BLUE + "Mana");
        manaMeta.setLore(Arrays.asList(
            ChatColor.GRAY + "Current: " + (int)data.getCurrentMana(),
            ChatColor.GRAY + "Maximum: " + (int)data.getMaxMana(),
            ChatColor.GRAY + "Total Mana Attribute: " + data.getTotalAttribute(Attribute.MANA)
        ));
        mana.setItemMeta(manaMeta);
        gui.setItem(20, mana);

        // Attributes
        addAttributeItem(gui, 21, Material.IRON_SWORD, "Strength", Attribute.STR, data);
        addAttributeItem(gui, 22, Material.FEATHER, "Agility", Attribute.AGI, data);
        addAttributeItem(gui, 23, Material.BOOK, "Intelligence", Attribute.INT, data);
        addAttributeItem(gui, 24, Material.DIAMOND, "Critical Chance", Attribute.CRIT, data);
        addAttributeItem(gui, 25, Material.SUGAR, "Haste", Attribute.HASTE, data);

        // Close button
        ItemStack close = new ItemStack(Material.BARRIER);
        ItemMeta closeMeta = close.getItemMeta();
        closeMeta.setDisplayName(ChatColor.RED + "Close");
        close.setItemMeta(closeMeta);
        gui.setItem(53, close);

        player.openInventory(gui);
        openGUIs.put(player.getUniqueId(), "stats");
    }

    private void addAttributeItem(Inventory gui, int slot, Material material, String name, Attribute attr, PlayerData data) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(ChatColor.YELLOW + name);
        meta.setLore(Arrays.asList(
            ChatColor.GRAY + "Base: " + data.getBaseAttributes().getOrDefault(attr, 0),
            ChatColor.GRAY + "Equipment: " + data.getEquipmentAttributes().getOrDefault(attr, 0),
            ChatColor.GRAY + "Buffs: " + data.getBuffAttributes().getOrDefault(attr, 0),
            ChatColor.GREEN + "Total: " + data.getTotalAttribute(attr)
        ));
        item.setItemMeta(meta);
        gui.setItem(slot, item);
    }

    private long getRequiredExpForLevel(int level) {
        return (long) (Math.pow(level, 2) * 100);
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        Player player = (Player) event.getWhoClicked();

        String guiType = openGUIs.get(player.getUniqueId());
        if (guiType == null) return;

        event.setCancelled(true);

        switch (guiType) {
            case "class_selection":
                handleClassSelectionClick(player, event);
                break;
            case "stats":
                handleStatsClick(player, event);
                break;
        }
    }

    private void handleClassSelectionClick(Player player, InventoryClickEvent event) {
        ItemStack clicked = event.getCurrentItem();
        if (clicked == null || !clicked.hasItemMeta()) return;

        String displayName = clicked.getItemMeta().getDisplayName();

        if (displayName.contains("Warrior")) {
            selectClass(player, "warrior");
        } else if (displayName.contains("Mage")) {
            selectClass(player, "mage");
        } else if (displayName.contains("Archer")) {
            selectClass(player, "archer");
        } else if (displayName.contains("Rogue")) {
            selectClass(player, "rogue");
        } else if (displayName.contains("Paladin")) {
            selectClass(player, "paladin");
        } else if (displayName.contains("Druid")) {
            selectClass(player, "druid");
        } else if (displayName.contains("Close")) {
            player.closeInventory();
        }
    }

    private void selectClass(Player player, String className) {
        boolean success = plugin.getClassManager().setPlayerClass(player, className);
        if (success) {
            player.closeInventory();
            player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.0f);
            player.sendMessage(ChatColor.GREEN + "You are now a " + className + "!");

            // Give starting items
            giveStartingItems(player, className);

            // Show stats GUI after selection
            plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                openStatsGUI(player);
            }, 20L);
        } else {
            player.sendMessage(ChatColor.RED + "Failed to set class!");
        }
    }

    private void giveStartingItems(Player player, String className) {
        switch (className) {
            case "warrior":
                plugin.getItemManager().giveItem(player, "iron_training_blade");
                break;
            case "mage":
                plugin.getItemManager().giveItem(player, "mage_staff");
                break;
            case "archer":
                plugin.getItemManager().giveItem(player, "hunter_bow");
                player.getInventory().addItem(new ItemStack(Material.ARROW, 64));
                break;
        }
    }

    private void handleStatsClick(Player player, InventoryClickEvent event) {
        ItemStack clicked = event.getCurrentItem();
        if (clicked == null || !clicked.hasItemMeta()) return;

        String displayName = clicked.getItemMeta().getDisplayName();

        if (displayName.contains("Close")) {
            player.closeInventory();
        }
    }

    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (event.getPlayer() instanceof Player) {
            Player player = (Player) event.getPlayer();
            openGUIs.remove(player.getUniqueId());
        }
    }

    public void shutdown() {
        openGUIs.clear();
    }
}
