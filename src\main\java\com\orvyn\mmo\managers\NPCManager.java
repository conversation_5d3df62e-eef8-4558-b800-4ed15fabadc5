package com.orvyn.mmo.managers;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import com.orvyn.mmo.models.NPC;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.entity.Villager;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerInteractEntityEvent;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.*;

public class NPCManager implements Listener {
    
    private final OrvynMMOPlugin plugin;
    private final Map<String, NPC> npcs = new HashMap<>();
    private final Map<Entity, String> spawnedNPCs = new HashMap<>();
    private final Map<UUID, Long> lastInteraction = new HashMap<>();
    private final Map<UUID, Integer> dialogueProgress = new HashMap<>();
    
    // Global settings
    private long dialogueDelay = 2000;
    private long interactionCooldown = 1000;
    private double maxInteractionDistance = 5.0;
    private double despawnDistance = 100.0;
    private long respawnTime = 300000;
    
    public NPCManager(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
        reload();
        startNPCTask();
    }
    
    public void reload() {
        // Despawn existing NPCs
        for (Entity entity : spawnedNPCs.keySet()) {
            entity.remove();
        }
        spawnedNPCs.clear();
        npcs.clear();
        lastInteraction.clear();
        dialogueProgress.clear();
        
        loadNPCs();
        spawnAllNPCs();
        plugin.getLogger().info("Loaded " + npcs.size() + " NPCs");
    }
    
    private void loadNPCs() {
        FileConfiguration config = plugin.getConfigHub().getConfig("npcs");

        if (config == null) {
            plugin.getLogger().warning("NPCs configuration not found! Skipping NPC loading.");
            return;
        }

        // Load global settings
        ConfigurationSection globalSection = config.getConfigurationSection("global_settings");
        if (globalSection != null) {
            dialogueDelay = globalSection.getLong("dialogue_delay", 2000);
            interactionCooldown = globalSection.getLong("interaction_cooldown", 1000);
            maxInteractionDistance = globalSection.getDouble("max_interaction_distance", 5.0);
            despawnDistance = globalSection.getDouble("despawn_distance", 100.0);
            respawnTime = globalSection.getLong("respawn_time", 300000);
        }
        
        // Load NPCs
        for (String npcId : config.getKeys(false)) {
            if (npcId.equals("global_settings")) continue;
            
            ConfigurationSection section = config.getConfigurationSection(npcId);
            if (section != null) {
                NPC npc = loadNPC(npcId, section);
                if (npc != null) {
                    npcs.put(npcId, npc);
                }
            }
        }
    }
    
    private NPC loadNPC(String id, ConfigurationSection section) {
        try {
            String typeStr = section.getString("type", "tutorial");
            NPC.NPCType type = NPC.NPCType.valueOf(typeStr.toUpperCase());
            
            String entityTypeStr = section.getString("entity_type", "VILLAGER");
            EntityType entityType = EntityType.valueOf(entityTypeStr.toUpperCase());
            
            String name = section.getString("name", id);
            
            // Load location
            ConfigurationSection locSection = section.getConfigurationSection("location");
            if (locSection == null) {
                plugin.getLogger().warning("No location defined for NPC: " + id);
                return null;
            }
            
            String worldName = locSection.getString("world");
            World world = plugin.getServer().getWorld(worldName);
            if (world == null) {
                plugin.getLogger().warning("World not found for NPC " + id + ": " + worldName);
                return null;
            }
            
            Location location = new Location(world,
                locSection.getDouble("x"),
                locSection.getDouble("y"),
                locSection.getDouble("z"),
                (float) locSection.getDouble("yaw", 0.0),
                (float) locSection.getDouble("pitch", 0.0));
            
            List<String> dialogue = section.getStringList("dialogue");
            
            String professionStr = section.getString("profession", "FARMER");
            Villager.Profession profession = Villager.Profession.valueOf(professionStr.toUpperCase());
            
            String classFilter = section.getString("class_filter");
            String professionFilter = section.getString("profession_filter");
            List<String> availableQuests = section.getStringList("available_quests");
            boolean glow = section.getBoolean("glow", false);
            boolean persistent = section.getBoolean("persistent", true);
            
            return new NPC(id, type, entityType, name, location, dialogue, profession,
                          classFilter, professionFilter, availableQuests, glow, persistent);
            
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to load NPC " + id + ": " + e.getMessage());
            return null;
        }
    }
    
    private void spawnAllNPCs() {
        for (NPC npc : npcs.values()) {
            spawnNPC(npc);
        }
    }
    
    private void spawnNPC(NPC npc) {
        Entity entity = npc.getLocation().getWorld().spawnEntity(npc.getLocation(), npc.getEntityType());
        
        if (entity instanceof Villager) {
            Villager villager = (Villager) entity;
            villager.setProfession(npc.getProfession());
            villager.setAI(false); // Prevent movement
        }
        
        // Set custom name
        entity.setCustomName(ChatColor.translateAlternateColorCodes('§', npc.getName()));
        entity.setCustomNameVisible(true);
        
        // Set glow effect
        if (npc.isGlow()) {
            entity.setGlowing(true);
        }
        
        // Make persistent
        if (npc.isPersistent()) {
            entity.setPersistent(true);
        }
        
        spawnedNPCs.put(entity, npc.getId());
    }
    
    private void startNPCTask() {
        new BukkitRunnable() {
            @Override
            public void run() {
                checkNPCDespawn();
            }
        }.runTaskTimer(plugin, 0L, 1200L); // Check every minute
    }
    
    private void checkNPCDespawn() {
        Iterator<Map.Entry<Entity, String>> iterator = spawnedNPCs.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Entity, String> entry = iterator.next();
            Entity entity = entry.getKey();
            
            if (entity.isDead() || !entity.isValid()) {
                iterator.remove();
                continue;
            }
            
            // Check if players are nearby
            boolean playerNearby = false;
            for (Player player : plugin.getServer().getOnlinePlayers()) {
                if (player.getLocation().distance(entity.getLocation()) <= despawnDistance) {
                    playerNearby = true;
                    break;
                }
            }
            
            if (!playerNearby) {
                NPC npc = npcs.get(entry.getValue());
                if (npc != null && !npc.isPersistent()) {
                    entity.remove();
                    iterator.remove();
                    
                    // Schedule respawn
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            spawnNPC(npc);
                        }
                    }.runTaskLater(plugin, respawnTime / 50); // Convert ms to ticks
                }
            }
        }
    }
    
    @EventHandler
    public void onPlayerInteractEntity(PlayerInteractEntityEvent event) {
        Player player = event.getPlayer();
        Entity entity = event.getRightClicked();
        
        String npcId = spawnedNPCs.get(entity);
        if (npcId == null) return;
        
        NPC npc = npcs.get(npcId);
        if (npc == null) return;
        
        event.setCancelled(true);
        
        // Check interaction cooldown
        long currentTime = System.currentTimeMillis();
        long lastTime = lastInteraction.getOrDefault(player.getUniqueId(), 0L);
        if (currentTime - lastTime < interactionCooldown) {
            return;
        }
        
        // Check distance
        if (player.getLocation().distance(entity.getLocation()) > maxInteractionDistance) {
            player.sendMessage(ChatColor.RED + "You are too far away!");
            return;
        }
        
        lastInteraction.put(player.getUniqueId(), currentTime);
        handleNPCInteraction(player, npc);
    }
    
    private void handleNPCInteraction(Player player, NPC npc) {
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        
        // Check filters
        if (npc.getClassFilter() != null && !npc.getClassFilter().equalsIgnoreCase(data.getPlayerClass())) {
            player.sendMessage(ChatColor.RED + "This trainer is not for your class!");
            return;
        }
        
        if (npc.getProfessionFilter() != null) {
            // Check if player has the profession (implementation depends on profession system)
            player.sendMessage(ChatColor.YELLOW + "You need to be a " + npc.getProfessionFilter() + " to talk to this trainer!");
            return;
        }
        
        // Handle different NPC types
        switch (npc.getType()) {
            case TUTORIAL:
                handleTutorialNPC(player, npc);
                break;
            case CLASS_TRAINER:
                handleClassTrainerNPC(player, npc);
                break;
            case QUEST_GIVER:
                handleQuestGiverNPC(player, npc);
                break;
            case PROFESSION_TRAINER:
                handleProfessionTrainerNPC(player, npc);
                break;
            case WAYPOINT_GUIDE:
                handleWaypointGuideNPC(player, npc);
                break;
        }
    }
    
    private void handleTutorialNPC(Player player, NPC npc) {
        startDialogue(player, npc, () -> {
            // After dialogue, open class selection GUI
            plugin.getUIManager().openClassSelectionGUI(player);
        });
    }
    
    private void handleClassTrainerNPC(Player player, NPC npc) {
        // Check if player has already chosen a class
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(player);
        String playerClass = data.getPlayerClass();

        if (playerClass == null || playerClass.equals("none")) {
            // New player - show welcome message
            player.sendMessage("");
            player.sendMessage(ChatColor.GOLD + "✨ " + ChatColor.YELLOW + "Welcome, new adventurer!" + ChatColor.GOLD + " ✨");
            player.sendMessage(ChatColor.AQUA + "Choose your class to begin your journey:");
            player.sendMessage(ChatColor.GRAY + "• " + ChatColor.RED + "Warrior" + ChatColor.GRAY + " - Strong melee fighter");
            player.sendMessage(ChatColor.GRAY + "• " + ChatColor.BLUE + "Mage" + ChatColor.GRAY + " - Powerful spellcaster");
            player.sendMessage(ChatColor.GRAY + "• " + ChatColor.GREEN + "Archer" + ChatColor.GRAY + " - Skilled ranged combatant");
            player.sendMessage("");
        } else {
            // Existing player - show current class
            player.sendMessage("");
            player.sendMessage(ChatColor.GOLD + "✨ " + ChatColor.YELLOW + "Welcome back, " + playerClass + "!" + ChatColor.GOLD + " ✨");
            player.sendMessage(ChatColor.AQUA + "Would you like to change your class?");
            player.sendMessage("");
        }

        startDialogue(player, npc, () -> {
            // After dialogue, open class selection GUI
            plugin.getUIManager().openClassSelectionGUI(player);
        });
    }

    private void handleQuestGiverNPC(Player player, NPC npc) {
        player.sendMessage("");
        player.sendMessage(ChatColor.GOLD + "✨ " + ChatColor.YELLOW + "Quest Master" + ChatColor.GOLD + " ✨");
        player.sendMessage(ChatColor.AQUA + "Ready for adventure? I have exciting quests waiting for you!");
        player.sendMessage("");

        startDialogue(player, npc, () -> {
            // After dialogue, open quest GUI
            try {
                // Use the new quest system
                plugin.getServer().dispatchCommand(player, "mychar quests");
            } catch (Exception e) {
                player.sendMessage(ChatColor.GREEN + "Quest system is available! Use " + ChatColor.WHITE + "/mychar quests" + ChatColor.GREEN + " to view your quests.");
            }
        });
    }

    private void handleProfessionTrainerNPC(Player player, NPC npc) {
        player.sendMessage("");
        player.sendMessage(ChatColor.GOLD + "✨ " + ChatColor.YELLOW + "Profession Trainer" + ChatColor.GOLD + " ✨");
        player.sendMessage(ChatColor.AQUA + "Ready to master various professions? I can help you improve!");
        player.sendMessage("");

        startDialogue(player, npc, () -> {
            // After dialogue, open professions GUI
            try {
                plugin.getServer().dispatchCommand(player, "mychar professions");
            } catch (Exception e) {
                player.sendMessage(ChatColor.GREEN + "Profession training available! Use " + ChatColor.WHITE + "/mychar professions" + ChatColor.GREEN + " to view your progress.");
            }
        });
    }

    private void handleWaypointGuideNPC(Player player, NPC npc) {
        startDialogue(player, npc, () -> {
            // After dialogue, show waypoint message
            player.sendMessage(ChatColor.GREEN + "Waypoint system available! Use /ommo waypoints to manage your travel points.");
        });
    }
    
    private void startDialogue(Player player, NPC npc, Runnable onComplete) {
        List<String> dialogue = npc.getDialogue();
        if (dialogue.isEmpty()) {
            if (onComplete != null) onComplete.run();
            return;
        }
        
        new BukkitRunnable() {
            int index = 0;
            
            @Override
            public void run() {
                if (index >= dialogue.size()) {
                    cancel();
                    if (onComplete != null) onComplete.run();
                    return;
                }
                
                String message = ChatColor.translateAlternateColorCodes('§', dialogue.get(index));
                player.sendMessage(ChatColor.GRAY + "[" + npc.getName() + ChatColor.GRAY + "] " + message);
                index++;
            }
        }.runTaskTimer(plugin, 0L, dialogueDelay / 50); // Convert ms to ticks
    }
    
    // Method to add and spawn a new NPC directly
    public boolean addAndSpawnNPC(NPC npc) {
        try {
            npcs.put(npc.getId(), npc);
            spawnNPC(npc);
            plugin.getLogger().info("Successfully spawned NPC: " + npc.getId() + " at " +
                npc.getLocation().getBlockX() + ", " + npc.getLocation().getBlockY() + ", " + npc.getLocation().getBlockZ());
            return true;
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to spawn NPC " + npc.getId() + ": " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    // Method to remove an NPC by ID
    public boolean removeNPC(String npcId) {
        try {
            NPC npc = npcs.remove(npcId);
            if (npc == null) {
                return false;
            }

            // Find and remove the spawned entity
            Entity entityToRemove = null;
            for (Map.Entry<Entity, String> entry : spawnedNPCs.entrySet()) {
                if (entry.getValue().equals(npcId)) {
                    entityToRemove = entry.getKey();
                    break;
                }
            }

            if (entityToRemove != null) {
                entityToRemove.remove();
                spawnedNPCs.remove(entityToRemove);
            }

            plugin.getLogger().info("Successfully removed NPC: " + npcId);
            return true;
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to remove NPC " + npcId + ": " + e.getMessage());
            return false;
        }
    }

    // Method to get NPC by entity
    public NPC getNPCByEntity(Entity entity) {
        String npcId = spawnedNPCs.get(entity);
        return npcId != null ? npcs.get(npcId) : null;
    }

    public void shutdown() {
        for (Entity entity : spawnedNPCs.keySet()) {
            entity.remove();
        }
        spawnedNPCs.clear();
    }

    // Getters
    public Map<String, NPC> getNPCs() { return npcs; }
    public Map<Entity, String> getSpawnedNPCs() { return spawnedNPCs; }
}
