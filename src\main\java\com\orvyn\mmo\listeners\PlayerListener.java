package com.orvyn.mmo.listeners;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;

public class PlayerListener implements Listener {
    
    private final OrvynMMOPlugin plugin;
    
    public PlayerListener(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        // Load player data
        PlayerData data = plugin.getPlayerDataManager().getPlayerData(event.getPlayer());

        // Initialize if new player
        if (data.getClassLevel() == 0) {
            data.setClassLevel(1);
        }

        // Start HUD display
        plugin.getHUDManager().startHUD(event.getPlayer());
    }
    
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        // Stop HUD display
        plugin.getHUDManager().stopHUD(event.getPlayer());

        // Save and unload player data
        plugin.getPlayerDataManager().unloadPlayerData(event.getPlayer().getUniqueId());
    }
}
