package com.orvyn.mmo.managers;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.io.*;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;

public class PlayerDataManager {
    
    private final OrvynMMOPlugin plugin;
    private final Map<UUID, PlayerData> playerDataCache = new ConcurrentHashMap<>();
    private final File dataFolder;
    
    public PlayerDataManager(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
        this.dataFolder = new File(plugin.getDataFolder(), "playerdata");
        
        if (!dataFolder.exists()) {
            dataFolder.mkdirs();
        }
        
        // Start auto-save task
        startAutoSaveTask();
    }
    
    private void startAutoSaveTask() {
        new BukkitRunnable() {
            @Override
            public void run() {
                saveAllDirtyPlayers();
            }
        }.runTaskTimerAsynchronously(plugin, 20 * 60, 20 * 60); // Every minute
    }
    
    public PlayerData getPlayerData(UUID playerId) {
        return playerDataCache.computeIfAbsent(playerId, this::loadPlayerData);
    }
    
    public PlayerData getPlayerData(Player player) {
        return getPlayerData(player.getUniqueId());
    }
    
    private PlayerData loadPlayerData(UUID playerId) {
        File playerFile = new File(dataFolder, playerId.toString() + ".dat");
        
        if (playerFile.exists()) {
            try (ObjectInputStream ois = new ObjectInputStream(new FileInputStream(playerFile))) {
                return (PlayerData) ois.readObject();
            } catch (Exception e) {
                plugin.getLogger().log(Level.WARNING, "Failed to load player data for " + playerId + ", creating new", e);
            }
        }
        
        return new PlayerData(playerId);
    }
    
    public void savePlayerData(UUID playerId) {
        PlayerData data = playerDataCache.get(playerId);
        if (data != null && data.isDirty()) {
            savePlayerDataAsync(data);
        }
    }
    
    private void savePlayerDataAsync(PlayerData data) {
        // Save asynchronously to avoid blocking main thread
        plugin.getServer().getScheduler().runTaskAsynchronously(plugin, () -> {
            savePlayerDataSync(data);
        });
    }
    
    private void savePlayerDataSync(PlayerData data) {
        File playerFile = new File(dataFolder, data.getPlayerId().toString() + ".dat");
        
        try (ObjectOutputStream oos = new ObjectOutputStream(new FileOutputStream(playerFile))) {
            oos.writeObject(data);
            data.markClean();
        } catch (IOException e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to save player data for " + data.getPlayerId(), e);
        }
    }
    
    public void saveAllDirtyPlayers() {
        for (PlayerData data : playerDataCache.values()) {
            if (data.isDirty()) {
                savePlayerDataSync(data);
            }
        }
    }
    
    public void saveAllPlayers() {
        for (PlayerData data : playerDataCache.values()) {
            savePlayerDataSync(data);
        }
    }
    
    public void unloadPlayerData(UUID playerId) {
        PlayerData data = playerDataCache.get(playerId);
        if (data != null) {
            if (data.isDirty()) {
                savePlayerDataSync(data);
            }
            playerDataCache.remove(playerId);
        }
    }
    
    public void shutdown() {
        saveAllPlayers();
        playerDataCache.clear();
    }
}
