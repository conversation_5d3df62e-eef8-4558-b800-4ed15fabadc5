package com.orvyn.mmo.managers;

import com.orvyn.mmo.OrvynMMOPlugin;
import com.orvyn.mmo.data.PlayerData;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.io.*;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;

public class PlayerDataManager {

    private final OrvynMMOPlugin plugin;
    private final Map<UUID, PlayerData> playerDataCache = new ConcurrentHashMap<>();
    private final File dataFolder;
    private org.bukkit.scheduler.BukkitTask autoSaveTask;
    
    public PlayerDataManager(OrvynMMOPlugin plugin) {
        this.plugin = plugin;
        this.dataFolder = new File(plugin.getDataFolder(), "playerdata");
        
        if (!dataFolder.exists()) {
            dataFolder.mkdirs();
        }

        // Create version tracking file
        createVersionFile();

        // Start auto-save task
        startAutoSaveTask();
    }
    
    private void startAutoSaveTask() {
        autoSaveTask = new BukkitRunnable() {
            @Override
            public void run() {
                saveAllDirtyPlayers();
            }
        }.runTaskTimerAsynchronously(plugin, 20 * 60, 20 * 60); // Every minute

        plugin.getLogger().info("Started auto-save task for player data (saves every minute)");
    }

    // Create a version file to track data format versions
    private void createVersionFile() {
        File versionFile = new File(dataFolder, "data_version.txt");
        try {
            if (!versionFile.exists()) {
                versionFile.createNewFile();
                try (java.io.FileWriter writer = new java.io.FileWriter(versionFile)) {
                    writer.write("OrvynMMO Player Data Version: 1.0\n");
                    writer.write("Created: " + new java.util.Date() + "\n");
                    writer.write("Plugin Version: " + plugin.getDescription().getVersion() + "\n");
                }
                plugin.getLogger().info("Created player data version file");
            }
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to create version file: " + e.getMessage());
        }
    }
    
    public PlayerData getPlayerData(UUID playerId) {
        return playerDataCache.computeIfAbsent(playerId, this::loadPlayerData);
    }
    
    public PlayerData getPlayerData(Player player) {
        return getPlayerData(player.getUniqueId());
    }
    
    private PlayerData loadPlayerData(UUID playerId) {
        File playerFile = new File(dataFolder, playerId.toString() + ".dat");

        if (playerFile.exists()) {
            try (ObjectInputStream ois = new ObjectInputStream(new FileInputStream(playerFile))) {
                PlayerData data = (PlayerData) ois.readObject();
                plugin.getLogger().info("Loaded existing player data for " + playerId);

                // Migrate old data if needed
                migratePlayerData(data);

                // Validate data integrity
                validatePlayerData(data);

                return data;
            } catch (Exception e) {
                plugin.getLogger().log(Level.WARNING, "Failed to load player data for " + playerId + " (possibly due to version change), attempting backup and creating new", e);

                // Try to backup the corrupted file
                backupCorruptedPlayerData(playerFile, playerId);
            }
        }

        // Create new player with configuration-based stats
        plugin.getLogger().info("Creating new player data for " + playerId);
        return PlayerData.createNewPlayer(playerId, plugin);
    }

    // Migrate player data for compatibility across plugin updates
    private void migratePlayerData(PlayerData data) {
        // Check if player data needs migration (e.g., missing attributes from config)
        try {
            org.bukkit.configuration.file.FileConfiguration config = plugin.getConfigHub().getConfig("classes.yml");
            if (config != null) {
                String playerClass = data.getPlayerClass();
                org.bukkit.configuration.ConfigurationSection classSection = config.getConfigurationSection(playerClass);
                if (classSection != null) {
                    org.bukkit.configuration.ConfigurationSection baseSection = classSection.getConfigurationSection("base");
                    if (baseSection != null) {
                        boolean needsUpdate = false;

                        // Check if any config attributes are missing from player data
                        for (String attrName : baseSection.getKeys(false)) {
                            com.orvyn.mmo.enums.Attribute attr = com.orvyn.mmo.enums.Attribute.fromString(attrName);
                            if (attr != null && !data.getBaseAttributes().containsKey(attr)) {
                                int value = baseSection.getInt(attrName);
                                data.getBaseAttributes().put(attr, value);
                                needsUpdate = true;
                            }
                        }

                        if (needsUpdate) {
                            data.recalculateResources();
                            plugin.getLogger().info("Migrated player data for " + data.getPlayerId() + " - added missing attributes");
                        }
                    }
                }
            }
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to migrate player data for " + data.getPlayerId() + ": " + e.getMessage());
        }
    }

    // Backup corrupted player data files
    private void backupCorruptedPlayerData(File originalFile, UUID playerId) {
        try {
            File backupDir = new File(dataFolder, "corrupted_backups");
            if (!backupDir.exists()) {
                backupDir.mkdirs();
            }

            String timestamp = String.valueOf(System.currentTimeMillis());
            File backupFile = new File(backupDir, playerId.toString() + "_" + timestamp + ".dat.backup");

            java.nio.file.Files.copy(originalFile.toPath(), backupFile.toPath());
            plugin.getLogger().info("Backed up corrupted player data to: " + backupFile.getName());

            // Delete the corrupted original
            originalFile.delete();

        } catch (Exception e) {
            plugin.getLogger().warning("Failed to backup corrupted player data for " + playerId + ": " + e.getMessage());
        }
    }
    
    public void savePlayerData(UUID playerId) {
        PlayerData data = playerDataCache.get(playerId);
        if (data != null && data.isDirty()) {
            savePlayerDataAsync(data);
        }
    }
    
    private void savePlayerDataAsync(PlayerData data) {
        // Save asynchronously to avoid blocking main thread
        plugin.getServer().getScheduler().runTaskAsynchronously(plugin, () -> {
            savePlayerDataSync(data);
        });
    }
    
    private void savePlayerDataSync(PlayerData data) {
        File playerFile = new File(dataFolder, data.getPlayerId().toString() + ".dat");
        File tempFile = new File(dataFolder, data.getPlayerId().toString() + ".dat.tmp");

        try {
            // Write to temporary file first
            try (ObjectOutputStream oos = new ObjectOutputStream(new FileOutputStream(tempFile))) {
                oos.writeObject(data);
                oos.flush();
            }

            // Atomic move - replace old file with new one
            if (playerFile.exists()) {
                playerFile.delete();
            }

            if (tempFile.renameTo(playerFile)) {
                data.markClean();
                plugin.getLogger().fine("Successfully saved player data for " + data.getPlayerId());
            } else {
                throw new IOException("Failed to rename temporary file to final file");
            }

        } catch (IOException e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to save player data for " + data.getPlayerId(), e);

            // Clean up temporary file if it exists
            if (tempFile.exists()) {
                tempFile.delete();
            }
        }
    }
    
    public void saveAllDirtyPlayers() {
        for (PlayerData data : playerDataCache.values()) {
            if (data.isDirty()) {
                savePlayerDataSync(data);
            }
        }
    }
    
    public void saveAllPlayers() {
        for (PlayerData data : playerDataCache.values()) {
            savePlayerDataSync(data);
        }
    }
    
    public void unloadPlayerData(UUID playerId) {
        PlayerData data = playerDataCache.get(playerId);
        if (data != null) {
            if (data.isDirty()) {
                savePlayerDataSync(data);
            }
            playerDataCache.remove(playerId);
        }
    }
    
    public void shutdown() {
        plugin.getLogger().info("Shutting down PlayerDataManager - saving all player data...");

        // Cancel auto-save task
        if (autoSaveTask != null) {
            autoSaveTask.cancel();
        }

        // Force save all players synchronously
        int savedCount = 0;
        for (PlayerData data : playerDataCache.values()) {
            savePlayerDataSync(data);
            savedCount++;
        }

        plugin.getLogger().info("Saved " + savedCount + " player data files during shutdown");
        playerDataCache.clear();
    }


    // Method to update all online players' base stats from configuration
    public void updateAllPlayersFromConfig() {
        plugin.getLogger().info("Updating all online players' stats from configuration...");

        for (PlayerData data : playerDataCache.values()) {
            updatePlayerFromConfig(data);
        }

        plugin.getLogger().info("Updated " + playerDataCache.size() + " players' stats from configuration");
    }

    // Validate and repair player data integrity
    public void validatePlayerData(PlayerData data) {
        boolean needsRepair = false;

        // Ensure player has a valid class
        if (data.getPlayerClass() == null || data.getPlayerClass().isEmpty()) {
            data.setPlayerClass("warrior");
            needsRepair = true;
            plugin.getLogger().warning("Repaired missing class for player " + data.getPlayerId());
        }

        // Ensure player has minimum level
        if (data.getClassLevel() < 1) {
            data.setClassLevel(1);
            needsRepair = true;
            plugin.getLogger().warning("Repaired invalid level for player " + data.getPlayerId());
        }

        // Ensure player has base attributes
        if (data.getBaseAttributes().isEmpty()) {
            updatePlayerFromConfig(data);
            needsRepair = true;
            plugin.getLogger().warning("Repaired missing attributes for player " + data.getPlayerId());
        }

        // Ensure resources are valid
        if (data.getMaxHP() <= 0 || data.getMaxMana() <= 0) {
            data.recalculateResources();
            needsRepair = true;
            plugin.getLogger().warning("Repaired invalid resources for player " + data.getPlayerId());
        }

        // Ensure current HP/Mana are within bounds
        if (data.getCurrentHP() > data.getMaxHP()) {
            data.setCurrentHP(data.getMaxHP());
            needsRepair = true;
        }

        if (data.getCurrentMana() > data.getMaxMana()) {
            data.setCurrentMana(data.getMaxMana());
            needsRepair = true;
        }

        if (needsRepair) {
            plugin.getLogger().info("Validated and repaired player data for " + data.getPlayerId());
        }
    }

    // Update a single player's base stats from configuration
    private void updatePlayerFromConfig(PlayerData data) {
        try {
            org.bukkit.configuration.file.FileConfiguration config = plugin.getConfigHub().getConfig("classes.yml");
            if (config != null) {
                String playerClass = data.getPlayerClass();
                org.bukkit.configuration.ConfigurationSection classSection = config.getConfigurationSection(playerClass);
                if (classSection != null) {
                    org.bukkit.configuration.ConfigurationSection baseSection = classSection.getConfigurationSection("base");
                    if (baseSection != null) {
                        // Store current HP/Mana percentages
                        double hpPercent = data.getMaxHP() > 0 ? data.getCurrentHP() / data.getMaxHP() : 1.0;
                        double manaPercent = data.getMaxMana() > 0 ? data.getCurrentMana() / data.getMaxMana() : 1.0;

                        // Update base attributes from config
                        data.getBaseAttributes().clear();
                        for (String attrName : baseSection.getKeys(false)) {
                            com.orvyn.mmo.enums.Attribute attr = com.orvyn.mmo.enums.Attribute.fromString(attrName);
                            if (attr != null) {
                                int value = baseSection.getInt(attrName);
                                data.getBaseAttributes().put(attr, value);
                            }
                        }

                        // Recalculate resources
                        data.recalculateResources();

                        // Restore HP/Mana percentages
                        data.setCurrentHP(data.getMaxHP() * hpPercent);
                        data.setCurrentMana(data.getMaxMana() * manaPercent);

                        plugin.getLogger().info("Updated " + playerClass + " stats for player " + data.getPlayerId());
                    }
                }
            }
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to update player " + data.getPlayerId() + " from config: " + e.getMessage());
        }
    }

    // Create a backup of all player data
    public void createBackup(String reason) {
        try {
            String timestamp = new java.text.SimpleDateFormat("yyyy-MM-dd_HH-mm-ss").format(new java.util.Date());
            File backupDir = new File(plugin.getDataFolder(), "playerdata_backups");
            if (!backupDir.exists()) {
                backupDir.mkdirs();
            }

            File specificBackupDir = new File(backupDir, "backup_" + timestamp + "_" + reason);
            specificBackupDir.mkdirs();

            // Copy all .dat files
            File[] playerFiles = dataFolder.listFiles((dir, name) -> name.endsWith(".dat"));
            if (playerFiles != null) {
                int copiedFiles = 0;
                for (File playerFile : playerFiles) {
                    File backupFile = new File(specificBackupDir, playerFile.getName());
                    java.nio.file.Files.copy(playerFile.toPath(), backupFile.toPath());
                    copiedFiles++;
                }

                plugin.getLogger().info("Created backup of " + copiedFiles + " player data files to: " + specificBackupDir.getName());
            }

        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to create player data backup: " + e.getMessage(), e);
        }
    }

    // Get statistics about player data storage
    public void logStorageStatistics() {
        File[] playerFiles = dataFolder.listFiles((dir, name) -> name.endsWith(".dat"));
        int fileCount = playerFiles != null ? playerFiles.length : 0;

        long totalSize = 0;
        if (playerFiles != null) {
            for (File file : playerFiles) {
                totalSize += file.length();
            }
        }

        plugin.getLogger().info("Player Data Storage Statistics:");
        plugin.getLogger().info("  - Total player files: " + fileCount);
        plugin.getLogger().info("  - Total storage size: " + (totalSize / 1024) + " KB");
        plugin.getLogger().info("  - Storage location: " + dataFolder.getAbsolutePath());
        plugin.getLogger().info("  - Cached players: " + playerDataCache.size());
    }
}
